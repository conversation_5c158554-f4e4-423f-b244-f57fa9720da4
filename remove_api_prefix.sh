#!/bin/bash

# 移除前端 API 調用中的 api/ 前綴，因為後端控制器現在已經有了 /api 前綴

API_FILE="/Users/<USER>/jsbvmx/frontend/frontend/src/services/api.ts"

# 備份原文件
cp "$API_FILE" "$API_FILE.backup3"

# 移除所有 api/ 前綴
sed -i '' "s|return apiClient\.get('api/|return apiClient.get('|g" "$API_FILE"
sed -i '' "s|return apiClient\.post('api/|return apiClient.post('|g" "$API_FILE"
sed -i '' "s|return apiClient\.put('api/|return apiClient.put('|g" "$API_FILE"
sed -i '' "s|return apiClient\.delete('api/|return apiClient.delete('|g" "$API_FILE"

sed -i '' "s|return apiClient\.get(\`api/|return apiClient.get(\`|g" "$API_FILE"
sed -i '' "s|return apiClient\.post(\`api/|return apiClient.post(\`|g" "$API_FILE"
sed -i '' "s|return apiClient\.put(\`api/|return apiClient.put(\`|g" "$API_FILE"
sed -i '' "s|return apiClient\.delete(\`api/|return apiClient.delete(\`|g" "$API_FILE"

echo "API 前綴移除完成！"
