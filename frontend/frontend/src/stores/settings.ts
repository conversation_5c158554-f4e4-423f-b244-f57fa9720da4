import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '../services/api'
import { ElMessage } from 'element-plus'

export interface Setting {
  id?: number
  key: string
  value: string
  category: 'SYSTEM' | 'CUSTOM'
  description?: string
  dataType: 'STRING' | 'NUMBER' | 'BOOLEAN' | 'JSON'
  isPublic: boolean
  createdAt?: string
  updatedAt?: string
}

export const useSettingsStore = defineStore('settings', () => {
  const settings = ref<Setting[]>([])
  const currentSetting = ref<Setting | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const getSettingById = computed(() => {
    return (id: number) => settings.value.find(setting => setting.id === id)
  })

  const getSettingByKey = computed(() => {
    return (key: string) => settings.value.find(setting => setting.key === key)
  })

  const getSettingsByCategory = computed(() => {
    return (category: string) => settings.value.filter(setting => setting.category === category)
  })

  const systemSettings = computed(() => {
    return settings.value.filter(setting => setting.category === 'SYSTEM')
  })

  const customSettings = computed(() => {
    return settings.value.filter(setting => setting.category === 'CUSTOM')
  })

  const publicSettings = computed(() => {
    return settings.value.filter(setting => setting.isPublic)
  })

  // Actions
  const fetchSettings = async () => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getSettings()
      settings.value = response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取設置失敗'
      ElMessage.error(error.value)
    } finally {
      loading.value = false
    }
  }

  const fetchSetting = async (id: number) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getSetting(id)
      currentSetting.value = response.data
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取設置失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchSettingByKey = async (key: string) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getSettingByKey(key)
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取設置失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchSystemSettings = async () => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getSystemSettings()
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取系統設置失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchCustomSettings = async () => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getCustomSettings()
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取自定義設置失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const createSetting = async (settingData: Omit<Setting, 'id' | 'createdAt' | 'updatedAt'>) => {
    loading.value = true
    error.value = null
    try {
      // Check if setting with same key already exists
      const existingSetting = settings.value.find(setting => setting.key === settingData.key)
      if (existingSetting) {
        throw new Error('設置鍵已存在')
      }

      const response = await api.createSetting(settingData)
      const newSetting = response.data
      settings.value.push(newSetting)
      ElMessage.success('設置創建成功')
      return newSetting
    } catch (err: any) {
      error.value = err.response?.data?.message || err.message || '創建設置失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateSetting = async (id: number, settingData: Partial<Setting>) => {
    loading.value = true
    error.value = null
    try {
      // Check if setting with same key already exists (excluding current setting)
      if (settingData.key) {
        const existingSetting = settings.value.find(setting => setting.key === settingData.key && setting.id !== id)
        if (existingSetting) {
          throw new Error('設置鍵已存在')
        }
      }

      const response = await api.updateSetting(id, settingData)
      const updatedSetting = response.data
      const index = settings.value.findIndex(setting => setting.id === id)
      if (index !== -1) {
        settings.value[index] = updatedSetting
      }
      if (currentSetting.value?.id === id) {
        currentSetting.value = updatedSetting
      }
      ElMessage.success('設置更新成功')
      return updatedSetting
    } catch (err: any) {
      error.value = err.response?.data?.message || err.message || '更新設置失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateSettingByKey = async (key: string, value: string) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.updateSettingByKey(key, { value })
      const updatedSetting = response.data
      const index = settings.value.findIndex(setting => setting.key === key)
      if (index !== -1) {
        settings.value[index] = updatedSetting
      }
      ElMessage.success('設置更新成功')
      return updatedSetting
    } catch (err: any) {
      error.value = err.response?.data?.message || '更新設置失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteSetting = async (id: number) => {
    loading.value = true
    error.value = null
    try {
      await api.deleteSetting(id)
      settings.value = settings.value.filter(setting => setting.id !== id)
      if (currentSetting.value?.id === id) {
        currentSetting.value = null
      }
      ElMessage.success('設置刪除成功')
    } catch (err: any) {
      error.value = err.response?.data?.message || '刪除設置失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  const clearCurrentSetting = () => {
    currentSetting.value = null
  }

  return {
    // State
    settings,
    currentSetting,
    loading,
    error,
    
    // Getters
    getSettingById,
    getSettingByKey,
    getSettingsByCategory,
    systemSettings,
    customSettings,
    publicSettings,
    
    // Actions
    fetchSettings,
    fetchSetting,
    fetchSettingByKey,
    fetchSystemSettings,
    fetchCustomSettings,
    createSetting,
    updateSetting,
    updateSettingByKey,
    deleteSetting,
    clearError,
    clearCurrentSetting
  }
})
