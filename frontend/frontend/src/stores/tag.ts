import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '../services/api'
import { ElMessage } from 'element-plus'

export interface Tag {
  id?: number
  name: string
  color?: string
  description?: string
  createdAt?: string
  updatedAt?: string
}

export const useTagStore = defineStore('tag', () => {
  const tags = ref<Tag[]>([])
  const currentTag = ref<Tag | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const getTagById = computed(() => {
    return (id: number) => tags.value.find(tag => tag.id === id)
  })

  const getTagByName = computed(() => {
    return (name: string) => tags.value.find(tag => tag.name === name)
  })

  const sortedTags = computed(() => {
    return [...tags.value].sort((a, b) => a.name.localeCompare(b.name))
  })

  // Actions
  const fetchTags = async () => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getTags()
      tags.value = response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取標籤失敗'
      ElMessage.error(error.value)
    } finally {
      loading.value = false
    }
  }

  const fetchTag = async (id: number) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getTag(id)
      currentTag.value = response.data
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取標籤失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchTagByName = async (name: string) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getTagByName(name)
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取標籤失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const createTag = async (tagData: Omit<Tag, 'id' | 'createdAt' | 'updatedAt'>) => {
    loading.value = true
    error.value = null
    try {
      // Check if tag with same name already exists
      const existingTag = tags.value.find(tag => tag.name === tagData.name)
      if (existingTag) {
        throw new Error('標籤名稱已存在')
      }

      const response = await api.createTag(tagData)
      const newTag = response.data
      tags.value.push(newTag)
      ElMessage.success('標籤創建成功')
      return newTag
    } catch (err: any) {
      error.value = err.response?.data?.message || err.message || '創建標籤失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateTag = async (id: number, tagData: Partial<Tag>) => {
    loading.value = true
    error.value = null
    try {
      // Check if tag with same name already exists (excluding current tag)
      if (tagData.name) {
        const existingTag = tags.value.find(tag => tag.name === tagData.name && tag.id !== id)
        if (existingTag) {
          throw new Error('標籤名稱已存在')
        }
      }

      const response = await api.updateTag(id, tagData)
      const updatedTag = response.data
      const index = tags.value.findIndex(tag => tag.id === id)
      if (index !== -1) {
        tags.value[index] = updatedTag
      }
      if (currentTag.value?.id === id) {
        currentTag.value = updatedTag
      }
      ElMessage.success('標籤更新成功')
      return updatedTag
    } catch (err: any) {
      error.value = err.response?.data?.message || err.message || '更新標籤失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteTag = async (id: number) => {
    loading.value = true
    error.value = null
    try {
      await api.deleteTag(id)
      tags.value = tags.value.filter(tag => tag.id !== id)
      if (currentTag.value?.id === id) {
        currentTag.value = null
      }
      ElMessage.success('標籤刪除成功')
    } catch (err: any) {
      error.value = err.response?.data?.message || '刪除標籤失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  const clearCurrentTag = () => {
    currentTag.value = null
  }

  return {
    // State
    tags,
    currentTag,
    loading,
    error,
    
    // Getters
    getTagById,
    getTagByName,
    sortedTags,
    
    // Actions
    fetchTags,
    fetchTag,
    fetchTagByName,
    createTag,
    updateTag,
    deleteTag,
    clearError,
    clearCurrentTag
  }
})
