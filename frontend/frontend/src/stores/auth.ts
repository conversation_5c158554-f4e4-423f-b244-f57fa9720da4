import { defineStore } from 'pinia'
import axios from 'axios'
import router from '../router'

const API_URL = 'http://localhost:8080/auth/'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: JSON.parse(localStorage.getItem('user') || 'null'),
    returnUrl: null as string | null
  }),
  getters: {
    isAuthenticated: (state) => !!state.user,
    token: (state) => state.user?.token,
    userId: (state) => state.user?.id,
    username: (state) => state.user?.username,
    roles: (state) => state.user?.roles || []
  },
  actions: {
    async login(username: string, password: string) {
      try {
        const response = await axios.post(API_URL + 'login', {
          username,
          password
        })

        const user = response.data

        // Store user details and jwt token in local storage to keep user logged in
        localStorage.setItem('user', JSON.stringify(user))
        this.user = user

        // Redirect to previous url or default to dashboard
        router.push(this.returnUrl || '/dashboard')

        return Promise.resolve(user)
      } catch (error) {
        return Promise.reject(error)
      }
    },

    logout() {
      localStorage.removeItem('user')
      this.user = null
      router.push('/login')
    },

    async register(username: string, firstName: string, lastName: string, email: string, password: string, roles: string[] = ['employee']) {
      try {
        const response = await axios.post(API_URL + 'register', {
          username,
          firstName,
          lastName,
          email,
          password,
          roles
        })

        return Promise.resolve(response.data)
      } catch (error) {
        return Promise.reject(error)
      }
    }
  }
})
