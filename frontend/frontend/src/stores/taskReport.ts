import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '../services/api'
import { ElMessage } from 'element-plus'

export interface TaskReport {
  id?: number
  task: {
    id: number
    name?: string
  }
  user: {
    id: number
    username?: string
    firstName?: string
    lastName?: string
  }
  content: string
  progressUpdate: number
  issues?: string
  reportDate: string
  createdAt?: string
  updatedAt?: string
}

export const useTaskReportStore = defineStore('taskReport', () => {
  const taskReports = ref<TaskReport[]>([])
  const currentTaskReport = ref<TaskReport | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const getTaskReportById = computed(() => {
    return (id: number) => taskReports.value.find(report => report.id === id)
  })

  const getTaskReportsByTask = computed(() => {
    return (taskId: number) => taskReports.value.filter(report => report.task.id === taskId)
  })

  const getTaskReportsByUser = computed(() => {
    return (userId: number) => taskReports.value.filter(report => report.user.id === userId)
  })

  // Actions
  const fetchTaskReports = async () => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getTaskReports()
      taskReports.value = response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取任務報告失敗'
      ElMessage.error(error.value)
    } finally {
      loading.value = false
    }
  }

  const fetchTaskReport = async (id: number) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getTaskReport(id)
      currentTaskReport.value = response.data
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取任務報告失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchTaskReportsByTask = async (taskId: number) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getTaskReportsByTask(taskId)
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取任務報告失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchMyTaskReports = async () => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getMyTaskReports()
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取我的任務報告失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const createTaskReport = async (taskReportData: Omit<TaskReport, 'id' | 'createdAt' | 'updatedAt'>) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.createTaskReport(taskReportData)
      const newTaskReport = response.data
      taskReports.value.push(newTaskReport)
      ElMessage.success('任務報告創建成功')
      return newTaskReport
    } catch (err: any) {
      error.value = err.response?.data?.message || '創建任務報告失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateTaskReport = async (id: number, taskReportData: Partial<TaskReport>) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.updateTaskReport(id, taskReportData)
      const updatedTaskReport = response.data
      const index = taskReports.value.findIndex(report => report.id === id)
      if (index !== -1) {
        taskReports.value[index] = updatedTaskReport
      }
      if (currentTaskReport.value?.id === id) {
        currentTaskReport.value = updatedTaskReport
      }
      ElMessage.success('任務報告更新成功')
      return updatedTaskReport
    } catch (err: any) {
      error.value = err.response?.data?.message || '更新任務報告失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteTaskReport = async (id: number) => {
    loading.value = true
    error.value = null
    try {
      await api.deleteTaskReport(id)
      taskReports.value = taskReports.value.filter(report => report.id !== id)
      if (currentTaskReport.value?.id === id) {
        currentTaskReport.value = null
      }
      ElMessage.success('任務報告刪除成功')
    } catch (err: any) {
      error.value = err.response?.data?.message || '刪除任務報告失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  const clearCurrentTaskReport = () => {
    currentTaskReport.value = null
  }

  return {
    // State
    taskReports,
    currentTaskReport,
    loading,
    error,
    
    // Getters
    getTaskReportById,
    getTaskReportsByTask,
    getTaskReportsByUser,
    
    // Actions
    fetchTaskReports,
    fetchTaskReport,
    fetchTaskReportsByTask,
    fetchMyTaskReports,
    createTaskReport,
    updateTaskReport,
    deleteTaskReport,
    clearError,
    clearCurrentTaskReport
  }
})
