import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '../services/api'
import { ElMessage } from 'element-plus'

export interface ContactRecord {
  id?: number
  customer: {
    id: number
    name?: string
    email?: string
    phone?: string
  }
  user: {
    id: number
    username?: string
    firstName?: string
    lastName?: string
  }
  contactType: 'PHONE' | 'EMAIL' | 'MEETING' | 'VISIT' | 'OTHER'
  subject: string
  content: string
  contactDate: string
  followUpDate?: string
  outcome?: string
  createdAt?: string
  updatedAt?: string
}

export const useContactRecordStore = defineStore('contactRecord', () => {
  const contactRecords = ref<ContactRecord[]>([])
  const currentContactRecord = ref<ContactRecord | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const getContactRecordById = computed(() => {
    return (id: number) => contactRecords.value.find(record => record.id === id)
  })

  const getContactRecordsByCustomer = computed(() => {
    return (customerId: number) => contactRecords.value.filter(record => record.customer.id === customerId)
  })

  const getContactRecordsByUser = computed(() => {
    return (userId: number) => contactRecords.value.filter(record => record.user.id === userId)
  })

  const getContactRecordsByType = computed(() => {
    return (type: string) => contactRecords.value.filter(record => record.contactType === type)
  })

  // Actions
  const fetchContactRecords = async () => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getContactRecords()
      contactRecords.value = response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取聯繫記錄失敗'
      ElMessage.error(error.value)
    } finally {
      loading.value = false
    }
  }

  const fetchContactRecord = async (id: number) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getContactRecord(id)
      currentContactRecord.value = response.data
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取聯繫記錄失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchContactRecordsByCustomer = async (customerId: number) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getContactRecordsByCustomer(customerId)
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取客戶聯繫記錄失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchContactRecordsByUser = async (userId: number) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getContactRecordsByUser(userId)
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取用戶聯繫記錄失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchContactRecordsByType = async (type: string) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getContactRecordsByType(type)
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取聯繫記錄失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchContactRecordsByDateRange = async (startDate: string, endDate: string) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getContactRecordsByDateRange(startDate, endDate)
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取聯繫記錄失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const createContactRecord = async (recordData: Omit<ContactRecord, 'id' | 'createdAt' | 'updatedAt'>) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.createContactRecord(recordData)
      const newRecord = response.data
      contactRecords.value.push(newRecord)
      ElMessage.success('聯繫記錄創建成功')
      return newRecord
    } catch (err: any) {
      error.value = err.response?.data?.message || '創建聯繫記錄失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateContactRecord = async (id: number, recordData: Partial<ContactRecord>) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.updateContactRecord(id, recordData)
      const updatedRecord = response.data
      const index = contactRecords.value.findIndex(record => record.id === id)
      if (index !== -1) {
        contactRecords.value[index] = updatedRecord
      }
      if (currentContactRecord.value?.id === id) {
        currentContactRecord.value = updatedRecord
      }
      ElMessage.success('聯繫記錄更新成功')
      return updatedRecord
    } catch (err: any) {
      error.value = err.response?.data?.message || '更新聯繫記錄失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteContactRecord = async (id: number) => {
    loading.value = true
    error.value = null
    try {
      await api.deleteContactRecord(id)
      contactRecords.value = contactRecords.value.filter(record => record.id !== id)
      if (currentContactRecord.value?.id === id) {
        currentContactRecord.value = null
      }
      ElMessage.success('聯繫記錄刪除成功')
    } catch (err: any) {
      error.value = err.response?.data?.message || '刪除聯繫記錄失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  const clearCurrentContactRecord = () => {
    currentContactRecord.value = null
  }

  return {
    // State
    contactRecords,
    currentContactRecord,
    loading,
    error,
    
    // Getters
    getContactRecordById,
    getContactRecordsByCustomer,
    getContactRecordsByUser,
    getContactRecordsByType,
    
    // Actions
    fetchContactRecords,
    fetchContactRecord,
    fetchContactRecordsByCustomer,
    fetchContactRecordsByUser,
    fetchContactRecordsByType,
    fetchContactRecordsByDateRange,
    createContactRecord,
    updateContactRecord,
    deleteContactRecord,
    clearError,
    clearCurrentContactRecord
  }
})
