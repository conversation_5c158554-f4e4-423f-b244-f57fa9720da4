import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '../services/api'
import { ElMessage } from 'element-plus'

export interface ActivityLog {
  id?: number
  user: {
    id: number
    username: string
    firstName: string
    lastName: string
    email: string
  }
  activityType: 'LOGIN' | 'LOGOUT' | 'CREATE' | 'UPDATE' | 'DELETE' | 'VIEW'
  description: string
  ipAddress?: string
  createdAt: string
}

export const useActivityLogStore = defineStore('activityLog', () => {
  const activityLogs = ref<ActivityLog[]>([])
  const currentActivityLog = ref<ActivityLog | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const getActivityLogById = computed(() => {
    return (id: number) => activityLogs.value.find(log => log.id === id)
  })

  const getActivityLogsByType = computed(() => {
    return (type: string) => activityLogs.value.filter(log => log.activityType === type)
  })

  const getActivityLogsByUser = computed(() => {
    return (userId: number) => activityLogs.value.filter(log => log.user.id === userId)
  })

  const sortedActivityLogs = computed(() => {
    return [...activityLogs.value].sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )
  })

  const loginLogs = computed(() => {
    return activityLogs.value.filter(log => log.activityType === 'LOGIN')
  })

  const recentLogs = computed(() => {
    return sortedActivityLogs.value.slice(0, 50) // 最近50條記錄
  })

  // Actions
  const fetchAllActivityLogs = async () => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getAllActivityLogs()
      activityLogs.value = response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取活動記錄失敗'
      ElMessage.error(error.value)
    } finally {
      loading.value = false
    }
  }

  const fetchActivityLogsByType = async (activityType: string) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getActivityLogsByType(activityType)
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取活動記錄失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchActivityLogsByDateRange = async (startDate: string, endDate: string) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getActivityLogsByDateRange(startDate, endDate)
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取活動記錄失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchActivityLogById = async (id: number) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getActivityLogById(id)
      currentActivityLog.value = response.data
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取活動記錄失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const searchActivityLogs = (searchTerm: string) => {
    if (!searchTerm) return sortedActivityLogs.value
    
    const term = searchTerm.toLowerCase()
    return sortedActivityLogs.value.filter(log => 
      log.description.toLowerCase().includes(term) ||
      log.user.username.toLowerCase().includes(term) ||
      log.user.firstName.toLowerCase().includes(term) ||
      log.user.lastName.toLowerCase().includes(term) ||
      log.activityType.toLowerCase().includes(term)
    )
  }

  const getActivityTypeStats = computed(() => {
    const stats = {
      LOGIN: 0,
      LOGOUT: 0,
      CREATE: 0,
      UPDATE: 0,
      DELETE: 0,
      VIEW: 0
    }
    
    activityLogs.value.forEach(log => {
      stats[log.activityType]++
    })
    
    return stats
  })

  const getUserActivityStats = computed(() => {
    const userStats: Record<string, number> = {}
    
    activityLogs.value.forEach(log => {
      const userKey = `${log.user.firstName} ${log.user.lastName}`
      userStats[userKey] = (userStats[userKey] || 0) + 1
    })
    
    return Object.entries(userStats)
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10) // 前10名活躍用戶
  })

  const clearError = () => {
    error.value = null
  }

  const clearCurrentActivityLog = () => {
    currentActivityLog.value = null
  }

  const clearActivityLogs = () => {
    activityLogs.value = []
  }

  return {
    // State
    activityLogs,
    currentActivityLog,
    loading,
    error,
    
    // Getters
    getActivityLogById,
    getActivityLogsByType,
    getActivityLogsByUser,
    sortedActivityLogs,
    loginLogs,
    recentLogs,
    getActivityTypeStats,
    getUserActivityStats,
    
    // Actions
    fetchAllActivityLogs,
    fetchActivityLogsByType,
    fetchActivityLogsByDateRange,
    fetchActivityLogById,
    searchActivityLogs,
    clearError,
    clearCurrentActivityLog,
    clearActivityLogs
  }
})
