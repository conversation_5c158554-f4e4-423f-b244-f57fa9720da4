import { defineS<PERSON> } from 'pinia'
import { ref, computed } from 'vue'
import api from '../services/api'
import { ElMessage } from 'element-plus'

export interface ResourceAllocation {
  id?: number
  resource: {
    id: number
    name?: string
    type?: string
  }
  project?: {
    id: number
    name?: string
  }
  task?: {
    id: number
    name?: string
  }
  allocatedQuantity: number
  startDate: string
  endDate: string
  notes?: string
  createdAt?: string
  updatedAt?: string
}

export const useResourceAllocationStore = defineStore('resourceAllocation', () => {
  const resourceAllocations = ref<ResourceAllocation[]>([])
  const currentResourceAllocation = ref<ResourceAllocation | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const getResourceAllocationById = computed(() => {
    return (id: number) => resourceAllocations.value.find(allocation => allocation.id === id)
  })

  const getResourceAllocationsByResource = computed(() => {
    return (resourceId: number) => resourceAllocations.value.filter(allocation => allocation.resource.id === resourceId)
  })

  const getResourceAllocationsByProject = computed(() => {
    return (projectId: number) => resourceAllocations.value.filter(allocation => allocation.project?.id === projectId)
  })

  const getResourceAllocationsByTask = computed(() => {
    return (taskId: number) => resourceAllocations.value.filter(allocation => allocation.task?.id === taskId)
  })

  // Actions
  const fetchResourceAllocations = async () => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getResourceAllocations()
      resourceAllocations.value = response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取資源分配失敗'
      ElMessage.error(error.value)
    } finally {
      loading.value = false
    }
  }

  const fetchResourceAllocation = async (id: number) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getResourceAllocation(id)
      currentResourceAllocation.value = response.data
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取資源分配失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchResourceAllocationsByResource = async (resourceId: number) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getResourceAllocationsByResource(resourceId)
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取資源分配失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchResourceAllocationsByProject = async (projectId: number) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getResourceAllocationsByProject(projectId)
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取專案資源分配失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchResourceAllocationsByTask = async (taskId: number) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getResourceAllocationsByTask(taskId)
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取任務資源分配失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchAvailableResourceAllocations = async (date: string) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.getAvailableResourceAllocations(date)
      return response.data
    } catch (err: any) {
      error.value = err.response?.data?.message || '獲取可用資源分配失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const createResourceAllocation = async (allocationData: Omit<ResourceAllocation, 'id' | 'createdAt' | 'updatedAt'>) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.createResourceAllocation(allocationData)
      const newAllocation = response.data
      resourceAllocations.value.push(newAllocation)
      ElMessage.success('資源分配創建成功')
      return newAllocation
    } catch (err: any) {
      error.value = err.response?.data?.message || '創建資源分配失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateResourceAllocation = async (id: number, allocationData: Partial<ResourceAllocation>) => {
    loading.value = true
    error.value = null
    try {
      const response = await api.updateResourceAllocation(id, allocationData)
      const updatedAllocation = response.data
      const index = resourceAllocations.value.findIndex(allocation => allocation.id === id)
      if (index !== -1) {
        resourceAllocations.value[index] = updatedAllocation
      }
      if (currentResourceAllocation.value?.id === id) {
        currentResourceAllocation.value = updatedAllocation
      }
      ElMessage.success('資源分配更新成功')
      return updatedAllocation
    } catch (err: any) {
      error.value = err.response?.data?.message || '更新資源分配失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteResourceAllocation = async (id: number) => {
    loading.value = true
    error.value = null
    try {
      await api.deleteResourceAllocation(id)
      resourceAllocations.value = resourceAllocations.value.filter(allocation => allocation.id !== id)
      if (currentResourceAllocation.value?.id === id) {
        currentResourceAllocation.value = null
      }
      ElMessage.success('資源分配刪除成功')
    } catch (err: any) {
      error.value = err.response?.data?.message || '刪除資源分配失敗'
      ElMessage.error(error.value)
      throw err
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  const clearCurrentResourceAllocation = () => {
    currentResourceAllocation.value = null
  }

  return {
    // State
    resourceAllocations,
    currentResourceAllocation,
    loading,
    error,
    
    // Getters
    getResourceAllocationById,
    getResourceAllocationsByResource,
    getResourceAllocationsByProject,
    getResourceAllocationsByTask,
    
    // Actions
    fetchResourceAllocations,
    fetchResourceAllocation,
    fetchResourceAllocationsByResource,
    fetchResourceAllocationsByProject,
    fetchResourceAllocationsByTask,
    fetchAvailableResourceAllocations,
    createResourceAllocation,
    updateResourceAllocation,
    deleteResourceAllocation,
    clearError,
    clearCurrentResourceAllocation
  }
})
