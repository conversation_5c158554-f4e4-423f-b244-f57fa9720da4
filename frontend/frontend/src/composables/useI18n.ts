import { ref, computed } from 'vue'
import zhTW from '../locales/zh-TW'

// 當前語言
const currentLocale = ref('zh-TW')

// 翻譯資源
const messages = {
  'zh-TW': zhTW
}

// 翻譯函數
export function useI18n() {
  const t = (key: string, params?: Record<string, any>): string => {
    const keys = key.split('.')
    let value: any = messages[currentLocale.value]
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k]
      } else {
        return key // 如果找不到翻譯，返回原始key
      }
    }
    
    if (typeof value !== 'string') {
      return key
    }
    
    // 簡單的參數替換
    if (params) {
      return value.replace(/\{(\w+)\}/g, (match, paramKey) => {
        return params[paramKey] || match
      })
    }
    
    return value
  }
  
  const locale = computed({
    get: () => currentLocale.value,
    set: (value: string) => {
      currentLocale.value = value
    }
  })
  
  return {
    t,
    locale
  }
}

// 專案狀態翻譯
export function useProjectStatus() {
  const { t } = useI18n()
  
  const getStatusLabel = (status: string): string => {
    const statusMap: Record<string, string> = {
      'PLANNING': t('status.planning'),
      'NOT_STARTED': t('status.notStarted'),
      'IN_PROGRESS': t('status.inProgress'),
      'COMPLETED': t('status.completed'),
      'DELAYED': t('status.delayed'),
      'CANCELLED': t('status.cancelled'),
      'CLOSED': t('status.closed'),
      'ON_HOLD': t('status.onHold')
    }
    
    return statusMap[status] || status
  }
  
  const getStatusType = (status: string): string => {
    const typeMap: Record<string, string> = {
      'PLANNING': 'info',
      'NOT_STARTED': 'info',
      'IN_PROGRESS': 'warning',
      'COMPLETED': 'success',
      'DELAYED': 'danger',
      'CANCELLED': 'info',
      'CLOSED': 'success',
      'ON_HOLD': 'warning'
    }
    
    return typeMap[status] || 'info'
  }
  
  return {
    getStatusLabel,
    getStatusType
  }
}

// 任務優先級翻譯
export function useTaskPriority() {
  const { t } = useI18n()
  
  const getPriorityLabel = (priority: string): string => {
    const priorityMap: Record<string, string> = {
      'URGENT': t('priority.urgent'),
      'HIGH': t('priority.high'),
      'MEDIUM': t('priority.medium'),
      'LOW': t('priority.low'),
      'NONE': t('priority.none')
    }
    
    return priorityMap[priority] || priority
  }
  
  const getPriorityType = (priority: string): string => {
    const typeMap: Record<string, string> = {
      'URGENT': 'danger',
      'HIGH': 'warning',
      'MEDIUM': 'primary',
      'LOW': 'info',
      'NONE': 'info'
    }
    
    return typeMap[priority] || 'info'
  }
  
  return {
    getPriorityLabel,
    getPriorityType
  }
}

// 進度狀態翻譯
export function useProgressStatus() {
  const { t } = useI18n()
  
  const getProgressStatusLabel = (status: string): string => {
    const statusMap: Record<string, string> = {
      'AHEAD': t('progress.ahead'),
      'ON_TRACK': t('progress.onTrack'),
      'BEHIND': t('progress.behind'),
      'OVERDUE': t('progress.overdue')
    }
    
    return statusMap[status] || status
  }
  
  const getProgressStatusType = (status: string): string => {
    const typeMap: Record<string, string> = {
      'AHEAD': 'success',
      'ON_TRACK': 'primary',
      'BEHIND': 'warning',
      'OVERDUE': 'danger'
    }
    
    return typeMap[status] || 'info'
  }
  
  return {
    getProgressStatusLabel,
    getProgressStatusType
  }
}

// 日期格式化
export function useDateFormat() {
  const formatDate = (date: string | Date, format: 'short' | 'long' | 'time' = 'short'): string => {
    if (!date) return '-'
    
    const d = typeof date === 'string' ? new Date(date) : date
    
    if (isNaN(d.getTime())) return '-'
    
    const options: Intl.DateTimeFormatOptions = {
      timeZone: 'Asia/Taipei'
    }
    
    switch (format) {
      case 'short':
        options.year = 'numeric'
        options.month = '2-digit'
        options.day = '2-digit'
        break
      case 'long':
        options.year = 'numeric'
        options.month = 'long'
        options.day = 'numeric'
        options.weekday = 'long'
        break
      case 'time':
        options.year = 'numeric'
        options.month = '2-digit'
        options.day = '2-digit'
        options.hour = '2-digit'
        options.minute = '2-digit'
        break
    }
    
    return d.toLocaleDateString('zh-TW', options)
  }
  
  const formatTime = (date: string | Date): string => {
    if (!date) return '-'
    
    const d = typeof date === 'string' ? new Date(date) : date
    
    if (isNaN(d.getTime())) return '-'
    
    return d.toLocaleString('zh-TW', {
      timeZone: 'Asia/Taipei',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
  
  const formatRelativeTime = (date: string | Date): string => {
    if (!date) return '-'
    
    const d = typeof date === 'string' ? new Date(date) : date
    const now = new Date()
    const diff = now.getTime() - d.getTime()
    
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    
    if (minutes < 1) {
      return '剛剛'
    } else if (minutes < 60) {
      return `${minutes}分鐘前`
    } else if (hours < 24) {
      return `${hours}小時前`
    } else if (days < 7) {
      return `${days}天前`
    } else {
      return formatDate(d)
    }
  }
  
  return {
    formatDate,
    formatTime,
    formatRelativeTime
  }
}

// 數字格式化
export function useNumberFormat() {
  const formatNumber = (num: number, decimals: number = 0): string => {
    return num.toLocaleString('zh-TW', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    })
  }
  
  const formatCurrency = (amount: number, currency: string = 'TWD'): string => {
    return amount.toLocaleString('zh-TW', {
      style: 'currency',
      currency: currency
    })
  }
  
  const formatPercentage = (value: number, decimals: number = 1): string => {
    return `${(value).toFixed(decimals)}%`
  }
  
  return {
    formatNumber,
    formatCurrency,
    formatPercentage
  }
}

export default useI18n
