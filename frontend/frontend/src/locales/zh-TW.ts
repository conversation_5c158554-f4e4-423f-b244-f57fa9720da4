export default {
  // 通用
  common: {
    save: '保存',
    cancel: '取消',
    delete: '刪除',
    edit: '編輯',
    add: '新增',
    create: '創建',
    update: '更新',
    search: '搜尋',
    filter: '篩選',
    refresh: '刷新',
    loading: '載入中...',
    noData: '暫無數據',
    confirm: '確認',
    back: '返回',
    next: '下一步',
    previous: '上一步',
    submit: '提交',
    reset: '重置',
    clear: '清除',
    close: '關閉',
    view: '查看',
    download: '下載',
    upload: '上傳',
    export: '匯出',
    import: '匯入',
    print: '列印',
    copy: '複製',
    cut: '剪切',
    paste: '貼上',
    select: '選擇',
    selectAll: '全選',
    deselect: '取消選擇',
    expand: '展開',
    collapse: '收起',
    more: '更多',
    less: '收起',
    settings: '設定',
    help: '幫助',
    about: '關於',
    version: '版本',
    language: '語言',
    theme: '主題',
    logout: '登出',
    login: '登入',
    register: '註冊',
    profile: '個人資料',
    dashboard: '儀表板',
    home: '首頁'
  },

  // 專案管理
  project: {
    title: '專案管理',
    list: '專案列表',
    detail: '專案詳情',
    create: '新增專案',
    edit: '編輯專案',
    delete: '刪除專案',
    name: '專案名稱',
    description: '專案描述',
    status: '專案狀態',
    progress: '專案進度',
    startDate: '開始日期',
    endDate: '結束日期',
    budget: '預算',
    cost: '成本',
    manager: '專案經理',
    customer: '客戶',
    members: '專案成員',
    tasks: '任務',
    documents: '文件',
    resources: '資源',
    timeline: '時間軸',
    gantt: '甘特圖',
    overview: '概覽',
    automation: '自動化',
    progressAutomation: '進度自動化',
    totalProjects: '總專案數',
    activeProjects: '進行中專案',
    completedProjects: '已完成專案',
    delayedProjects: '延期專案'
  },

  // 任務管理
  task: {
    title: '任務管理',
    list: '任務列表',
    detail: '任務詳情',
    create: '新增任務',
    edit: '編輯任務',
    delete: '刪除任務',
    name: '任務名稱',
    description: '任務描述',
    status: '任務狀態',
    progress: '任務進度',
    priority: '優先級',
    assignee: '負責人',
    startDate: '開始日期',
    dueDate: '截止日期',
    parent: '父任務',
    subtasks: '子任務',
    dependencies: '依賴關係',
    reports: '任務報告',
    comments: '評論',
    attachments: '附件',
    timeTracking: '時間追蹤',
    totalTasks: '總任務數',
    completedTasks: '已完成任務',
    overdueTasks: '逾期任務',
    todayTasks: '今日任務',
    myTasks: '我的任務',
    assignedTasks: '指派給我的任務',
    createdTasks: '我創建的任務'
  },

  // 狀態
  status: {
    planning: '規劃中',
    notStarted: '未開始',
    inProgress: '進行中',
    completed: '已完成',
    delayed: '延期',
    cancelled: '已取消',
    closed: '已結案',
    onHold: '暫停',
    review: '審核中',
    approved: '已批准',
    rejected: '已拒絕'
  },

  // 優先級
  priority: {
    urgent: '緊急',
    high: '高',
    medium: '中',
    low: '低',
    none: '無'
  },

  // 進度相關
  progress: {
    dashboard: '進度儀表板',
    overview: '進度概覽',
    analysis: '進度分析',
    tracking: '進度追蹤',
    automation: '進度自動化',
    calculation: '進度計算',
    timeBasedProgress: '時間進度',
    actualProgress: '實際進度',
    progressDifference: '進度差異',
    progressStatus: '進度狀態',
    ahead: '超前',
    onTrack: '正常',
    behind: '落後',
    overdue: '逾期',
    completion: '完成率',
    averageCompletion: '平均完成率',
    onTimeCompletion: '按時完成率',
    resourceUtilization: '資源利用率',
    teamEfficiency: '團隊效率',
    progressAlerts: '進度警報',
    progressSummary: '進度總覽',
    progressDetails: '進度詳情',
    progressUpdate: '進度更新',
    progressHistory: '進度歷史',
    autoCalculated: '自動計算',
    manualUpdate: '手動更新',
    recalculate: '重新計算',
    lastUpdated: '最後更新',
    lastCalculated: '最後計算'
  },

  // 用戶管理
  user: {
    title: '用戶管理',
    list: '用戶列表',
    detail: '用戶詳情',
    create: '新增用戶',
    edit: '編輯用戶',
    delete: '刪除用戶',
    username: '用戶名',
    email: '電子郵件',
    firstName: '名字',
    lastName: '姓氏',
    fullName: '全名',
    phone: '電話',
    department: '部門',
    role: '角色',
    status: '狀態',
    enabled: '啟用',
    disabled: '停用',
    lastLogin: '最後登入',
    createdAt: '創建時間',
    updatedAt: '更新時間'
  },

  // 客戶管理
  customer: {
    title: '客戶管理',
    list: '客戶列表',
    detail: '客戶詳情',
    create: '新增客戶',
    edit: '編輯客戶',
    delete: '刪除客戶',
    name: '客戶名稱',
    type: '客戶類型',
    contactPerson: '聯絡人',
    email: '電子郵件',
    phone: '電話',
    address: '地址',
    projects: '相關專案',
    contracts: '合約'
  },

  // 資源管理
  resource: {
    title: '資源管理',
    list: '資源列表',
    detail: '資源詳情',
    create: '新增資源',
    edit: '編輯資源',
    delete: '刪除資源',
    allocation: '資源分配',
    availability: '資源可用性',
    utilization: '資源利用率',
    name: '資源名稱',
    type: '資源類型',
    category: '資源類別',
    quantity: '數量',
    unit: '單位',
    cost: '成本',
    supplier: '供應商',
    location: '位置',
    status: '狀態',
    available: '可用',
    allocated: '已分配',
    unavailable: '不可用',
    maintenance: '維護中'
  },

  // 報告
  report: {
    title: '報告',
    list: '報告列表',
    detail: '報告詳情',
    create: '新增報告',
    edit: '編輯報告',
    delete: '刪除報告',
    generate: '生成報告',
    export: '匯出報告',
    print: '列印報告',
    schedule: '定時報告',
    template: '報告模板',
    type: '報告類型',
    period: '報告期間',
    format: '報告格式',
    daily: '日報',
    weekly: '週報',
    monthly: '月報',
    quarterly: '季報',
    yearly: '年報',
    custom: '自定義'
  },

  // 通知
  notification: {
    title: '通知',
    list: '通知列表',
    detail: '通知詳情',
    create: '新增通知',
    edit: '編輯通知',
    delete: '刪除通知',
    send: '發送通知',
    read: '已讀',
    unread: '未讀',
    markAsRead: '標記為已讀',
    markAsUnread: '標記為未讀',
    markAllAsRead: '全部標記為已讀',
    type: '通知類型',
    message: '通知內容',
    recipient: '接收者',
    sender: '發送者',
    sentAt: '發送時間',
    readAt: '閱讀時間'
  },

  // 設定
  settings: {
    title: '系統設定',
    general: '一般設定',
    security: '安全設定',
    notification: '通知設定',
    appearance: '外觀設定',
    language: '語言設定',
    timezone: '時區設定',
    dateFormat: '日期格式',
    timeFormat: '時間格式',
    currency: '貨幣設定',
    backup: '備份設定',
    maintenance: '維護模式',
    logs: '系統日誌',
    audit: '審計日誌',
    performance: '性能監控'
  },

  // 錯誤訊息
  error: {
    general: '發生錯誤，請稍後再試',
    network: '網路連接錯誤',
    server: '伺服器錯誤',
    notFound: '找不到請求的資源',
    unauthorized: '未授權訪問',
    forbidden: '禁止訪問',
    validation: '數據驗證失敗',
    required: '此欄位為必填',
    invalid: '數據格式無效',
    tooLong: '輸入內容過長',
    tooShort: '輸入內容過短',
    duplicate: '數據重複',
    notExists: '數據不存在',
    expired: '會話已過期，請重新登入',
    maintenance: '系統維護中，請稍後再試'
  },

  // 成功訊息
  success: {
    created: '創建成功',
    updated: '更新成功',
    deleted: '刪除成功',
    saved: '保存成功',
    sent: '發送成功',
    uploaded: '上傳成功',
    downloaded: '下載成功',
    exported: '匯出成功',
    imported: '匯入成功',
    copied: '複製成功',
    login: '登入成功',
    logout: '登出成功',
    registered: '註冊成功',
    verified: '驗證成功',
    reset: '重置成功',
    synchronized: '同步成功',
    backup: '備份成功',
    restore: '還原成功'
  },

  // 確認訊息
  confirm: {
    delete: '確定要刪除嗎？',
    save: '確定要保存嗎？',
    cancel: '確定要取消嗎？',
    logout: '確定要登出嗎？',
    reset: '確定要重置嗎？',
    clear: '確定要清除嗎？',
    submit: '確定要提交嗎？',
    approve: '確定要批准嗎？',
    reject: '確定要拒絕嗎？',
    close: '確定要關閉嗎？',
    archive: '確定要歸檔嗎？',
    restore: '確定要還原嗎？'
  }
}
