import axios from 'axios'
import { useAuthStore } from '../stores/auth'

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8080/'

const apiClient = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Add a request interceptor
apiClient.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    const token = authStore.token

    if (token) {
      config.headers['Authorization'] = 'Bearer ' + token
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Add a response interceptor
apiClient.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    const authStore = useAuthStore()

    // If 401 Unauthorized, log out user
    if (error.response && error.response.status === 401) {
      authStore.logout()
    }

    return Promise.reject(error)
  }
)

export default {
  // User endpoints
  getUsers() {
    return apiClient.get('api/users')
  },
  getUser(id: number) {
    return apiClient.get(`api/users/${id}`)
  },
  createUser(data: any) {
    return apiClient.post('api/users', data)
  },
  updateUser(id: number, data: any) {
    return apiClient.put(`api/users/${id}`, data)
  },
  deleteUser(id: number) {
    return apiClient.delete(`api/users/${id}`)
  },
  searchUsers(query: string) {
    return apiClient.get(`api/users/search?q=${encodeURIComponent(query)}`)
  },
  enableUser(id: number) {
    return apiClient.put(`api/users/${id}/enable`)
  },
  disableUser(id: number) {
    return apiClient.put(`api/users/${id}/disable`)
  },
  batchEnableUsers(userIds: number[]) {
    return apiClient.post('api/users/batch-enable', userIds)
  },
  batchDisableUsers(userIds: number[]) {
    return apiClient.post('api/users/batch-disable', userIds)
  },
  getUserStatistics() {
    return apiClient.get('api/users/statistics')
  },
  getUserActivityLogs(id: number) {
    return apiClient.get(`api/users/${id}/activity-logs`)
  },
  getUserProjects(id: number) {
    return apiClient.get(`api/users/${id}/projects`)
  },

  // 文件管理 API
  getFiles() {
    return apiClient.get('api/files')
  },
  uploadFile(formData: FormData) {
    return apiClient.post('api/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  downloadFile(id: number) {
    return apiClient.get(`api/files/${id}/download`, {
      responseType: 'blob'
    })
  },
  deleteFile(id: number) {
    return apiClient.delete(`api/files/${id}`)
  },
  batchDeleteFiles(ids: number[]) {
    return apiClient.post('api/files/batch-delete', ids)
  },
  shareFile(shareData: any) {
    return apiClient.post('api/files/share', shareData)
  },
  getFileContent(id: number) {
    return apiClient.get(`api/files/${id}/content`)
  },
  getFileVersions(id: number) {
    return apiClient.get(`api/files/${id}/versions`)
  },
  uploadFileVersion(id: number, formData: FormData) {
    return apiClient.post(`api/files/${id}/versions`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  downloadFileVersion(versionId: number) {
    return apiClient.get(`api/files/versions/${versionId}/download`, {
      responseType: 'blob'
    })
  },
  restoreFileVersion(fileId: number, versionId: number) {
    return apiClient.post(`api/files/${fileId}/versions/${versionId}/restore`)
  },
  deleteFileVersion(versionId: number) {
    return apiClient.delete(`api/files/versions/${versionId}`)
  },

  // Department endpoints
  getDepartments() {
    return apiClient.get('api/departments')
  },
  getActiveDepartments() {
    return apiClient.get('api/departments/active')
  },
  getRootDepartments() {
    return apiClient.get('api/departments/root')
  },
  getDepartment(id: number) {
    return apiClient.get(`api/departments/${id}`)
  },
  getChildDepartments(id: number) {
    return apiClient.get(`api/departments/${id}/children`)
  },
  getDepartmentUsers(id: number) {
    return apiClient.get(`api/departments/${id}/users`)
  },
  createDepartment(data: any) {
    return apiClient.post('api/departments', data)
  },
  updateDepartment(id: number, data: any) {
    return apiClient.put(`api/departments/${id}`, data)
  },
  activateDepartment(id: number) {
    return apiClient.put(`api/departments/${id}/activate`)
  },
  deactivateDepartment(id: number) {
    return apiClient.put(`api/departments/${id}/deactivate`)
  },
  deleteDepartment(id: number) {
    return apiClient.delete(`api/departments/${id}`)
  },

  // Customer endpoints
  getCustomers() {
    return apiClient.get('api/customers')
  },
  searchCustomers(name: string) {
    return apiClient.get(`api/customers/search?name=${name}`)
  },
  getCustomersByType(typeId: number) {
    return apiClient.get(`api/customers/type/${typeId}`)
  },
  getCustomersByTag(tagId: number) {
    return apiClient.get(`api/customers/tag/${tagId}`)
  },
  getCustomer(id: number) {
    return apiClient.get(`api/customers/${id}`)
  },
  getCustomerContacts(id: number) {
    return apiClient.get(`api/customers/${id}/contacts`)
  },
  getCustomerProjects(id: number) {
    return apiClient.get(`api/customers/${id}/projects`)
  },
  createCustomer(data: any) {
    return apiClient.post('customers', data)
  },
  updateCustomer(id: number, data: any) {
    return apiClient.put(`api/customers/${id}`, data)
  },
  deleteCustomer(id: number) {
    return apiClient.delete(`api/customers/${id}`)
  },

  // Customer Type endpoints
  getCustomerTypes() {
    return apiClient.get('api/customer-types')
  },
  getCustomerType(id: number) {
    return apiClient.get(`api/customer-types/${id}`)
  },
  createCustomerType(data: any) {
    return apiClient.post('api/customer-types', data)
  },
  updateCustomerType(id: number, data: any) {
    return apiClient.put(`api/customer-types/${id}`, data)
  },
  deleteCustomerType(id: number) {
    return apiClient.delete(`api/customer-types/${id}`)
  },

  // Project endpoints
  getProjects() {
    return apiClient.get('projects')
  },
  getProjectsByStatus(status: string) {
    return apiClient.get(`api/projects/status/${status}`)
  },
  getProjectsByManager(managerId: number) {
    return apiClient.get(`api/projects/manager/${managerId}`)
  },
  getProjectsByMember(memberId: number) {
    return apiClient.get(`api/projects/member/${memberId}`)
  },
  getProjectsByCustomer(customerId: number) {
    return apiClient.get(`api/projects/customer/${customerId}`)
  },
  getProjectsByDateRange(startDate: string, endDate: string) {
    return apiClient.get(`api/projects/date-range?startDate=${startDate}&endDate=${endDate}`)
  },
  searchProjects(name: string) {
    return apiClient.get(`api/projects/search?name=${name}`)
  },
  getProject(id: number) {
    return apiClient.get(`api/projects/${id}`)
  },
  getProjectTasks(id: number) {
    return apiClient.get(`api/projects/${id}/tasks`)
  },
  getProjectDocuments(id: number) {
    return apiClient.get(`api/projects/${id}/documents`)
  },
  getProjectResources(id: number) {
    return apiClient.get(`api/projects/${id}/resources`)
  },
  getProjectResourceAllocations(id: number) {
    return apiClient.get(`api/resource-allocations/project/${id}`)
  },
  createProject(data: any) {
    return apiClient.post('projects', data)
  },
  updateProject(id: number, data: any) {
    return apiClient.put(`api/projects/${id}`, data)
  },
  updateProjectStatus(id: number, status: string) {
    return apiClient.put(`api/projects/${id}/status`, { status })
  },
  updateProjectProgress(id: number, progress: number) {
    return apiClient.put(`api/projects/${id}/progress`, { progress })
  },
  getProjectProgressDetails(id: number) {
    return apiClient.get(`api/projects/${id}/progress-details`)
  },
  recalculateProjectProgress(id: number) {
    return apiClient.post(`api/projects/${id}/recalculate-progress`)
  },
  deleteProject(id: number) {
    return apiClient.delete(`api/projects/${id}`)
  },
  getProjectMembers(id: number) {
    return apiClient.get(`api/projects/${id}/members`)
  },
  addProjectMember(id: number, userId: number) {
    return apiClient.post(`api/projects/${id}/members`, { userId })
  },
  removeProjectMember(id: number, userId: number) {
    return apiClient.delete(`api/projects/${id}/members/${userId}`)
  },

  // Resource endpoints
  getResources() {
    return apiClient.get('resources')
  },
  getResource(id: number) {
    return apiClient.get(`api/resources/${id}`)
  },
  getResourcesByType(type: string) {
    return apiClient.get(`api/resources/type/${type}`)
  },
  getAvailableResources() {
    return apiClient.get('resources/available')
  },
  searchResources(name: string) {
    return apiClient.get(`api/resources/search?name=${name}`)
  },
  getResourceAllocationsByResourceId(id: number) {
    return apiClient.get(`api/resources/${id}/allocations`)
  },
  createResource(data: any) {
    return apiClient.post('resources', data)
  },
  updateResource(id: number, data: any) {
    return apiClient.put(`api/resources/${id}`, data)
  },
  deleteResource(id: number) {
    return apiClient.delete(`api/resources/${id}`)
  },

  // Task endpoints
  getTasks() {
    return apiClient.get('tasks')
  },
  getTasksByProject(projectId: number) {
    return apiClient.get(`api/tasks/project/${projectId}`)
  },
  getTasksByAssignee(assigneeId: number) {
    return apiClient.get(`api/tasks/assignee/${assigneeId}`)
  },
  getTasksByStatus(status: string) {
    return apiClient.get(`api/tasks/status/${status}`)
  },
  getTasksByPriority(priority: string) {
    return apiClient.get(`api/tasks/priority/${priority}`)
  },
  getTasksDueBefore(date: string) {
    return apiClient.get(`api/tasks/due-before?date=${date}`)
  },
  getTasksDueBetween(startDate: string, endDate: string) {
    return apiClient.get(`api/tasks/due-between?startDate=${startDate}&endDate=${endDate}`)
  },
  getSubtasks(parentId: number) {
    return apiClient.get(`api/tasks/parent/${parentId}`)
  },
  getRootTasks() {
    return apiClient.get('tasks/root')
  },
  getTask(id: number) {
    return apiClient.get(`api/tasks/${id}`)
  },
  getTaskReportsByTaskId(id: number) {
    return apiClient.get(`api/tasks/${id}/reports`)
  },
  getTaskDocuments(id: number) {
    return apiClient.get(`api/tasks/${id}/documents`)
  },
  createTask(data: any) {
    return apiClient.post('tasks', data)
  },
  updateTask(id: number, data: any) {
    return apiClient.put(`api/tasks/${id}`, data)
  },
  updateTaskStatus(id: number, status: string) {
    return apiClient.put(`api/tasks/${id}/status`, { status })
  },
  updateTaskProgress(id: number, progress: number) {
    return apiClient.put(`api/tasks/${id}/progress`, { progress })
  },
  getTimeBasedProgress(id: number) {
    return apiClient.get(`api/tasks/${id}/time-based-progress`)
  },
  getTaskProgressAnalysis(id: number) {
    return apiClient.get(`api/tasks/${id}/progress-analysis`)
  },
  calculateTaskProgress(id: number) {
    return apiClient.post(`api/tasks/${id}/calculate-progress`)
  },
  deleteTask(id: number) {
    return apiClient.delete(`api/tasks/${id}`)
  },

  // Dashboard endpoints
  getDashboardSummary() {
    return apiClient.get('api/dashboard/summary')
  },
  getProjectsDashboard() {
    return apiClient.get('api/dashboard/projects')
  },
  getTasksDashboard() {
    return apiClient.get('api/dashboard/tasks')
  },

  // Notification endpoints
  getMyNotifications() {
    return apiClient.get('notifications/my')
  },
  getMyUnreadNotifications() {
    return apiClient.get('notifications/my/unread')
  },
  markNotificationAsRead(id: number) {
    return apiClient.put(`api/notifications/${id}/read`)
  },
  markAllNotificationsAsRead() {
    return apiClient.put('notifications/read-all')
  },
  deleteNotification(id: number) {
    return apiClient.delete(`api/notifications/${id}`)
  },

  // Task Report endpoints
  getTaskReports() {
    return apiClient.get('api/task-reports')
  },
  getTaskReport(id: number) {
    return apiClient.get(`api/task-reports/${id}`)
  },
  getTaskReportsByTask(taskId: number) {
    return apiClient.get(`api/task-reports/task/${taskId}`)
  },
  getTaskReportsByUser(userId: number) {
    return apiClient.get(`api/task-reports/user/${userId}`)
  },
  getTaskReportsByDateRange(startDate: string, endDate: string) {
    return apiClient.get(`api/task-reports/date-range?startDate=${startDate}&endDate=${endDate}`)
  },
  getMyTaskReports() {
    return apiClient.get('api/task-reports/my')
  },
  createTaskReport(data: any) {
    return apiClient.post('api/task-reports', data)
  },
  updateTaskReport(id: number, data: any) {
    return apiClient.put(`api/task-reports/${id}`, data)
  },
  deleteTaskReport(id: number) {
    return apiClient.delete(`api/task-reports/${id}`)
  },

  // Resource Allocation endpoints
  getResourceAllocations() {
    return apiClient.get('api/resource-allocations')
  },
  getResourceAllocation(id: number) {
    return apiClient.get(`api/resource-allocations/${id}`)
  },
  getResourceAllocationsByResource(resourceId: number) {
    return apiClient.get(`api/resource-allocations/resource/${resourceId}`)
  },
  getResourceAllocationsByProject(projectId: number) {
    return apiClient.get(`api/resource-allocations/project/${projectId}`)
  },
  getResourceAllocationsByTask(taskId: number) {
    return apiClient.get(`api/resource-allocations/task/${taskId}`)
  },
  getResourceAllocationsByDateRange(startDate: string, endDate: string) {
    return apiClient.get(`api/resource-allocations/date-range?startDate=${startDate}&endDate=${endDate}`)
  },
  getAvailableResourceAllocations(date: string) {
    return apiClient.get(`api/resource-allocations/available?date=${date}`)
  },
  createResourceAllocation(data: any) {
    return apiClient.post('api/resource-allocations', data)
  },
  updateResourceAllocation(id: number, data: any) {
    return apiClient.put(`api/resource-allocations/${id}`, data)
  },
  deleteResourceAllocation(id: number) {
    return apiClient.delete(`api/resource-allocations/${id}`)
  },

  // Resource Availability endpoints
  checkResourceAvailability(params: {
    resourceId: string | number
    startDate: string
    endDate: string
    requiredQuantity: number
  }) {
    return apiClient.get('api/resource-availability/check', { params })
  },
  checkProjectResourceRequirements(projectId: number) {
    return apiClient.get(`api/resource-availability/project/${projectId}/check`)
  },
  checkTaskResourceRequirements(taskId: number) {
    return apiClient.get(`api/resource-availability/task/${taskId}/check`)
  },
  getResourceUsageStatistics(resourceId: string | number, startDate: string, endDate: string) {
    return apiClient.get(`api/resource-availability/statistics/${resourceId}`, {
      params: { startDate, endDate }
    })
  },
  batchCheckProjectsResourceRequirements(projectIds: number[]) {
    return apiClient.post('api/resource-availability/projects/batch-check', projectIds)
  },
  batchCheckTasksResourceRequirements(taskIds: number[]) {
    return apiClient.post('api/resource-availability/tasks/batch-check', taskIds)
  },
  getResourceAllocationsInDateRange(resourceId: number, startDate: string, endDate: string) {
    return apiClient.get(`api/resource-availability/resource/${resourceId}/allocations`, {
      params: { startDate, endDate }
    })
  },

  // Contact Record endpoints
  getContactRecords() {
    return apiClient.get('api/contact-records')
  },
  getContactRecord(id: number) {
    return apiClient.get(`api/contact-records/${id}`)
  },
  getContactRecordsByCustomer(customerId: number) {
    return apiClient.get(`api/contact-records/customer/${customerId}`)
  },
  getContactRecordsByUser(userId: number) {
    return apiClient.get(`api/contact-records/user/${userId}`)
  },
  getContactRecordsByType(type: string) {
    return apiClient.get(`api/contact-records/type/${type}`)
  },
  getContactRecordsByDateRange(startDate: string, endDate: string) {
    return apiClient.get(`api/contact-records/date-range?startDate=${startDate}&endDate=${endDate}`)
  },
  createContactRecord(data: any) {
    return apiClient.post('api/contact-records', data)
  },
  updateContactRecord(id: number, data: any) {
    return apiClient.put(`api/contact-records/${id}`, data)
  },
  deleteContactRecord(id: number) {
    return apiClient.delete(`api/contact-records/${id}`)
  },

  // Tag endpoints
  getTags() {
    return apiClient.get('api/tags')
  },
  getTag(id: number) {
    return apiClient.get(`api/tags/${id}`)
  },
  getTagByName(name: string) {
    return apiClient.get(`api/tags/name/${name}`)
  },
  createTag(data: any) {
    return apiClient.post('api/tags', data)
  },
  updateTag(id: number, data: any) {
    return apiClient.put(`api/tags/${id}`, data)
  },
  deleteTag(id: number) {
    return apiClient.delete(`api/tags/${id}`)
  },

  // Activity Log endpoints
  getAllActivityLogs() {
    return apiClient.get('api/activity-logs')
  },
  getActivityLogsByType(activityType: string) {
    return apiClient.get(`api/activity-logs/type/${activityType}`)
  },
  getActivityLogsByDateRange(startDate: string, endDate: string) {
    return apiClient.get(`api/activity-logs/date-range?startDate=${startDate}&endDate=${endDate}`)
  },
  getActivityLogById(id: number) {
    return apiClient.get(`api/activity-logs/${id}`)
  },

  // Settings endpoints
  getSettings() {
    return apiClient.get('settings')
  },
  getSetting(id: number) {
    return apiClient.get(`api/settings/${id}`)
  },
  getSettingByKey(key: string) {
    return apiClient.get(`api/settings/key/${key}`)
  },
  getSettingsByCategory(category: string) {
    return apiClient.get(`api/settings/category/${category}`)
  },
  getSystemSettings() {
    return apiClient.get('settings/system')
  },
  getCustomSettings() {
    return apiClient.get('settings/custom')
  },
  createSetting(data: any) {
    return apiClient.post('settings', data)
  },
  updateSetting(id: number, data: any) {
    return apiClient.put(`api/settings/${id}`, data)
  },
  updateSettingByKey(key: string, data: any) {
    return apiClient.put(`api/settings/key/${key}`, data)
  },
  deleteSetting(id: number) {
    return apiClient.delete(`settings/${id}`)
  }
}
