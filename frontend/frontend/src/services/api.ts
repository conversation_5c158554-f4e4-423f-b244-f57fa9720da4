import axios from 'axios'
import { useAuthStore } from '../stores/auth'

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8080/api/'

const apiClient = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Add a request interceptor
apiClient.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    const token = authStore.token

    if (token) {
      config.headers['Authorization'] = 'Bearer ' + token
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Add a response interceptor
apiClient.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    const authStore = useAuthStore()

    // If 401 Unauthorized, log out user
    if (error.response && error.response.status === 401) {
      authStore.logout()
    }

    return Promise.reject(error)
  }
)

export default {
  // User endpoints
  getUsers() {
    return apiClient.get('users')
  },
  getUser(id: number) {
    return apiClient.get(`users/${id}`)
  },
  createUser(data: any) {
    return apiClient.post('users', data)
  },
  updateUser(id: number, data: any) {
    return apiClient.put(`users/${id}`, data)
  },
  deleteUser(id: number) {
    return apiClient.delete(`users/${id}`)
  },
  searchUsers(query: string) {
    return apiClient.get(`users/search?q=${encodeURIComponent(query)}`)
  },
  enableUser(id: number) {
    return apiClient.put(`users/${id}/enable`)
  },
  disableUser(id: number) {
    return apiClient.put(`users/${id}/disable`)
  },
  batchEnableUsers(userIds: number[]) {
    return apiClient.post('users/batch-enable', userIds)
  },
  batchDisableUsers(userIds: number[]) {
    return apiClient.post('users/batch-disable', userIds)
  },
  getUserStatistics() {
    return apiClient.get('users/statistics')
  },
  getUserActivityLogs(id: number) {
    return apiClient.get(`users/${id}/activity-logs`)
  },
  getUserProjects(id: number) {
    return apiClient.get(`users/${id}/projects`)
  },
  updatePassword(id: number, data: any) {
    return apiClient.put(`users/${id}/password`, data)
  },
  getUserStatistics() {
    return apiClient.get('users/statistics')
  },
  batchEnableUsers(userIds: number[]) {
    return apiClient.post('users/batch-enable', userIds)
  },
  batchDisableUsers(userIds: number[]) {
    return apiClient.post('users/batch-disable', userIds)
  },

  // Department endpoints
  getDepartments() {
    return apiClient.get('departments')
  },
  getActiveDepartments() {
    return apiClient.get('departments/active')
  },
  getRootDepartments() {
    return apiClient.get('departments/root')
  },
  getDepartment(id: number) {
    return apiClient.get(`departments/${id}`)
  },
  getChildDepartments(id: number) {
    return apiClient.get(`departments/${id}/children`)
  },
  getDepartmentUsers(id: number) {
    return apiClient.get(`departments/${id}/users`)
  },
  createDepartment(data: any) {
    return apiClient.post('departments', data)
  },
  updateDepartment(id: number, data: any) {
    return apiClient.put(`departments/${id}`, data)
  },
  activateDepartment(id: number) {
    return apiClient.put(`departments/${id}/activate`)
  },
  deactivateDepartment(id: number) {
    return apiClient.put(`departments/${id}/deactivate`)
  },
  deleteDepartment(id: number) {
    return apiClient.delete(`departments/${id}`)
  },

  // Customer endpoints
  getCustomers() {
    return apiClient.get('customers')
  },
  searchCustomers(name: string) {
    return apiClient.get(`customers/search?name=${name}`)
  },
  getCustomersByType(typeId: number) {
    return apiClient.get(`customers/type/${typeId}`)
  },
  getCustomersByTag(tagId: number) {
    return apiClient.get(`customers/tag/${tagId}`)
  },
  getCustomer(id: number) {
    return apiClient.get(`customers/${id}`)
  },
  getCustomerContacts(id: number) {
    return apiClient.get(`customers/${id}/contacts`)
  },
  getCustomerProjects(id: number) {
    return apiClient.get(`customers/${id}/projects`)
  },
  createCustomer(data: any) {
    return apiClient.post('customers', data)
  },
  updateCustomer(id: number, data: any) {
    return apiClient.put(`customers/${id}`, data)
  },
  deleteCustomer(id: number) {
    return apiClient.delete(`customers/${id}`)
  },

  // Customer Type endpoints
  getCustomerTypes() {
    return apiClient.get('customer-types')
  },
  getCustomerType(id: number) {
    return apiClient.get(`customer-types/${id}`)
  },
  createCustomerType(data: any) {
    return apiClient.post('customer-types', data)
  },
  updateCustomerType(id: number, data: any) {
    return apiClient.put(`customer-types/${id}`, data)
  },
  deleteCustomerType(id: number) {
    return apiClient.delete(`customer-types/${id}`)
  },

  // Project endpoints
  getProjects() {
    return apiClient.get('projects')
  },
  getProjectsByStatus(status: string) {
    return apiClient.get(`projects/status/${status}`)
  },
  getProjectsByManager(managerId: number) {
    return apiClient.get(`projects/manager/${managerId}`)
  },
  getProjectsByMember(memberId: number) {
    return apiClient.get(`projects/member/${memberId}`)
  },
  getProjectsByCustomer(customerId: number) {
    return apiClient.get(`projects/customer/${customerId}`)
  },
  getProjectsByDateRange(startDate: string, endDate: string) {
    return apiClient.get(`projects/date-range?startDate=${startDate}&endDate=${endDate}`)
  },
  searchProjects(name: string) {
    return apiClient.get(`projects/search?name=${name}`)
  },
  getProject(id: number) {
    return apiClient.get(`projects/${id}`)
  },
  getProjectTasks(id: number) {
    return apiClient.get(`projects/${id}/tasks`)
  },
  getProjectDocuments(id: number) {
    return apiClient.get(`projects/${id}/documents`)
  },
  getProjectResources(id: number) {
    return apiClient.get(`projects/${id}/resources`)
  },
  getProjectResourceAllocations(id: number) {
    return apiClient.get(`resource-allocations/project/${id}`)
  },
  createProject(data: any) {
    return apiClient.post('projects', data)
  },
  updateProject(id: number, data: any) {
    return apiClient.put(`projects/${id}`, data)
  },
  updateProjectStatus(id: number, status: string) {
    return apiClient.put(`projects/${id}/status`, { status })
  },
  updateProjectProgress(id: number, progress: number) {
    return apiClient.put(`projects/${id}/progress`, { progress })
  },
  getProjectProgressDetails(id: number) {
    return apiClient.get(`projects/${id}/progress-details`)
  },
  recalculateProjectProgress(id: number) {
    return apiClient.post(`projects/${id}/recalculate-progress`)
  },
  deleteProject(id: number) {
    return apiClient.delete(`projects/${id}`)
  },
  getProjectMembers(id: number) {
    return apiClient.get(`projects/${id}/members`)
  },
  addProjectMember(id: number, userId: number) {
    return apiClient.post(`projects/${id}/members`, { userId })
  },
  removeProjectMember(id: number, userId: number) {
    return apiClient.delete(`projects/${id}/members/${userId}`)
  },

  // Resource endpoints
  getResources() {
    return apiClient.get('resources')
  },
  getResource(id: number) {
    return apiClient.get(`resources/${id}`)
  },
  getResourcesByType(type: string) {
    return apiClient.get(`resources/type/${type}`)
  },
  getAvailableResources() {
    return apiClient.get('resources/available')
  },
  searchResources(name: string) {
    return apiClient.get(`resources/search?name=${name}`)
  },
  getResourceAllocationsByResourceId(id: number) {
    return apiClient.get(`resources/${id}/allocations`)
  },
  createResource(data: any) {
    return apiClient.post('resources', data)
  },
  updateResource(id: number, data: any) {
    return apiClient.put(`resources/${id}`, data)
  },
  deleteResource(id: number) {
    return apiClient.delete(`resources/${id}`)
  },

  // Task endpoints
  getTasks() {
    return apiClient.get('tasks')
  },
  getTasksByProject(projectId: number) {
    return apiClient.get(`tasks/project/${projectId}`)
  },
  getTasksByAssignee(assigneeId: number) {
    return apiClient.get(`tasks/assignee/${assigneeId}`)
  },
  getTasksByStatus(status: string) {
    return apiClient.get(`tasks/status/${status}`)
  },
  getTasksByPriority(priority: string) {
    return apiClient.get(`tasks/priority/${priority}`)
  },
  getTasksDueBefore(date: string) {
    return apiClient.get(`tasks/due-before?date=${date}`)
  },
  getTasksDueBetween(startDate: string, endDate: string) {
    return apiClient.get(`tasks/due-between?startDate=${startDate}&endDate=${endDate}`)
  },
  getSubtasks(parentId: number) {
    return apiClient.get(`tasks/parent/${parentId}`)
  },
  getRootTasks() {
    return apiClient.get('tasks/root')
  },
  getTask(id: number) {
    return apiClient.get(`tasks/${id}`)
  },
  getTaskReportsByTaskId(id: number) {
    return apiClient.get(`tasks/${id}/reports`)
  },
  getTaskDocuments(id: number) {
    return apiClient.get(`tasks/${id}/documents`)
  },
  createTask(data: any) {
    return apiClient.post('tasks', data)
  },
  updateTask(id: number, data: any) {
    return apiClient.put(`tasks/${id}`, data)
  },
  updateTaskStatus(id: number, status: string) {
    return apiClient.put(`tasks/${id}/status`, { status })
  },
  updateTaskProgress(id: number, progress: number) {
    return apiClient.put(`tasks/${id}/progress`, { progress })
  },
  getTimeBasedProgress(id: number) {
    return apiClient.get(`tasks/${id}/time-based-progress`)
  },
  getTaskProgressAnalysis(id: number) {
    return apiClient.get(`tasks/${id}/progress-analysis`)
  },
  calculateTaskProgress(id: number) {
    return apiClient.post(`tasks/${id}/calculate-progress`)
  },
  deleteTask(id: number) {
    return apiClient.delete(`tasks/${id}`)
  },

  // Dashboard endpoints
  getDashboardSummary() {
    return apiClient.get('dashboard/summary')
  },
  getProjectsDashboard() {
    return apiClient.get('dashboard/projects')
  },
  getTasksDashboard() {
    return apiClient.get('dashboard/tasks')
  },

  // Notification endpoints
  getMyNotifications() {
    return apiClient.get('notifications/my')
  },
  getMyUnreadNotifications() {
    return apiClient.get('notifications/my/unread')
  },
  markNotificationAsRead(id: number) {
    return apiClient.put(`notifications/${id}/read`)
  },
  markAllNotificationsAsRead() {
    return apiClient.put('notifications/read-all')
  },
  deleteNotification(id: number) {
    return apiClient.delete(`notifications/${id}`)
  },

  // Task Report endpoints
  getTaskReports() {
    return apiClient.get('task-reports')
  },
  getTaskReport(id: number) {
    return apiClient.get(`task-reports/${id}`)
  },
  getTaskReportsByTask(taskId: number) {
    return apiClient.get(`task-reports/task/${taskId}`)
  },
  getTaskReportsByUser(userId: number) {
    return apiClient.get(`task-reports/user/${userId}`)
  },
  getTaskReportsByDateRange(startDate: string, endDate: string) {
    return apiClient.get(`task-reports/date-range?startDate=${startDate}&endDate=${endDate}`)
  },
  getMyTaskReports() {
    return apiClient.get('task-reports/my')
  },
  createTaskReport(data: any) {
    return apiClient.post('task-reports', data)
  },
  updateTaskReport(id: number, data: any) {
    return apiClient.put(`task-reports/${id}`, data)
  },
  deleteTaskReport(id: number) {
    return apiClient.delete(`task-reports/${id}`)
  },

  // Resource Allocation endpoints
  getResourceAllocations() {
    return apiClient.get('resource-allocations')
  },
  getResourceAllocation(id: number) {
    return apiClient.get(`resource-allocations/${id}`)
  },
  getResourceAllocationsByResource(resourceId: number) {
    return apiClient.get(`resource-allocations/resource/${resourceId}`)
  },
  getResourceAllocationsByProject(projectId: number) {
    return apiClient.get(`resource-allocations/project/${projectId}`)
  },
  getResourceAllocationsByTask(taskId: number) {
    return apiClient.get(`resource-allocations/task/${taskId}`)
  },
  getResourceAllocationsByDateRange(startDate: string, endDate: string) {
    return apiClient.get(`resource-allocations/date-range?startDate=${startDate}&endDate=${endDate}`)
  },
  getAvailableResourceAllocations(date: string) {
    return apiClient.get(`resource-allocations/available?date=${date}`)
  },
  createResourceAllocation(data: any) {
    return apiClient.post('resource-allocations', data)
  },
  updateResourceAllocation(id: number, data: any) {
    return apiClient.put(`resource-allocations/${id}`, data)
  },
  deleteResourceAllocation(id: number) {
    return apiClient.delete(`resource-allocations/${id}`)
  },

  // Resource Availability endpoints
  checkResourceAvailability(params: {
    resourceId: string | number
    startDate: string
    endDate: string
    requiredQuantity: number
  }) {
    return apiClient.get('resource-availability/check', { params })
  },
  checkProjectResourceRequirements(projectId: number) {
    return apiClient.get(`resource-availability/project/${projectId}/check`)
  },
  checkTaskResourceRequirements(taskId: number) {
    return apiClient.get(`resource-availability/task/${taskId}/check`)
  },
  getResourceUsageStatistics(resourceId: string | number, startDate: string, endDate: string) {
    return apiClient.get(`resource-availability/statistics/${resourceId}`, {
      params: { startDate, endDate }
    })
  },
  batchCheckProjectsResourceRequirements(projectIds: number[]) {
    return apiClient.post('resource-availability/projects/batch-check', projectIds)
  },
  batchCheckTasksResourceRequirements(taskIds: number[]) {
    return apiClient.post('resource-availability/tasks/batch-check', taskIds)
  },
  getResourceAllocationsInDateRange(resourceId: number, startDate: string, endDate: string) {
    return apiClient.get(`resource-availability/resource/${resourceId}/allocations`, {
      params: { startDate, endDate }
    })
  },

  // Contact Record endpoints
  getContactRecords() {
    return apiClient.get('contact-records')
  },
  getContactRecord(id: number) {
    return apiClient.get(`contact-records/${id}`)
  },
  getContactRecordsByCustomer(customerId: number) {
    return apiClient.get(`contact-records/customer/${customerId}`)
  },
  getContactRecordsByUser(userId: number) {
    return apiClient.get(`contact-records/user/${userId}`)
  },
  getContactRecordsByType(type: string) {
    return apiClient.get(`contact-records/type/${type}`)
  },
  getContactRecordsByDateRange(startDate: string, endDate: string) {
    return apiClient.get(`contact-records/date-range?startDate=${startDate}&endDate=${endDate}`)
  },
  createContactRecord(data: any) {
    return apiClient.post('contact-records', data)
  },
  updateContactRecord(id: number, data: any) {
    return apiClient.put(`contact-records/${id}`, data)
  },
  deleteContactRecord(id: number) {
    return apiClient.delete(`contact-records/${id}`)
  },

  // Tag endpoints
  getTags() {
    return apiClient.get('tags')
  },
  getTag(id: number) {
    return apiClient.get(`tags/${id}`)
  },
  getTagByName(name: string) {
    return apiClient.get(`tags/name/${name}`)
  },
  createTag(data: any) {
    return apiClient.post('tags', data)
  },
  updateTag(id: number, data: any) {
    return apiClient.put(`tags/${id}`, data)
  },
  deleteTag(id: number) {
    return apiClient.delete(`tags/${id}`)
  },

  // Activity Log endpoints
  getAllActivityLogs() {
    return apiClient.get('activity-logs')
  },
  getActivityLogsByType(activityType: string) {
    return apiClient.get(`activity-logs/type/${activityType}`)
  },
  getActivityLogsByDateRange(startDate: string, endDate: string) {
    return apiClient.get(`activity-logs/date-range?startDate=${startDate}&endDate=${endDate}`)
  },
  getActivityLogById(id: number) {
    return apiClient.get(`activity-logs/${id}`)
  },

  // Settings endpoints
  getSettings() {
    return apiClient.get('settings')
  },
  getSetting(id: number) {
    return apiClient.get(`settings/${id}`)
  },
  getSettingByKey(key: string) {
    return apiClient.get(`settings/key/${key}`)
  },
  getSettingsByCategory(category: string) {
    return apiClient.get(`settings/category/${category}`)
  },
  getSystemSettings() {
    return apiClient.get('settings/system')
  },
  getCustomSettings() {
    return apiClient.get('settings/custom')
  },
  createSetting(data: any) {
    return apiClient.post('settings', data)
  },
  updateSetting(id: number, data: any) {
    return apiClient.put(`settings/${id}`, data)
  },
  updateSettingByKey(key: string, data: any) {
    return apiClient.put(`settings/key/${key}`, data)
  },
  deleteSetting(id: number) {
    return apiClient.delete(`settings/${id}`)
  }
}
