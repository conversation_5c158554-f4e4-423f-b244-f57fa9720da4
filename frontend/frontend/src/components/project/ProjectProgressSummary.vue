<template>
  <div class="project-progress-summary">
    <el-card class="summary-card">
      <template #header>
        <div class="card-header">
          <span>專案進度總覽</span>
          <el-button
            type="primary"
            size="small"
            @click="refreshData"
            :loading="loading"
          >
            刷新數據
          </el-button>
        </div>
      </template>

      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="4" animated />
      </div>

      <div v-else class="summary-content">
        <!-- 整體進度 -->
        <div class="overall-progress">
          <div class="progress-header">
            <h3>整體進度</h3>
            <span class="progress-percentage">{{ overallProgress }}%</span>
          </div>
          <el-progress
            :percentage="overallProgress"
            :color="getProgressColor(overallProgress)"
            :stroke-width="16"
            :show-text="false"
          />
          <div class="progress-stats">
            <span>已完成 {{ completedTasks }} / {{ totalTasks }} 個任務</span>
          </div>
        </div>

        <!-- 任務狀態分布 -->
        <div class="status-distribution">
          <h3>任務狀態分布</h3>
          <div class="status-grid">
            <div
              v-for="status in taskStatusStats"
              :key="status.status"
              class="status-item"
            >
              <div class="status-icon" :class="getStatusClass(status.status)">
                <span class="status-count">{{ status.count }}</span>
              </div>
              <div class="status-info">
                <div class="status-label">{{ getStatusLabel(status.status) }}</div>
                <div class="status-percentage">{{ getStatusPercentage(status.count) }}%</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 進度趨勢 -->
        <div class="progress-trend">
          <h3>進度分析</h3>
          <div class="trend-grid">
            <div class="trend-item">
              <div class="trend-label">平均進度</div>
              <div class="trend-value">{{ averageProgress }}%</div>
              <el-progress
                :percentage="averageProgress"
                :stroke-width="6"
                :show-text="false"
              />
            </div>
            <div class="trend-item">
              <div class="trend-label">超前任務</div>
              <div class="trend-value text-success">{{ aheadTasks }}</div>
              <div class="trend-desc">進度超過時間進度</div>
            </div>
            <div class="trend-item">
              <div class="trend-label">落後任務</div>
              <div class="trend-value text-danger">{{ behindTasks }}</div>
              <div class="trend-desc">進度低於時間進度</div>
            </div>
            <div class="trend-item">
              <div class="trend-label">逾期任務</div>
              <div class="trend-value text-warning">{{ overdueTasks }}</div>
              <div class="trend-desc">已超過截止日期</div>
            </div>
          </div>
        </div>

        <!-- 關鍵任務 -->
        <div class="critical-tasks">
          <h3>需要關注的任務</h3>
          <div v-if="criticalTasksList.length === 0" class="no-critical">
            <el-empty description="暫無需要特別關注的任務" :image-size="60" />
          </div>
          <div v-else class="critical-list">
            <div
              v-for="task in criticalTasksList"
              :key="task.id"
              class="critical-item"
              @click="viewTask(task.id)"
            >
              <div class="task-info">
                <div class="task-name">{{ task.name }}</div>
                <div class="task-meta">
                  <el-tag :type="getStatusType(task.status)" size="small">
                    {{ getStatusLabel(task.status) }}
                  </el-tag>
                  <span class="task-progress">{{ task.progress }}%</span>
                </div>
              </div>
              <div class="task-warning">
                <el-icon class="warning-icon"><Warning /></el-icon>
                <span>{{ getTaskWarning(task) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Warning } from '@element-plus/icons-vue'
import api from '../../services/api'

interface Props {
  projectId: number
}

const props = defineProps<Props>()
const router = useRouter()

const loading = ref(false)
const tasks = ref<any[]>([])

// 計算屬性
const totalTasks = computed(() => tasks.value.length)

const completedTasks = computed(() =>
  tasks.value.filter(task => task.status === 'COMPLETED').length
)

const overallProgress = computed(() => {
  if (totalTasks.value === 0) return 0
  const totalProgress = tasks.value.reduce((sum, task) => sum + (task.progress || 0), 0)
  return Math.round(totalProgress / totalTasks.value)
})

const averageProgress = computed(() => {
  if (totalTasks.value === 0) return 0
  const totalProgress = tasks.value.reduce((sum, task) => sum + (task.progress || 0), 0)
  return Math.round(totalProgress / totalTasks.value)
})

const taskStatusStats = computed(() => {
  const stats = {}
  tasks.value.forEach(task => {
    stats[task.status] = (stats[task.status] || 0) + 1
  })

  return Object.entries(stats).map(([status, count]) => ({
    status,
    count: count as number
  }))
})

const aheadTasks = computed(() => {
  return tasks.value.filter(task => {
    // 簡化的超前判斷邏輯
    const now = new Date()
    const start = new Date(task.startDate)
    const due = new Date(task.dueDate)
    const timeProgress = Math.min(100, Math.max(0,
      (now.getTime() - start.getTime()) / (due.getTime() - start.getTime()) * 100
    ))
    return (task.progress || 0) > timeProgress + 10
  }).length
})

const behindTasks = computed(() => {
  return tasks.value.filter(task => {
    const now = new Date()
    const start = new Date(task.startDate)
    const due = new Date(task.dueDate)
    const timeProgress = Math.min(100, Math.max(0,
      (now.getTime() - start.getTime()) / (due.getTime() - start.getTime()) * 100
    ))
    return (task.progress || 0) < timeProgress - 10
  }).length
})

const overdueTasks = computed(() => {
  const now = new Date()
  return tasks.value.filter(task =>
    new Date(task.dueDate) < now && task.status !== 'COMPLETED'
  ).length
})

const criticalTasksList = computed(() => {
  const now = new Date()
  return tasks.value.filter(task => {
    // 逾期且未完成
    if (new Date(task.dueDate) < now && task.status !== 'COMPLETED') {
      return true
    }

    // 即將到期（3天內）且進度不足
    const daysToDeadline = (new Date(task.dueDate).getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
    if (daysToDeadline <= 3 && daysToDeadline > 0 && (task.progress || 0) < 80) {
      return true
    }

    // 進度嚴重落後
    const start = new Date(task.startDate)
    const due = new Date(task.dueDate)
    const timeProgress = Math.min(100, Math.max(0,
      (now.getTime() - start.getTime()) / (due.getTime() - start.getTime()) * 100
    ))
    if ((task.progress || 0) < timeProgress - 20) {
      return true
    }

    return false
  }).slice(0, 5) // 最多顯示5個
})

// 方法
const fetchTasks = async () => {
  loading.value = true
  try {
    const response = await api.getTasksByProject(props.projectId)
    tasks.value = response.data
  } catch (error) {
    console.error('Failed to fetch tasks:', error)
    ElMessage.error('獲取任務數據失敗')
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  fetchTasks()
}

const getProgressColor = (percentage: number) => {
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

const getStatusClass = (status: string) => {
  const classMap = {
    'NOT_STARTED': 'status-not-started',
    'IN_PROGRESS': 'status-in-progress',
    'COMPLETED': 'status-completed',
    'DELAYED': 'status-delayed'
  }
  return classMap[status] || ''
}

const getStatusLabel = (status: string) => {
  const labelMap = {
    'NOT_STARTED': '未開始',
    'IN_PROGRESS': '進行中',
    'COMPLETED': '已完成',
    'DELAYED': '延期'
  }
  return labelMap[status] || status
}

const getStatusType = (status: string) => {
  const typeMap = {
    'NOT_STARTED': 'info',
    'IN_PROGRESS': 'warning',
    'COMPLETED': 'success',
    'DELAYED': 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusPercentage = (count: number) => {
  if (totalTasks.value === 0) return 0
  return Math.round((count / totalTasks.value) * 100)
}

const getTaskWarning = (task: any) => {
  const now = new Date()
  const dueDate = new Date(task.dueDate)

  if (dueDate < now && task.status !== 'COMPLETED') {
    return '已逾期'
  }

  const daysToDeadline = (dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
  if (daysToDeadline <= 3 && daysToDeadline > 0) {
    return `${Math.ceil(daysToDeadline)}天後到期`
  }

  return '進度落後'
}

const viewTask = (taskId: number) => {
  router.push({ name: 'task-detail', params: { id: taskId } })
}

onMounted(() => {
  fetchTasks()
})
</script>

<style scoped>
.project-progress-summary {
  width: 100%;
}

.summary-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.loading-container {
  padding: 20px;
}

.summary-content {
  padding: 10px 0;
}

.overall-progress {
  margin-bottom: 30px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.progress-header h3 {
  margin: 0;
  color: #303133;
}

.progress-percentage {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.progress-stats {
  margin-top: 8px;
  font-size: 14px;
  color: #606266;
}

.status-distribution,
.progress-trend,
.critical-tasks {
  margin-bottom: 30px;
}

.status-distribution h3,
.progress-trend h3,
.critical-tasks h3 {
  margin: 0 0 15px 0;
  color: #303133;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.status-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
}

.status-not-started { background-color: #909399; }
.status-in-progress { background-color: #e6a23c; }
.status-completed { background-color: #67c23a; }
.status-delayed { background-color: #f56c6c; }

.status-info {
  flex: 1;
}

.status-label {
  font-size: 14px;
  color: #303133;
  margin-bottom: 2px;
}

.status-percentage {
  font-size: 12px;
  color: #606266;
}

.trend-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.trend-item {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  text-align: center;
}

.trend-label {
  font-size: 12px;
  color: #606266;
  margin-bottom: 5px;
}

.trend-value {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 8px;
}

.trend-desc {
  font-size: 11px;
  color: #909399;
}

.text-success { color: #67c23a; }
.text-danger { color: #f56c6c; }
.text-warning { color: #e6a23c; }

.no-critical {
  text-align: center;
  padding: 20px;
}

.critical-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.critical-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.critical-item:hover {
  background-color: #fde2e2;
  transform: translateY(-1px);
}

.task-info {
  flex: 1;
}

.task-name {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.task-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-progress {
  font-size: 12px;
  color: #606266;
}

.task-warning {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #f56c6c;
}

.warning-icon {
  font-size: 14px;
}
</style>
