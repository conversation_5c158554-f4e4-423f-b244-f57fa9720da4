<template>
  <div class="gantt-chart-container">
    <div class="gantt-toolbar">
      <el-button-group>
        <el-button :type="view === 'day' ? 'primary' : ''" @click="view = 'day'">日</el-button>
        <el-button :type="view === 'week' ? 'primary' : ''" @click="view = 'week'">週</el-button>
        <el-button :type="view === 'month' ? 'primary' : ''" @click="view = 'month'">月</el-button>
      </el-button-group>
      
      <div class="gantt-date-navigation">
        <el-button @click="navigateDate(-1)">
          <el-icon><ArrowLeft /></el-icon>
        </el-button>
        <span class="gantt-current-date">{{ formatCurrentDate() }}</span>
        <el-button @click="navigateDate(1)">
          <el-icon><ArrowRight /></el-icon>
        </el-button>
      </div>
      
      <el-button @click="resetDate">今天</el-button>
    </div>
    
    <div class="gantt-chart" ref="ganttChartRef">
      <div class="gantt-header">
        <div class="gantt-header-scale">
          <div
            v-for="(scale, index) in timeScales"
            :key="index"
            class="gantt-scale-cell"
            :style="{ width: `${scale.width}px` }"
          >
            {{ scale.label }}
          </div>
        </div>
      </div>
      
      <div class="gantt-body">
        <div class="gantt-task-list">
          <div
            v-for="task in tasks"
            :key="task.id"
            class="gantt-task-row"
            :class="{ 'gantt-task-parent': task.subtasks && task.subtasks.length > 0 }"
          >
            <div class="gantt-task-info">
              <div class="gantt-task-name">{{ task.name }}</div>
            </div>
            
            <div class="gantt-task-timeline">
              <div
                class="gantt-task-bar"
                :class="getTaskStatusClass(task.status)"
                :style="getTaskBarStyle(task)"
                @click="$emit('task-click', task.id)"
              >
                <div class="gantt-task-progress" :style="{ width: `${task.progress}%` }"></div>
                <div class="gantt-task-label">{{ task.name }}</div>
              </div>
              
              <div
                v-for="dependency in task.dependencies"
                :key="`${task.id}-${dependency.id}`"
                class="gantt-dependency-line"
                :style="getDependencyLineStyle(task, dependency)"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, PropType } from 'vue'
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'

const props = defineProps({
  tasks: {
    type: Array as PropType<any[]>,
    required: true
  },
  startDate: {
    type: String,
    required: true
  },
  endDate: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['task-click'])

const ganttChartRef = ref<HTMLElement | null>(null)
const view = ref('week')
const currentDate = ref(new Date())
const cellWidth = ref(40)

// Computed properties
const timeScales = computed(() => {
  const scales = []
  const start = new Date(props.startDate)
  const end = new Date(props.endDate)
  
  if (view.value === 'day') {
    // Daily view
    const days = getDaysBetween(start, end)
    for (let i = 0; i <= days; i++) {
      const date = new Date(start)
      date.setDate(date.getDate() + i)
      scales.push({
        label: formatDate(date, 'MM/DD'),
        width: cellWidth.value
      })
    }
  } else if (view.value === 'week') {
    // Weekly view
    const weeks = Math.ceil(getDaysBetween(start, end) / 7)
    for (let i = 0; i < weeks; i++) {
      const weekStart = new Date(start)
      weekStart.setDate(weekStart.getDate() + i * 7)
      const weekEnd = new Date(weekStart)
      weekEnd.setDate(weekEnd.getDate() + 6)
      scales.push({
        label: `${formatDate(weekStart, 'MM/DD')}-${formatDate(weekEnd, 'MM/DD')}`,
        width: cellWidth.value * 7
      })
    }
  } else if (view.value === 'month') {
    // Monthly view
    const months = getMonthsBetween(start, end)
    for (let i = 0; i <= months; i++) {
      const date = new Date(start.getFullYear(), start.getMonth() + i, 1)
      scales.push({
        label: formatDate(date, 'YYYY/MM'),
        width: cellWidth.value * getDaysInMonth(date.getFullYear(), date.getMonth())
      })
    }
  }
  
  return scales
})

// Helper functions
const getDaysBetween = (start: Date, end: Date) => {
  const oneDay = 24 * 60 * 60 * 1000
  return Math.round(Math.abs((end.getTime() - start.getTime()) / oneDay))
}

const getMonthsBetween = (start: Date, end: Date) => {
  return (end.getFullYear() - start.getFullYear()) * 12 + end.getMonth() - start.getMonth()
}

const getDaysInMonth = (year: number, month: number) => {
  return new Date(year, month + 1, 0).getDate()
}

const formatDate = (date: Date, format: string) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
}

const formatCurrentDate = () => {
  if (view.value === 'day') {
    return formatDate(currentDate.value, 'YYYY/MM/DD')
  } else if (view.value === 'week') {
    const weekStart = new Date(currentDate.value)
    const day = currentDate.value.getDay()
    weekStart.setDate(currentDate.value.getDate() - day + (day === 0 ? -6 : 1))
    const weekEnd = new Date(weekStart)
    weekEnd.setDate(weekStart.getDate() + 6)
    return `${formatDate(weekStart, 'YYYY/MM/DD')} - ${formatDate(weekEnd, 'YYYY/MM/DD')}`
  } else if (view.value === 'month') {
    return formatDate(currentDate.value, 'YYYY/MM')
  }
  return ''
}

const navigateDate = (direction: number) => {
  if (view.value === 'day') {
    currentDate.value.setDate(currentDate.value.getDate() + direction)
  } else if (view.value === 'week') {
    currentDate.value.setDate(currentDate.value.getDate() + direction * 7)
  } else if (view.value === 'month') {
    currentDate.value.setMonth(currentDate.value.getMonth() + direction)
  }
  currentDate.value = new Date(currentDate.value)
}

const resetDate = () => {
  currentDate.value = new Date()
}

const getTaskBarStyle = (task: any) => {
  const start = new Date(props.startDate)
  const taskStart = new Date(task.startDate)
  const taskEnd = new Date(task.dueDate || task.endDate)
  
  const left = getDaysBetween(start, taskStart) * cellWidth.value
  const width = Math.max(cellWidth.value, getDaysBetween(taskStart, taskEnd) * cellWidth.value)
  
  return {
    left: `${left}px`,
    width: `${width}px`
  }
}

const getTaskStatusClass = (status: string) => {
  const statusMap: Record<string, string> = {
    'NOT_STARTED': 'gantt-task-not-started',
    'IN_PROGRESS': 'gantt-task-in-progress',
    'COMPLETED': 'gantt-task-completed',
    'DELAYED': 'gantt-task-delayed'
  }
  return statusMap[status] || ''
}

const getDependencyLineStyle = (task: any, dependency: any) => {
  // This is a simplified implementation
  // In a real application, you would need to calculate the actual path
  return {
    display: 'none' // Hide for now
  }
}

onMounted(() => {
  // Initialize with project start date
  currentDate.value = new Date(props.startDate)
})

// Watch for changes in view
watch(view, () => {
  // Adjust cell width based on view
  if (view.value === 'day') {
    cellWidth.value = 40
  } else if (view.value === 'week') {
    cellWidth.value = 20
  } else if (view.value === 'month') {
    cellWidth.value = 10
  }
})
</script>

<style scoped>
.gantt-chart-container {
  width: 100%;
  overflow: hidden;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.gantt-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #f5f7fa;
}

.gantt-date-navigation {
  display: flex;
  align-items: center;
}

.gantt-current-date {
  margin: 0 10px;
  font-weight: bold;
}

.gantt-chart {
  display: flex;
  flex-direction: column;
  overflow-x: auto;
}

.gantt-header {
  position: sticky;
  top: 0;
  background-color: #f5f7fa;
  z-index: 1;
}

.gantt-header-scale {
  display: flex;
  border-bottom: 1px solid #e4e7ed;
}

.gantt-scale-cell {
  padding: 8px 4px;
  text-align: center;
  border-right: 1px solid #e4e7ed;
  font-size: 12px;
  white-space: nowrap;
}

.gantt-body {
  display: flex;
  flex-direction: column;
}

.gantt-task-row {
  display: flex;
  border-bottom: 1px solid #e4e7ed;
  height: 40px;
}

.gantt-task-parent {
  background-color: #f5f7fa;
  font-weight: bold;
}

.gantt-task-info {
  width: 200px;
  min-width: 200px;
  padding: 8px;
  border-right: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
}

.gantt-task-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.gantt-task-timeline {
  position: relative;
  flex: 1;
}

.gantt-task-bar {
  position: absolute;
  height: 20px;
  top: 10px;
  border-radius: 4px;
  background-color: #409EFF;
  cursor: pointer;
  overflow: hidden;
}

.gantt-task-not-started {
  background-color: #909399;
}

.gantt-task-in-progress {
  background-color: #409EFF;
}

.gantt-task-completed {
  background-color: #67C23A;
}

.gantt-task-delayed {
  background-color: #F56C6C;
}

.gantt-task-progress {
  height: 100%;
  background-color: rgba(255, 255, 255, 0.3);
}

.gantt-task-label {
  position: absolute;
  top: 0;
  left: 4px;
  right: 4px;
  bottom: 0;
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.gantt-dependency-line {
  position: absolute;
  border-top: 2px dashed #909399;
  z-index: 0;
}
</style>
