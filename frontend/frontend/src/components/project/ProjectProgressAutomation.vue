<template>
  <div class="project-progress-automation">
    <el-card class="automation-card">
      <template #header>
        <div class="card-header">
          <span>專案進度自動化</span>
          <el-button 
            type="primary" 
            size="small" 
            @click="recalculateProgress"
            :loading="recalculating"
          >
            重新計算進度
          </el-button>
        </div>
      </template>

      <div v-loading="loading" class="automation-content">
        <!-- 當前進度顯示 -->
        <div class="current-progress">
          <div class="progress-header">
            <h3>當前進度</h3>
            <span class="progress-value">{{ progressDetails.currentProgress || 0 }}%</span>
          </div>
          <el-progress 
            :percentage="progressDetails.currentProgress || 0" 
            :color="getProgressColor(progressDetails.currentProgress || 0)"
            :stroke-width="16"
            :show-text="false"
          />
          <div class="progress-meta">
            <span>自動計算 | 最後更新: {{ formatTime(progressDetails.lastCalculated) }}</span>
          </div>
        </div>

        <!-- 計算說明 -->
        <div class="calculation-explanation">
          <h3>進度計算方式</h3>
          <div class="explanation-content">
            <div class="explanation-item">
              <el-icon class="explanation-icon"><InfoFilled /></el-icon>
              <div class="explanation-text">
                <strong>自動計算：</strong>專案進度根據所屬任務的完成情況自動計算，無需手動調整
              </div>
            </div>
            <div class="explanation-item">
              <el-icon class="explanation-icon"><TrendCharts /></el-icon>
              <div class="explanation-text">
                <strong>加權平均：</strong>根據任務優先級給予不同權重（緊急: 2.0x, 高: 1.5x, 中: 1.0x, 低: 0.7x）
              </div>
            </div>
            <div class="explanation-item">
              <el-icon class="explanation-icon"><Clock /></el-icon>
              <div class="explanation-text">
                <strong>即時更新：</strong>當任務進度更新時，專案進度會自動重新計算
              </div>
            </div>
          </div>
        </div>

        <!-- 任務統計 -->
        <div class="task-statistics">
          <h3>任務統計</h3>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">{{ progressDetails.totalTasks || 0 }}</div>
              <div class="stat-label">總任務數</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ progressDetails.completedTasks || 0 }}</div>
              <div class="stat-label">已完成任務</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ Math.round(progressDetails.averageTaskProgress || 0) }}%</div>
              <div class="stat-label">平均任務進度</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ getCompletionRate() }}%</div>
              <div class="stat-label">完成率</div>
            </div>
          </div>
        </div>

        <!-- 優先級分布 -->
        <div v-if="progressDetails.tasksByPriority" class="priority-distribution">
          <h3>任務優先級分布</h3>
          <div class="priority-grid">
            <div 
              v-for="(count, priority) in progressDetails.tasksByPriority" 
              :key="priority"
              class="priority-item"
            >
              <div class="priority-header">
                <el-tag :type="getPriorityType(priority)" size="small">
                  {{ getPriorityLabel(priority) }}
                </el-tag>
                <span class="priority-count">{{ count }} 個任務</span>
              </div>
              <div class="priority-progress">
                <span class="priority-progress-text">
                  平均進度: {{ Math.round(progressDetails.progressByPriority[priority] || 0) }}%
                </span>
                <el-progress 
                  :percentage="Math.round(progressDetails.progressByPriority[priority] || 0)" 
                  :stroke-width="6"
                  :show-text="false"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 自動化規則 -->
        <div class="automation-rules">
          <h3>自動化規則</h3>
          <div class="rules-list">
            <div class="rule-item">
              <el-icon class="rule-icon success"><CircleCheck /></el-icon>
              <div class="rule-text">
                <strong>狀態自動更新：</strong>
                <ul>
                  <li>進度 > 0% 且狀態為「規劃中」→ 自動更新為「執行中」</li>
                  <li>進度 = 100% → 自動更新為「已完成」</li>
                  <li>超過截止日期且進度 < 100% → 自動更新為「延期」</li>
                </ul>
              </div>
            </div>
            <div class="rule-item">
              <el-icon class="rule-icon warning"><Warning /></el-icon>
              <div class="rule-text">
                <strong>權重計算：</strong>
                任務持續時間超過7天的任務權重增加20%，確保長期任務的影響得到適當體現
              </div>
            </div>
            <div class="rule-item">
              <el-icon class="rule-icon info"><InfoFilled /></el-icon>
              <div class="rule-text">
                <strong>進度記錄：</strong>
                當進度變化超過5%時，系統會自動記錄變更日誌，便於追蹤專案進展
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  InfoFilled, 
  TrendCharts, 
  Clock, 
  CircleCheck, 
  Warning 
} from '@element-plus/icons-vue'
import api from '../../services/api'

interface Props {
  projectId: number
}

const props = defineProps<Props>()

const loading = ref(false)
const recalculating = ref(false)
const progressDetails = ref<any>({})

// 計算屬性
const getCompletionRate = () => {
  if (!progressDetails.value.totalTasks || progressDetails.value.totalTasks === 0) return 0
  return Math.round((progressDetails.value.completedTasks / progressDetails.value.totalTasks) * 100)
}

// 方法
const fetchProgressDetails = async () => {
  loading.value = true
  try {
    const response = await api.getProjectProgressDetails(props.projectId)
    progressDetails.value = response.data
  } catch (error) {
    console.error('Failed to fetch progress details:', error)
    ElMessage.error('獲取進度詳情失敗')
  } finally {
    loading.value = false
  }
}

const recalculateProgress = async () => {
  recalculating.value = true
  try {
    const response = await api.recalculateProjectProgress(props.projectId)
    const result = response.data
    
    ElMessage.success(
      `進度已重新計算：${result.oldProgress}% → ${result.newProgress}%`
    )
    
    // 刷新進度詳情
    await fetchProgressDetails()
  } catch (error) {
    console.error('Failed to recalculate progress:', error)
    ElMessage.error('重新計算進度失敗')
  } finally {
    recalculating.value = false
  }
}

const getProgressColor = (percentage: number) => {
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

const getPriorityType = (priority: string) => {
  const typeMap = {
    'URGENT': 'danger',
    'HIGH': 'warning',
    'MEDIUM': 'primary',
    'LOW': 'info',
    'NONE': 'info'
  }
  return typeMap[priority] || 'info'
}

const getPriorityLabel = (priority: string) => {
  const labelMap = {
    'URGENT': '緊急',
    'HIGH': '高',
    'MEDIUM': '中',
    'LOW': '低',
    'NONE': '無'
  }
  return labelMap[priority] || priority
}

const formatTime = (dateString: string) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleString('zh-TW')
}

onMounted(() => {
  fetchProgressDetails()
})
</script>

<style scoped>
.project-progress-automation {
  width: 100%;
}

.automation-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.automation-content {
  padding: 10px 0;
}

.current-progress {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.progress-header h3 {
  margin: 0;
  color: #303133;
}

.progress-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.progress-meta {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.calculation-explanation,
.task-statistics,
.priority-distribution,
.automation-rules {
  margin-bottom: 30px;
}

.calculation-explanation h3,
.task-statistics h3,
.priority-distribution h3,
.automation-rules h3 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
}

.explanation-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.explanation-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 12px;
  background-color: #f0f9ff;
  border-radius: 6px;
}

.explanation-icon {
  color: #409eff;
  font-size: 16px;
  margin-top: 2px;
}

.explanation-text {
  flex: 1;
  font-size: 14px;
  line-height: 1.5;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #606266;
}

.priority-grid {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.priority-item {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.priority-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.priority-count {
  font-size: 12px;
  color: #606266;
}

.priority-progress-text {
  font-size: 12px;
  color: #606266;
  margin-bottom: 5px;
  display: block;
}

.rules-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.rule-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.rule-icon {
  font-size: 16px;
  margin-top: 2px;
}

.rule-icon.success { color: #67c23a; }
.rule-icon.warning { color: #e6a23c; }
.rule-icon.info { color: #409eff; }

.rule-text {
  flex: 1;
  font-size: 14px;
  line-height: 1.5;
}

.rule-text ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.rule-text li {
  margin-bottom: 4px;
}
</style>
