<template>
  <div class="project-list">
    <div class="project-list-header">
      <div class="search-box">
        <el-input
          v-model="searchQuery"
          placeholder="搜尋專案名稱"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #append>
            <el-button @click="handleSearch">
              <el-icon><Search /></el-icon>
            </el-button>
          </template>
        </el-input>
      </div>
      
      <div class="filter-box">
        <el-select
          v-model="selectedStatus"
          placeholder="專案狀態"
          clearable
          @change="handleStatusChange"
        >
          <el-option label="規劃中" value="PLANNING" />
          <el-option label="執行中" value="IN_PROGRESS" />
          <el-option label="已完成" value="COMPLETED" />
          <el-option label="延期" value="DELAYED" />
          <el-option label="結案" value="CLOSED" />
        </el-select>
        
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="開始日期"
          end-placeholder="結束日期"
          format="YYYY/MM/DD"
          value-format="YYYY-MM-DD"
          @change="handleDateRangeChange"
        />
      </div>
      
      <div class="action-box">
        <el-button type="primary" @click="$emit('add-project')">
          <el-icon><Plus /></el-icon> 新增專案
        </el-button>
      </div>
    </div>
    
    <el-table
      :data="projects"
      style="width: 100%"
      v-loading="loading"
      @row-click="handleRowClick"
    >
      <el-table-column prop="name" label="專案名稱" min-width="150" />
      <el-table-column prop="customer.name" label="客戶" min-width="120" />
      <el-table-column label="專案負責人" width="120">
        <template #default="scope">
          {{ scope.row.manager ? `${scope.row.manager.firstName} ${scope.row.manager.lastName}` : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="日期" width="200">
        <template #default="scope">
          {{ formatDate(scope.row.startDate) }} 至 {{ formatDate(scope.row.endDate) }}
        </template>
      </el-table-column>
      <el-table-column label="狀態" width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusLabel(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="進度" width="180">
        <template #default="scope">
          <el-progress :percentage="scope.row.progress" />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="scope">
          <el-button
            type="primary"
            size="small"
            @click.stop="$emit('view-project', scope.row.id)"
          >
            查看
          </el-button>
          <el-button
            type="danger"
            size="small"
            @click.stop="handleDelete(scope.row)"
          >
            刪除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus } from '@element-plus/icons-vue'
import api from '../../services/api'

const emit = defineEmits(['view-project', 'add-project', 'refresh'])

// Data
const projects = ref([])
const loading = ref(false)
const searchQuery = ref('')
const selectedStatus = ref('')
const dateRange = ref<[string, string] | null>(null)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// Fetch projects
const fetchProjects = async () => {
  loading.value = true
  try {
    let response
    
    if (searchQuery.value) {
      response = await api.searchProjects(searchQuery.value)
    } else if (selectedStatus.value) {
      response = await api.getProjectsByStatus(selectedStatus.value)
    } else if (dateRange.value && dateRange.value[0] && dateRange.value[1]) {
      response = await api.getProjectsByDateRange(dateRange.value[0], dateRange.value[1])
    } else {
      response = await api.getProjects()
    }
    
    projects.value = response.data
    total.value = response.data.length
  } catch (error) {
    console.error('Failed to fetch projects:', error)
    ElMessage.error('獲取專案資料失敗')
  } finally {
    loading.value = false
  }
}

// Handle search
const handleSearch = () => {
  currentPage.value = 1
  selectedStatus.value = ''
  dateRange.value = null
  fetchProjects()
}

// Handle status change
const handleStatusChange = () => {
  currentPage.value = 1
  searchQuery.value = ''
  dateRange.value = null
  fetchProjects()
}

// Handle date range change
const handleDateRangeChange = () => {
  if (dateRange.value && dateRange.value[0] && dateRange.value[1]) {
    currentPage.value = 1
    searchQuery.value = ''
    selectedStatus.value = ''
    fetchProjects()
  }
}

// Handle row click
const handleRowClick = (row: any) => {
  emit('view-project', row.id)
}

// Handle delete
const handleDelete = (project: any) => {
  ElMessageBox.confirm(
    `確定要刪除專案 "${project.name}" 嗎？`,
    '刪除專案',
    {
      confirmButtonText: '確定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await api.deleteProject(project.id)
      ElMessage.success('專案已刪除')
      fetchProjects()
      emit('refresh')
    } catch (error) {
      console.error('Failed to delete project:', error)
      ElMessage.error('刪除專案失敗')
    }
  }).catch(() => {
    // User cancelled
  })
}

// Pagination
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchProjects()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchProjects()
}

// Helper functions
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-TW')
}

const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    'PLANNING': '規劃中',
    'IN_PROGRESS': '執行中',
    'COMPLETED': '已完成',
    'DELAYED': '延期',
    'CLOSED': '結案'
  }
  return statusMap[status] || status
}

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'PLANNING': 'info',
    'IN_PROGRESS': 'primary',
    'COMPLETED': 'success',
    'DELAYED': 'danger',
    'CLOSED': 'info'
  }
  return typeMap[status] || 'info'
}

// Watch for changes in search query
watch(searchQuery, (newVal, oldVal) => {
  if (newVal === '' && oldVal !== '') {
    fetchProjects()
  }
})

onMounted(() => {
  fetchProjects()
})
</script>

<style scoped>
.project-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.search-box {
  flex: 1;
  min-width: 250px;
}

.filter-box {
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .project-list-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box, .filter-box, .action-box {
    width: 100%;
    margin-bottom: 10px;
  }
  
  .filter-box {
    flex-direction: column;
  }
}
</style>
