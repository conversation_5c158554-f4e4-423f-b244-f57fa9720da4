<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="120px"
    v-loading="loading"
  >
    <el-form-item label="專案名稱" prop="name">
      <el-input v-model="form.name" placeholder="請輸入專案名稱" />
    </el-form-item>
    
    <el-form-item label="專案描述" prop="description">
      <el-input v-model="form.description" type="textarea" :rows="3" placeholder="請輸入專案描述" />
    </el-form-item>
    
    <el-form-item label="開始日期" prop="startDate">
      <el-date-picker
        v-model="form.startDate"
        type="date"
        placeholder="選擇開始日期"
        format="YYYY/MM/DD"
        value-format="YYYY-MM-DD"
        style="width: 100%"
      />
    </el-form-item>
    
    <el-form-item label="結束日期" prop="endDate">
      <el-date-picker
        v-model="form.endDate"
        type="date"
        placeholder="選擇結束日期"
        format="YYYY/MM/DD"
        value-format="YYYY-MM-DD"
        style="width: 100%"
      />
    </el-form-item>
    
    <el-form-item label="專案狀態" prop="status">
      <el-select v-model="form.status" placeholder="請選擇專案狀態" style="width: 100%">
        <el-option label="規劃中" value="PLANNING" />
        <el-option label="執行中" value="IN_PROGRESS" />
        <el-option label="已完成" value="COMPLETED" />
        <el-option label="延期" value="DELAYED" />
        <el-option label="結案" value="CLOSED" />
      </el-select>
    </el-form-item>
    
    <el-form-item label="進度" prop="progress">
      <el-slider v-model="form.progress" :step="5" show-stops />
    </el-form-item>
    
    <el-form-item label="客戶" prop="customer">
      <el-select
        v-model="form.customer"
        filterable
        remote
        reserve-keyword
        placeholder="請選擇客戶"
        :remote-method="remoteCustomerSearch"
        :loading="customersLoading"
        style="width: 100%"
      >
        <el-option
          v-for="item in customers"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    
    <el-form-item label="專案負責人" prop="manager">
      <el-select
        v-model="form.manager"
        filterable
        placeholder="請選擇專案負責人"
        style="width: 100%"
      >
        <el-option
          v-for="item in users"
          :key="item.id"
          :label="`${item.firstName} ${item.lastName} (${item.username})`"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    
    <el-form-item label="專案成員" prop="members">
      <el-select
        v-model="form.members"
        multiple
        filterable
        placeholder="請選擇專案成員"
        style="width: 100%"
      >
        <el-option
          v-for="item in users"
          :key="item.id"
          :label="`${item.firstName} ${item.lastName} (${item.username})`"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    
    <el-form-item label="預算" prop="budget">
      <el-input-number v-model="form.budget" :min="0" :precision="2" :step="1000" style="width: 100%" />
    </el-form-item>
    
    <el-form-item>
      <el-button type="primary" @click="submitForm">{{ submitButtonText }}</el-button>
      <el-button @click="resetForm">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineProps, defineEmits } from 'vue'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import api from '../../services/api'

const props = defineProps({
  projectId: {
    type: Number,
    default: null
  },
  submitButtonText: {
    type: String,
    default: '儲存'
  }
})

const emit = defineEmits(['submit-success'])

const formRef = ref<FormInstance>()
const loading = ref(false)
const customersLoading = ref(false)
const customers = ref([])
const users = ref([])

const form = reactive({
  name: '',
  description: '',
  startDate: '',
  endDate: '',
  status: 'PLANNING',
  progress: 0,
  customer: null as number | null,
  manager: null as number | null,
  members: [] as number[],
  budget: 0
})

const rules = reactive<FormRules>({
  name: [
    { required: true, message: '請輸入專案名稱', trigger: 'blur' },
    { max: 100, message: '長度不能超過 100 個字元', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '長度不能超過 500 個字元', trigger: 'blur' }
  ],
  startDate: [
    { required: true, message: '請選擇開始日期', trigger: 'change' }
  ],
  status: [
    { required: true, message: '請選擇專案狀態', trigger: 'change' }
  ],
  manager: [
    { required: true, message: '請選擇專案負責人', trigger: 'change' }
  ]
})

// Validate end date is after start date
const validateEndDate = (rule: any, value: string, callback: any) => {
  if (value && form.startDate && new Date(value) < new Date(form.startDate)) {
    callback(new Error('結束日期必須晚於開始日期'))
  } else {
    callback()
  }
}

// Fetch users
const fetchUsers = async () => {
  try {
    const response = await api.getUsers()
    users.value = response.data
  } catch (error) {
    console.error('Failed to fetch users:', error)
    ElMessage.error('獲取使用者資料失敗')
  }
}

// Remote search for customers
const remoteCustomerSearch = async (query: string) => {
  if (query) {
    customersLoading.value = true
    try {
      const response = await api.searchCustomers(query)
      customers.value = response.data
    } catch (error) {
      console.error('Failed to search customers:', error)
    } finally {
      customersLoading.value = false
    }
  } else {
    customers.value = []
  }
}

// Fetch project data if editing
const fetchProjectData = async () => {
  if (!props.projectId) return
  
  loading.value = true
  try {
    const response = await api.getProject(props.projectId)
    const project = response.data
    
    form.name = project.name
    form.description = project.description
    form.startDate = project.startDate
    form.endDate = project.endDate
    form.status = project.status
    form.progress = project.progress
    form.customer = project.customer?.id || null
    form.manager = project.manager?.id || null
    form.members = project.members?.map((member: any) => member.id) || []
    form.budget = project.budget || 0
    
    // Add customer to options
    if (project.customer) {
      customers.value = [project.customer]
    }
  } catch (error) {
    console.error('Failed to fetch project data:', error)
    ElMessage.error('獲取專案資料失敗')
  } finally {
    loading.value = false
  }
}

// Submit form
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        // Prepare data
        const projectData = {
          name: form.name,
          description: form.description,
          startDate: form.startDate,
          endDate: form.endDate,
          status: form.status,
          progress: form.progress,
          customer: form.customer ? { id: form.customer } : null,
          manager: { id: form.manager },
          members: form.members.map(id => ({ id })),
          budget: form.budget
        }
        
        // Create or update project
        let response
        if (props.projectId) {
          response = await api.updateProject(props.projectId, projectData)
        } else {
          response = await api.createProject(projectData)
        }
        
        ElMessage.success(props.projectId ? '專案資料已更新' : '專案已新增')
        emit('submit-success', response.data)
        
        // Reset form if creating new project
        if (!props.projectId) {
          resetForm()
        }
      } catch (error) {
        console.error('Failed to save project:', error)
        ElMessage.error(props.projectId ? '更新專案資料失敗' : '新增專案失敗')
      } finally {
        loading.value = false
      }
    }
  })
}

// Reset form
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

onMounted(() => {
  fetchUsers()
  if (props.projectId) {
    fetchProjectData()
  }
})
</script>

<style scoped>
.el-select {
  width: 100%;
}
</style>
