<template>
  <div class="department-form">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      @submit.prevent="handleSubmit"
    >
      <el-form-item label="部門名稱" prop="name">
        <el-input
          v-model="form.name"
          placeholder="請輸入部門名稱"
          maxlength="50"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="部門描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          placeholder="請輸入部門描述"
          :rows="3"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="上級部門" prop="parentId">
        <el-select
          v-model="form.parentId"
          placeholder="選擇上級部門（可選）"
          style="width: 100%"
          clearable
          filterable
        >
          <el-option
            v-for="dept in availableParentDepartments"
            :key="dept.id"
            :label="dept.name"
            :value="dept.id"
            :disabled="dept.id === form.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="部門主管" prop="managerId">
        <el-select
          v-model="form.managerId"
          placeholder="選擇部門主管（可選）"
          style="width: 100%"
          clearable
          filterable
          remote
          :remote-method="searchUsers"
          :loading="userSearchLoading"
        >
          <el-option
            v-for="user in availableManagers"
            :key="user.id"
            :label="`${user.firstName} ${user.lastName} (${user.username})`"
            :value="user.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="部門狀態" prop="active">
        <el-switch
          v-model="form.active"
          active-text="啟用"
          inactive-text="停用"
        />
      </el-form-item>

      <el-form-item label="聯繫電話" prop="phone">
        <el-input
          v-model="form.phone"
          placeholder="請輸入聯繫電話（可選）"
          maxlength="20"
        />
      </el-form-item>

      <el-form-item label="辦公地址" prop="address">
        <el-input
          v-model="form.address"
          placeholder="請輸入辦公地址（可選）"
          maxlength="100"
        />
      </el-form-item>

      <el-form-item label="部門職責" prop="responsibilities">
        <el-input
          v-model="form.responsibilities"
          type="textarea"
          placeholder="請輸入部門主要職責（可選）"
          :rows="3"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <div class="form-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEditing ? '更新' : '創建' }}
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import api from '../../services/api'

interface Props {
  department?: any
  isEditing?: boolean
  parentDepartment?: any
}

const props = withDefaults(defineProps<Props>(), {
  department: null,
  isEditing: false,
  parentDepartment: null
})

const emit = defineEmits<{
  submit: [data: any]
  cancel: []
}>()

// 響應式數據
const formRef = ref<FormInstance>()
const submitting = ref(false)
const userSearchLoading = ref(false)
const availableDepartments = ref<any[]>([])
const availableManagers = ref<any[]>([])

// 表單數據
const form = reactive({
  name: '',
  description: '',
  parentId: null as number | null,
  managerId: null as number | null,
  active: true,
  phone: '',
  address: '',
  responsibilities: ''
})

// 表單驗證規則
const rules: FormRules = {
  name: [
    { required: true, message: '請輸入部門名稱', trigger: 'blur' },
    { min: 2, max: 50, message: '部門名稱長度應在 2-50 個字符', trigger: 'blur' }
  ],
  description: [
    { max: 200, message: '描述長度不能超過 200 個字符', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^[\d\-\+\(\)\s]+$/, message: '請輸入有效的電話號碼', trigger: 'blur' }
  ],
  address: [
    { max: 100, message: '地址長度不能超過 100 個字符', trigger: 'blur' }
  ],
  responsibilities: [
    { max: 500, message: '職責描述長度不能超過 500 個字符', trigger: 'blur' }
  ]
}

// 計算屬性
const availableParentDepartments = computed(() => {
  return availableDepartments.value.filter(dept => 
    dept.id !== form.id && // 不能選擇自己作為父部門
    !isDescendant(dept.id, form.id) // 不能選擇自己的子部門作為父部門
  )
})

// 方法
const fetchDepartments = async () => {
  try {
    const response = await api.getDepartments()
    availableDepartments.value = response.data
  } catch (error) {
    console.error('獲取部門列表失敗:', error)
  }
}

const searchUsers = async (query: string) => {
  if (!query) {
    availableManagers.value = []
    return
  }

  userSearchLoading.value = true
  try {
    const response = await api.searchUsers(query)
    availableManagers.value = response.data
  } catch (error) {
    console.error('搜索用戶失敗:', error)
  } finally {
    userSearchLoading.value = false
  }
}

const isDescendant = (parentId: number, childId: number): boolean => {
  // 檢查 parentId 是否是 childId 的後代
  // 這裡需要實現樹形結構的遍歷邏輯
  // 簡化實現，實際應用中需要更完整的邏輯
  return false
}

const initForm = () => {
  if (props.department) {
    Object.assign(form, {
      name: props.department.name || '',
      description: props.department.description || '',
      parentId: props.department.parentId || null,
      managerId: props.department.manager?.id || null,
      active: props.department.active !== false,
      phone: props.department.phone || '',
      address: props.department.address || '',
      responsibilities: props.department.responsibilities || ''
    })

    // 如果有管理員，添加到可選列表中
    if (props.department.manager) {
      availableManagers.value = [props.department.manager]
    }
  }

  // 如果有父部門，設置為默認值
  if (props.parentDepartment) {
    form.parentId = props.parentDepartment.id
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    const submitData = {
      ...form,
      parentId: form.parentId || null,
      managerId: form.managerId || null
    }
    
    emit('submit', submitData)
  } catch (error) {
    console.error('表單驗證失敗:', error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}

// 監聽部門變化
watch(() => props.department, () => {
  initForm()
}, { immediate: true })

onMounted(() => {
  fetchDepartments()
  initForm()
})
</script>

<style scoped>
.department-form {
  padding: 20px 0;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #ebeef5;
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #303133;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}
</style>
