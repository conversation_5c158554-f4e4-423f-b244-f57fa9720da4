<template>
  <div class="department-detail">
    <div class="detail-header">
      <div class="header-info">
        <h2>{{ department.name }}</h2>
        <el-tag :type="department.active ? 'success' : 'danger'" size="large">
          {{ department.active ? '啟用' : '停用' }}
        </el-tag>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="handleEdit">
          <el-icon><Edit /></el-icon>
          編輯部門
        </el-button>
        <el-button @click="handleClose">
          <el-icon><Close /></el-icon>
          關閉
        </el-button>
      </div>
    </div>

    <el-tabs v-model="activeTab" type="border-card">
      <!-- 基本信息 -->
      <el-tab-pane label="基本信息" name="basic">
        <div class="basic-info">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="部門名稱">
              {{ department.name }}
            </el-descriptions-item>
            <el-descriptions-item label="部門狀態">
              <el-tag :type="department.active ? 'success' : 'danger'">
                {{ department.active ? '啟用' : '停用' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="上級部門">
              {{ department.parent?.name || '無' }}
            </el-descriptions-item>
            <el-descriptions-item label="部門主管">
              <span v-if="department.manager">
                {{ department.manager.firstName }} {{ department.manager.lastName }}
              </span>
              <span v-else class="no-data">未指定</span>
            </el-descriptions-item>
            <el-descriptions-item label="員工數量">
              <el-tag type="info">{{ department.employeeCount || 0 }}人</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="組織層級">
              <el-tag>L{{ department.level || 1 }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="聯繫電話">
              {{ department.phone || '未設置' }}
            </el-descriptions-item>
            <el-descriptions-item label="辦公地址">
              {{ department.address || '未設置' }}
            </el-descriptions-item>
            <el-descriptions-item label="創建時間" :span="2">
              {{ formatDate(department.createdAt) }}
            </el-descriptions-item>
            <el-descriptions-item label="最後更新" :span="2">
              {{ formatDate(department.updatedAt) }}
            </el-descriptions-item>
          </el-descriptions>

          <div v-if="department.description" class="description-section">
            <h4>部門描述</h4>
            <p class="description-text">{{ department.description }}</p>
          </div>

          <div v-if="department.responsibilities" class="responsibilities-section">
            <h4>部門職責</h4>
            <p class="responsibilities-text">{{ department.responsibilities }}</p>
          </div>
        </div>
      </el-tab-pane>

      <!-- 組織架構 -->
      <el-tab-pane label="組織架構" name="structure">
        <div class="structure-info">
          <!-- 上級部門 -->
          <div v-if="department.parent" class="parent-section">
            <h4>上級部門</h4>
            <el-card class="dept-card parent-card">
              <div class="dept-info">
                <el-icon class="dept-icon"><OfficeBuilding /></el-icon>
                <div class="dept-details">
                  <h5>{{ department.parent.name }}</h5>
                  <p>{{ department.parent.description || '無描述' }}</p>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 當前部門 -->
          <div class="current-section">
            <h4>當前部門</h4>
            <el-card class="dept-card current-card">
              <div class="dept-info">
                <el-icon class="dept-icon"><Collection /></el-icon>
                <div class="dept-details">
                  <h5>{{ department.name }}</h5>
                  <p>{{ department.description || '無描述' }}</p>
                  <div class="dept-meta">
                    <span>員工: {{ department.employeeCount || 0 }}人</span>
                    <span v-if="department.manager">
                      主管: {{ department.manager.firstName }} {{ department.manager.lastName }}
                    </span>
                  </div>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 子部門 -->
          <div v-if="childDepartments.length > 0" class="children-section">
            <h4>子部門 ({{ childDepartments.length }})</h4>
            <div class="children-grid">
              <el-card
                v-for="child in childDepartments"
                :key="child.id"
                class="dept-card child-card"
                @click="viewChildDepartment(child)"
              >
                <div class="dept-info">
                  <el-icon class="dept-icon"><Collection /></el-icon>
                  <div class="dept-details">
                    <h5>{{ child.name }}</h5>
                    <p>{{ child.description || '無描述' }}</p>
                    <div class="dept-meta">
                      <span>員工: {{ child.employeeCount || 0 }}人</span>
                      <el-tag :type="child.active ? 'success' : 'danger'" size="small">
                        {{ child.active ? '啟用' : '停用' }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 部門成員 -->
      <el-tab-pane label="部門成員" name="members">
        <div class="members-info">
          <div class="members-header">
            <h4>部門成員 ({{ departmentMembers.length }})</h4>
            <el-button type="primary" size="small" @click="manageMembersDialog = true">
              管理成員
            </el-button>
          </div>

          <el-table :data="departmentMembers" v-loading="membersLoading">
            <el-table-column label="頭像" width="80">
              <template #default="scope">
                <el-avatar :size="40" :src="scope.row.avatar">
                  {{ scope.row.firstName?.charAt(0) }}{{ scope.row.lastName?.charAt(0) }}
                </el-avatar>
              </template>
            </el-table-column>
            <el-table-column prop="username" label="用戶名" />
            <el-table-column label="姓名">
              <template #default="scope">
                {{ scope.row.firstName }} {{ scope.row.lastName }}
              </template>
            </el-table-column>
            <el-table-column prop="email" label="郵箱" />
            <el-table-column label="角色">
              <template #default="scope">
                <el-tag
                  v-for="role in scope.row.roles"
                  :key="role.id"
                  size="small"
                  style="margin-right: 4px"
                >
                  {{ getRoleLabel(role.name) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="狀態" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.enabled ? 'success' : 'danger'" size="small">
                  {{ scope.row.enabled ? '啟用' : '停用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="scope">
                <el-button size="small" @click="viewUser(scope.row.id)">
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <!-- 統計信息 -->
      <el-tab-pane label="統計信息" name="statistics">
        <div class="statistics-info">
          <div class="stats-grid">
            <el-card class="stat-card">
              <el-statistic title="總員工數" :value="department.employeeCount || 0" />
            </el-card>
            <el-card class="stat-card">
              <el-statistic title="子部門數" :value="childDepartments.length" />
            </el-card>
            <el-card class="stat-card">
              <el-statistic title="組織層級" :value="department.level || 1" />
            </el-card>
            <el-card class="stat-card">
              <el-statistic title="運行天數" :value="getDaysFromCreation()" />
            </el-card>
          </div>

          <!-- 成員角色分布 -->
          <div class="role-distribution">
            <h4>成員角色分布</h4>
            <div class="role-stats">
              <div
                v-for="(count, role) in roleDistribution"
                :key="role"
                class="role-stat-item"
              >
                <el-tag size="large">{{ getRoleLabel(role) }}</el-tag>
                <span class="role-count">{{ count }}人</span>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 成員管理對話框 -->
    <el-dialog v-model="manageMembersDialog" title="管理部門成員" width="600px">
      <div class="member-management">
        <p>成員管理功能開發中...</p>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Edit, Close, OfficeBuilding, Collection } from '@element-plus/icons-vue'
import api from '../../services/api'

interface Props {
  department: any
}

const props = defineProps<Props>()

const emit = defineEmits<{
  edit: [department: any]
  close: []
}>()

const router = useRouter()

// 響應式數據
const activeTab = ref('basic')
const childDepartments = ref<any[]>([])
const departmentMembers = ref<any[]>([])
const membersLoading = ref(false)
const manageMembersDialog = ref(false)

// 計算屬性
const roleDistribution = computed(() => {
  const distribution: Record<string, number> = {}
  departmentMembers.value.forEach(member => {
    member.roles?.forEach((role: any) => {
      distribution[role.name] = (distribution[role.name] || 0) + 1
    })
  })
  return distribution
})

// 方法
const fetchChildDepartments = async () => {
  try {
    const response = await api.getChildDepartments(props.department.id)
    childDepartments.value = response.data
  } catch (error) {
    console.error('獲取子部門失敗:', error)
  }
}

const fetchDepartmentMembers = async () => {
  membersLoading.value = true
  try {
    const response = await api.getDepartmentUsers(props.department.id)
    departmentMembers.value = response.data
  } catch (error) {
    console.error('獲取部門成員失敗:', error)
  } finally {
    membersLoading.value = false
  }
}

const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getDaysFromCreation = () => {
  if (!props.department.createdAt) return 0
  const created = new Date(props.department.createdAt)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - created.getTime())
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

const getRoleLabel = (roleName: string) => {
  const roleMap: Record<string, string> = {
    'SUPER_ADMIN': '超級管理員',
    'ADMIN': '管理員',
    'DEPARTMENT_HEAD': '部門主管',
    'PROJECT_MANAGER': '專案經理',
    'EMPLOYEE': '員工'
  }
  return roleMap[roleName] || roleName
}

const viewChildDepartment = (child: any) => {
  // 可以發射事件或導航到子部門詳情
  console.log('查看子部門:', child)
}

const viewUser = (userId: number) => {
  router.push({ name: 'user-detail', params: { id: userId } })
}

const handleEdit = () => {
  emit('edit', props.department)
}

const handleClose = () => {
  emit('close')
}

onMounted(() => {
  fetchChildDepartments()
  fetchDepartmentMembers()
})
</script>

<style scoped>
.department-detail {
  padding: 20px 0;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.header-info h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.basic-info {
  padding: 16px 0;
}

.description-section,
.responsibilities-section {
  margin-top: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.description-section h4,
.responsibilities-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.description-text,
.responsibilities-text {
  margin: 0;
  line-height: 1.6;
  color: #606266;
}

.structure-info {
  padding: 16px 0;
}

.parent-section,
.current-section,
.children-section {
  margin-bottom: 24px;
}

.parent-section h4,
.current-section h4,
.children-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.dept-card {
  cursor: pointer;
  transition: all 0.3s;
}

.dept-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.parent-card {
  border-left: 4px solid #409eff;
}

.current-card {
  border-left: 4px solid #67c23a;
}

.child-card {
  border-left: 4px solid #e6a23c;
}

.dept-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.dept-icon {
  font-size: 24px;
  color: #409eff;
  margin-top: 4px;
}

.dept-details h5 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.dept-details p {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 14px;
}

.dept-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #909399;
}

.children-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.members-info {
  padding: 16px 0;
}

.members-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.members-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.statistics-info {
  padding: 16px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
}

.role-distribution {
  margin-top: 24px;
}

.role-distribution h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
}

.role-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.role-stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.role-count {
  font-weight: 600;
  color: #303133;
}

.no-data {
  color: #c0c4cc;
  font-style: italic;
}

.member-management {
  text-align: center;
  padding: 40px;
  color: #909399;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
}

:deep(.el-tab-pane) {
  min-height: 400px;
}
</style>
