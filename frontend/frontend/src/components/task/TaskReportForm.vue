<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="120px"
    @submit.prevent="handleSubmit"
  >
    <el-form-item label="任務" prop="taskId">
      <el-select
        v-model="form.taskId"
        placeholder="請選擇任務"
        filterable
        style="width: 100%"
        :loading="tasksLoading"
      >
        <el-option
          v-for="task in tasks"
          :key="task.id"
          :label="task.name"
          :value="task.id"
        />
      </el-select>
    </el-form-item>

    <el-form-item label="報告內容" prop="content">
      <el-input
        v-model="form.content"
        type="textarea"
        :rows="4"
        placeholder="請輸入報告內容"
        maxlength="1000"
        show-word-limit
      />
    </el-form-item>

    <el-form-item label="進度更新" prop="progressUpdate">
      <el-slider
        v-model="form.progressUpdate"
        :min="0"
        :max="100"
        :step="5"
        show-input
        :format-tooltip="formatTooltip"
      />
    </el-form-item>

    <el-form-item label="問題與風險" prop="issues">
      <el-input
        v-model="form.issues"
        type="textarea"
        :rows="3"
        placeholder="請描述遇到的問題或風險（可選）"
        maxlength="500"
        show-word-limit
      />
    </el-form-item>

    <el-form-item label="報告日期" prop="reportDate">
      <el-date-picker
        v-model="form.reportDate"
        type="date"
        placeholder="選擇報告日期"
        style="width: 100%"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
      />
    </el-form-item>

    <el-form-item>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        {{ isEdit ? '更新' : '創建' }}
      </el-button>
      <el-button @click="handleCancel">取消</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useTaskReportStore, type TaskReport } from '../../stores/taskReport'
import api from '../../services/api'

interface Props {
  taskReport?: TaskReport | null
  isEdit?: boolean
}

interface Emits {
  (e: 'success', taskReport: TaskReport): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  taskReport: null,
  isEdit: false
})

const emit = defineEmits<Emits>()

const taskReportStore = useTaskReportStore()
const formRef = ref<FormInstance>()
const loading = ref(false)
const tasksLoading = ref(false)
const tasks = ref<any[]>([])

const form = reactive({
  taskId: '',
  content: '',
  progressUpdate: 0,
  issues: '',
  reportDate: new Date().toISOString().split('T')[0]
})

const rules: FormRules = {
  taskId: [
    { required: true, message: '請選擇任務', trigger: 'change' }
  ],
  content: [
    { required: true, message: '請輸入報告內容', trigger: 'blur' },
    { min: 10, message: '報告內容至少需要10個字符', trigger: 'blur' }
  ],
  progressUpdate: [
    { required: true, message: '請設置進度更新', trigger: 'change' }
  ],
  reportDate: [
    { required: true, message: '請選擇報告日期', trigger: 'change' }
  ]
}

const formatTooltip = (value: number) => `${value}%`

const fetchTasks = async () => {
  tasksLoading.value = true
  try {
    const response = await api.getTasks()
    tasks.value = response.data
  } catch (error) {
    ElMessage.error('獲取任務列表失敗')
  } finally {
    tasksLoading.value = false
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    const taskReportData = {
      task: { id: Number(form.taskId) },
      content: form.content,
      progressUpdate: form.progressUpdate,
      issues: form.issues || undefined,
      reportDate: form.reportDate
    }

    let result: TaskReport
    if (props.isEdit && props.taskReport?.id) {
      result = await taskReportStore.updateTaskReport(props.taskReport.id, taskReportData)
    } else {
      result = await taskReportStore.createTaskReport(taskReportData)
    }

    emit('success', result)
  } catch (error) {
    console.error('提交任務報告失敗:', error)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.taskId = ''
  form.content = ''
  form.progressUpdate = 0
  form.issues = ''
  form.reportDate = new Date().toISOString().split('T')[0]
}

// Watch for taskReport changes to populate form
watch(() => props.taskReport, (newTaskReport) => {
  if (newTaskReport && props.isEdit) {
    form.taskId = newTaskReport.task.id.toString()
    form.content = newTaskReport.content
    form.progressUpdate = newTaskReport.progressUpdate
    form.issues = newTaskReport.issues || ''
    form.reportDate = newTaskReport.reportDate
  } else {
    resetForm()
  }
}, { immediate: true })

onMounted(() => {
  fetchTasks()
})

defineExpose({
  resetForm
})
</script>

<style scoped>
.el-form {
  max-width: 600px;
}
</style>
