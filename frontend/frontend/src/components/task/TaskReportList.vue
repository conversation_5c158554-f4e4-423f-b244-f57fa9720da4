<template>
  <div class="task-report-list">
    <!-- 搜索和篩選 -->
    <div class="filter-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-select
            v-model="filters.taskId"
            placeholder="篩選任務"
            clearable
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="task in tasks"
              :key="task.id"
              :label="task.name"
              :value="task.id"
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="開始日期"
            end-placeholder="結束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-col>
        <el-col :span="6" class="text-right">
          <el-button type="primary" @click="handleCreate">
            <el-icon><Plus /></el-icon>
            新增報告
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 報告列表 -->
    <el-table
      :data="taskReports"
      v-loading="loading"
      stripe
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="80" />
      
      <el-table-column label="任務" min-width="150">
        <template #default="{ row }">
          <el-tag type="info">{{ row.task.name }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="報告人" min-width="120">
        <template #default="{ row }">
          {{ row.user.firstName }} {{ row.user.lastName }}
        </template>
      </el-table-column>

      <el-table-column label="進度" width="100">
        <template #default="{ row }">
          <el-progress
            :percentage="row.progressUpdate"
            :color="getProgressColor(row.progressUpdate)"
            :stroke-width="8"
          />
        </template>
      </el-table-column>

      <el-table-column label="報告內容" min-width="200">
        <template #default="{ row }">
          <el-tooltip :content="row.content" placement="top">
            <span class="content-preview">{{ truncateText(row.content, 50) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column label="問題" min-width="150">
        <template #default="{ row }">
          <el-tooltip v-if="row.issues" :content="row.issues" placement="top">
            <el-tag type="warning" size="small">
              {{ truncateText(row.issues, 30) }}
            </el-tag>
          </el-tooltip>
          <span v-else class="text-muted">無</span>
        </template>
      </el-table-column>

      <el-table-column prop="reportDate" label="報告日期" width="120" />

      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="handleView(row)"
          >
            查看
          </el-button>
          <el-button
            type="warning"
            size="small"
            @click="handleEdit(row)"
          >
            編輯
          </el-button>
          <el-popconfirm
            title="確定要刪除這個任務報告嗎？"
            @confirm="handleDelete(row.id)"
          >
            <template #reference>
              <el-button
                type="danger"
                size="small"
              >
                刪除
              </el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分頁 -->
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 查看詳情對話框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="任務報告詳情"
      width="600px"
    >
      <div v-if="selectedTaskReport" class="report-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任務">
            {{ selectedTaskReport.task.name }}
          </el-descriptions-item>
          <el-descriptions-item label="報告人">
            {{ selectedTaskReport.user.firstName }} {{ selectedTaskReport.user.lastName }}
          </el-descriptions-item>
          <el-descriptions-item label="進度">
            <el-progress
              :percentage="selectedTaskReport.progressUpdate"
              :color="getProgressColor(selectedTaskReport.progressUpdate)"
            />
          </el-descriptions-item>
          <el-descriptions-item label="報告日期">
            {{ selectedTaskReport.reportDate }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="content-section">
          <h4>報告內容</h4>
          <p>{{ selectedTaskReport.content }}</p>
        </div>
        
        <div v-if="selectedTaskReport.issues" class="issues-section">
          <h4>問題與風險</h4>
          <p>{{ selectedTaskReport.issues }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { Search, Refresh, Plus } from '@element-plus/icons-vue'
import { useTaskReportStore, type TaskReport } from '../../stores/taskReport'
import api from '../../services/api'

interface Emits {
  (e: 'create'): void
  (e: 'edit', taskReport: TaskReport): void
}

const emit = defineEmits<Emits>()

const taskReportStore = useTaskReportStore()
const loading = ref(false)
const tasks = ref<any[]>([])
const viewDialogVisible = ref(false)
const selectedTaskReport = ref<TaskReport | null>(null)

const filters = reactive({
  taskId: '',
  dateRange: [] as string[]
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const taskReports = computed(() => taskReportStore.taskReports)

const getProgressColor = (percentage: number) => {
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

const truncateText = (text: string, maxLength: number) => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const fetchTasks = async () => {
  try {
    const response = await api.getTasks()
    tasks.value = response.data
  } catch (error) {
    console.error('獲取任務列表失敗:', error)
  }
}

const fetchTaskReports = async () => {
  loading.value = true
  try {
    await taskReportStore.fetchTaskReports()
    pagination.total = taskReports.value.length
  } catch (error) {
    console.error('獲取任務報告失敗:', error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  // 實現搜索邏輯
  fetchTaskReports()
}

const handleReset = () => {
  filters.taskId = ''
  filters.dateRange = []
  fetchTaskReports()
}

const handleCreate = () => {
  emit('create')
}

const handleView = (taskReport: TaskReport) => {
  selectedTaskReport.value = taskReport
  viewDialogVisible.value = true
}

const handleEdit = (taskReport: TaskReport) => {
  emit('edit', taskReport)
}

const handleDelete = async (id: number) => {
  try {
    await taskReportStore.deleteTaskReport(id)
    fetchTaskReports()
  } catch (error) {
    console.error('刪除任務報告失敗:', error)
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  fetchTaskReports()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  fetchTaskReports()
}

onMounted(() => {
  fetchTasks()
  fetchTaskReports()
})
</script>

<style scoped>
.task-report-list {
  padding: 20px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
}

.text-right {
  text-align: right;
}

.content-preview {
  cursor: pointer;
}

.text-muted {
  color: #999;
}

.pagination-section {
  margin-top: 20px;
  text-align: center;
}

.report-detail {
  padding: 20px 0;
}

.content-section,
.issues-section {
  margin-top: 20px;
}

.content-section h4,
.issues-section h4 {
  margin-bottom: 10px;
  color: #333;
}

.content-section p,
.issues-section p {
  line-height: 1.6;
  color: #666;
}
</style>
