<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="120px"
    @submit.prevent="handleSubmit"
  >
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="任務名稱" prop="name">
          <el-input v-model="form.name" placeholder="請輸入任務名稱" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="所屬專案" prop="projectId">
          <el-select
            v-model="form.projectId"
            placeholder="請選擇專案"
            filterable
            style="width: 100%"
            :loading="projectsLoading"
          >
            <el-option
              v-for="project in projects"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item label="任務描述" prop="description">
      <el-input
        v-model="form.description"
        type="textarea"
        :rows="4"
        placeholder="請輸入任務描述"
        maxlength="500"
        show-word-limit
      />
    </el-form-item>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="開始日期" prop="startDate">
          <el-date-picker
            v-model="form.startDate"
            type="date"
            placeholder="選擇開始日期"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="截止日期" prop="dueDate">
          <el-date-picker
            v-model="form.dueDate"
            type="date"
            placeholder="選擇截止日期"
            style="width: 100%"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item label="優先度" prop="priority">
          <el-select v-model="form.priority" placeholder="選擇優先度" style="width: 100%">
            <el-option label="低" value="LOW" />
            <el-option label="中" value="MEDIUM" />
            <el-option label="高" value="HIGH" />
            <el-option label="緊急" value="URGENT" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="狀態" prop="status">
          <el-select v-model="form.status" placeholder="選擇狀態" style="width: 100%">
            <el-option label="待處理" value="PENDING" />
            <el-option label="進行中" value="IN_PROGRESS" />
            <el-option label="已完成" value="COMPLETED" />
            <el-option label="已取消" value="CANCELLED" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="進度" prop="progress">
          <el-slider
            v-model="form.progress"
            :min="0"
            :max="100"
            :step="5"
            show-input
            :format-tooltip="formatTooltip"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item label="負責人" prop="assigneeId">
          <el-select
            v-model="form.assigneeId"
            placeholder="選擇負責人"
            filterable
            clearable
            style="width: 100%"
            :loading="usersLoading"
          >
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="`${user.firstName} ${user.lastName}`"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="父任務" prop="parentId">
          <el-select
            v-model="form.parentId"
            placeholder="選擇父任務（可選）"
            filterable
            clearable
            style="width: 100%"
            :loading="tasksLoading"
          >
            <el-option
              v-for="task in availableTasks"
              :key="task.id"
              :label="task.name"
              :value="task.id"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        {{ submitButtonText }}
      </el-button>
      <el-button @click="handleCancel">取消</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import api from '../../services/api'

interface Props {
  taskId?: number
  submitButtonText?: string
}

interface Emits {
  (e: 'submit-success', task: any): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  submitButtonText: '創建任務'
})

const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)
const projectsLoading = ref(false)
const usersLoading = ref(false)
const tasksLoading = ref(false)

const projects = ref([])
const users = ref([])
const tasks = ref([])

const form = reactive({
  name: '',
  description: '',
  projectId: '',
  startDate: '',
  dueDate: '',
  priority: 'MEDIUM',
  status: 'PENDING',
  progress: 0,
  assigneeId: '',
  parentId: ''
})

const rules: FormRules = {
  name: [
    { required: true, message: '請輸入任務名稱', trigger: 'blur' },
    { min: 2, max: 100, message: '任務名稱長度在 2 到 100 個字符', trigger: 'blur' }
  ],
  projectId: [
    { required: true, message: '請選擇所屬專案', trigger: 'change' }
  ],
  description: [
    { max: 500, message: '描述不能超過 500 個字符', trigger: 'blur' }
  ],
  startDate: [
    { required: true, message: '請選擇開始日期', trigger: 'change' }
  ],
  dueDate: [
    { required: true, message: '請選擇截止日期', trigger: 'change' }
  ],
  priority: [
    { required: true, message: '請選擇優先度', trigger: 'change' }
  ],
  status: [
    { required: true, message: '請選擇狀態', trigger: 'change' }
  ]
}

// Computed
const availableTasks = computed(() => {
  return tasks.value.filter(task => task.id !== props.taskId)
})

// Methods
const fetchProjects = async () => {
  projectsLoading.value = true
  try {
    const response = await api.getProjects()
    projects.value = response.data
  } catch (error) {
    console.error('Failed to fetch projects:', error)
  } finally {
    projectsLoading.value = false
  }
}

const fetchUsers = async () => {
  usersLoading.value = true
  try {
    const response = await api.getUsers()
    users.value = response.data
  } catch (error) {
    console.error('Failed to fetch users:', error)
  } finally {
    usersLoading.value = false
  }
}

const fetchTasks = async () => {
  tasksLoading.value = true
  try {
    const response = await api.getTasks()
    tasks.value = response.data
  } catch (error) {
    console.error('Failed to fetch tasks:', error)
  } finally {
    tasksLoading.value = false
  }
}

const fetchTaskData = async () => {
  if (!props.taskId) return
  
  try {
    const response = await api.getTask(props.taskId)
    const task = response.data
    
    Object.assign(form, {
      name: task.name,
      description: task.description || '',
      projectId: task.project?.id || '',
      startDate: task.startDate || '',
      dueDate: task.dueDate || '',
      priority: task.priority || 'MEDIUM',
      status: task.status || 'PENDING',
      progress: task.progress || 0,
      assigneeId: task.assignee?.id || '',
      parentId: task.parent?.id || ''
    })
  } catch (error) {
    console.error('Failed to fetch task data:', error)
    ElMessage.error('獲取任務資料失敗')
  }
}

const formatTooltip = (value: number) => `${value}%`

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    const taskData = {
      name: form.name,
      description: form.description,
      project: { id: Number(form.projectId) },
      startDate: form.startDate,
      dueDate: form.dueDate,
      priority: form.priority,
      status: form.status,
      progress: form.progress,
      assignee: form.assigneeId ? { id: Number(form.assigneeId) } : null,
      parent: form.parentId ? { id: Number(form.parentId) } : null
    }

    let result
    if (props.taskId) {
      result = await api.updateTask(props.taskId, taskData)
    } else {
      result = await api.createTask(taskData)
    }

    ElMessage.success(props.taskId ? '任務更新成功！' : '任務創建成功！')
    emit('submit-success', result.data)
  } catch (error) {
    console.error('Failed to submit task:', error)
    ElMessage.error(props.taskId ? '任務更新失敗' : '任務創建失敗')
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}

// Watch for date validation
watch([() => form.startDate, () => form.dueDate], ([startDate, dueDate]) => {
  if (startDate && dueDate && new Date(startDate) > new Date(dueDate)) {
    ElMessage.warning('開始日期不能晚於截止日期')
  }
})

onMounted(() => {
  fetchProjects()
  fetchUsers()
  fetchTasks()
  if (props.taskId) {
    fetchTaskData()
  }
})
</script>

<style scoped>
.el-form {
  max-width: 800px;
}
</style>
