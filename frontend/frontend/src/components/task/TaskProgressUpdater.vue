<template>
  <div class="task-progress-updater">
    <el-card class="progress-card">
      <template #header>
        <div class="card-header">
          <span>進度更新</span>
          <el-button
            type="primary"
            size="small"
            @click="saveProgress"
            :loading="saving"
            :disabled="!hasChanges"
          >
            保存進度
          </el-button>
        </div>
      </template>

      <div class="progress-content">
        <!-- 當前進度顯示 -->
        <div class="current-progress">
          <div class="progress-label">
            <span>當前進度</span>
            <span class="progress-value">{{ currentProgress }}%</span>
          </div>
          <el-progress
            :percentage="currentProgress"
            :color="getProgressColor(currentProgress)"
            :stroke-width="12"
            :show-text="false"
          />
        </div>

        <!-- 進度調整器 -->
        <div class="progress-adjuster">
          <div class="adjuster-label">調整進度</div>
          <el-slider
            v-model="newProgress"
            :min="0"
            :max="100"
            :step="5"
            :marks="progressMarks"
            show-input
            :format-tooltip="formatTooltip"
            @change="onProgressChange"
          />
        </div>

        <!-- 快速設置按鈕 -->
        <div class="quick-actions">
          <div class="quick-label">快速設置</div>
          <div class="quick-buttons">
            <el-button
              v-for="preset in progressPresets"
              :key="preset.value"
              size="small"
              :type="preset.type"
              @click="setProgress(preset.value)"
            >
              {{ preset.label }}
            </el-button>
          </div>
        </div>

        <!-- 狀態自動更新 -->
        <div class="status-update">
          <el-checkbox
            v-model="autoUpdateStatus"
            :disabled="saving"
          >
            根據進度自動更新任務狀態
          </el-checkbox>
          <div class="status-hint">
            <el-text size="small" type="info">
              0%: 未開始 | 1-99%: 進行中 | 100%: 已完成
            </el-text>
          </div>
        </div>

        <!-- 進度變化預覽 -->
        <div v-if="hasChanges" class="progress-preview">
          <el-alert
            :title="getChangeMessage()"
            :type="getChangeType()"
            :closable="false"
            show-icon
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import api from '../../services/api'

interface Props {
  taskId: number
  initialProgress: number
}

interface Emits {
  (e: 'progress-updated', progress: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const saving = ref(false)
const currentProgress = ref(props.initialProgress || 0)
const newProgress = ref(props.initialProgress || 0)
const autoUpdateStatus = ref(true)

// 進度預設值
const progressPresets = [
  { value: 0, label: '0%', type: 'info' },
  { value: 25, label: '25%', type: 'warning' },
  { value: 50, label: '50%', type: 'warning' },
  { value: 75, label: '75%', type: 'success' },
  { value: 100, label: '100%', type: 'success' }
]

// 進度標記
const progressMarks = {
  0: '未開始',
  25: '1/4',
  50: '一半',
  75: '3/4',
  100: '完成'
}

// 計算屬性
const hasChanges = computed(() => {
  return currentProgress.value !== newProgress.value
})

// 監聽初始進度變化
watch(() => props.initialProgress, (newVal) => {
  currentProgress.value = newVal || 0
  newProgress.value = newVal || 0
}, { immediate: true })

// 方法
const formatTooltip = (value: number) => {
  return `${value}%`
}

const getProgressColor = (percentage: number) => {
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

const setProgress = (value: number) => {
  newProgress.value = value
}

const onProgressChange = (value: number) => {
  // 進度變化時的處理
  console.log('Progress changed to:', value)
}

const getChangeMessage = () => {
  const diff = newProgress.value - currentProgress.value
  if (diff > 0) {
    return `進度將從 ${currentProgress.value}% 增加到 ${newProgress.value}% (+${diff}%)`
  } else {
    return `進度將從 ${currentProgress.value}% 減少到 ${newProgress.value}% (${diff}%)`
  }
}

const getChangeType = () => {
  const diff = newProgress.value - currentProgress.value
  if (diff > 0) return 'success'
  if (diff < 0) return 'warning'
  return 'info'
}

const saveProgress = async () => {
  if (!hasChanges.value) return

  saving.value = true
  try {
    // 更新進度
    await api.updateTaskProgress(props.taskId, newProgress.value)

    // 如果啟用自動更新狀態
    if (autoUpdateStatus.value) {
      let newStatus = ''
      if (newProgress.value === 0) {
        newStatus = 'NOT_STARTED'
      } else if (newProgress.value === 100) {
        newStatus = 'COMPLETED'
      } else {
        newStatus = 'IN_PROGRESS'
      }

      try {
        await api.updateTaskStatus(props.taskId, newStatus)
      } catch (error) {
        console.warn('Failed to update task status:', error)
      }
    }

    // 更新當前進度
    currentProgress.value = newProgress.value

    // 發送事件
    emit('progress-updated', newProgress.value)

    ElMessage.success('進度更新成功')
  } catch (error) {
    console.error('Failed to update progress:', error)
    ElMessage.error('進度更新失敗')
  } finally {
    saving.value = false
  }
}
</script>

<style scoped>
.task-progress-updater {
  width: 100%;
}

.progress-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-content {
  padding: 10px 0;
}

.current-progress {
  margin-bottom: 30px;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-weight: 500;
}

.progress-value {
  color: #409eff;
  font-weight: bold;
}

.progress-adjuster {
  margin-bottom: 30px;
}

.adjuster-label {
  margin-bottom: 15px;
  font-weight: 500;
  color: #303133;
}

.quick-actions {
  margin-bottom: 25px;
}

.quick-label {
  margin-bottom: 10px;
  font-weight: 500;
  color: #303133;
}

.quick-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.status-update {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.status-hint {
  margin-top: 8px;
}

.progress-preview {
  margin-top: 15px;
}

:deep(.el-slider__marks-text) {
  font-size: 12px;
  color: #909399;
}

:deep(.el-slider__button) {
  border: 2px solid #409eff;
}

:deep(.el-slider__bar) {
  background: linear-gradient(90deg, #f56c6c 0%, #e6a23c 50%, #67c23a 100%);
}
</style>
