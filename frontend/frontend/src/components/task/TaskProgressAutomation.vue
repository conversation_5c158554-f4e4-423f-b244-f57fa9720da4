<template>
  <div class="task-progress-automation">
    <el-card class="automation-card">
      <template #header>
        <div class="card-header">
          <span>任務進度自動化</span>
          <el-button 
            type="text" 
            size="small" 
            @click="refreshAnalysis"
            :loading="loading"
          >
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>
      </template>

      <div v-loading="loading" class="automation-content">
        <!-- 自動化說明 -->
        <div class="automation-explanation">
          <h4>自動化計算方式</h4>
          <div class="explanation-items">
            <div class="explanation-item">
              <el-icon class="item-icon"><TrendCharts /></el-icon>
              <div class="item-content">
                <div class="item-title">時間進度計算</div>
                <div class="item-description">根據任務開始日期和截止日期計算時間進度</div>
              </div>
            </div>
            <div class="explanation-item">
              <el-icon class="item-icon"><Document /></el-icon>
              <div class="item-content">
                <div class="item-title">報告進度整合</div>
                <div class="item-description">結合任務報告中的進度更新進行智能調整</div>
              </div>
            </div>
            <div class="explanation-item">
              <el-icon class="item-icon"><Clock /></el-icon>
              <div class="item-content">
                <div class="item-title">即時更新</div>
                <div class="item-description">任務狀態變更時自動重新計算進度</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 進度分析 -->
        <div v-if="analysis" class="progress-analysis">
          <h4>進度分析</h4>
          
          <div class="analysis-grid">
            <div class="analysis-item">
              <div class="analysis-label">當前進度</div>
              <div class="analysis-value">
                <el-progress 
                  :percentage="analysis.currentProgress" 
                  :stroke-width="8"
                  :format="formatProgress"
                />
              </div>
            </div>
            
            <div class="analysis-item">
              <div class="analysis-label">時間進度</div>
              <div class="analysis-value">
                <el-progress 
                  :percentage="analysis.timeBasedProgress" 
                  :stroke-width="8"
                  :format="formatProgress"
                  color="#e6a23c"
                />
              </div>
            </div>
            
            <div class="analysis-item">
              <div class="analysis-label">進度狀態</div>
              <div class="analysis-value">
                <el-tag :type="getProgressStatusType(analysis.progressStatus)">
                  {{ getProgressStatusLabel(analysis.progressStatus) }}
                </el-tag>
              </div>
            </div>
            
            <div class="analysis-item">
              <div class="analysis-label">進度差異</div>
              <div class="analysis-value">
                <span :class="getProgressDifferenceClass(analysis.progressDifference)">
                  {{ formatProgressDifference(analysis.progressDifference) }}
                </span>
              </div>
            </div>
          </div>

          <!-- 任務報告統計 -->
          <div class="reports-stats">
            <h5>任務報告統計</h5>
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-value">{{ analysis.totalReports }}</div>
                <div class="stat-label">總報告數</div>
              </div>
              <div class="stat-item" v-if="analysis.lastReportDate">
                <div class="stat-value">{{ formatDate(analysis.lastReportDate) }}</div>
                <div class="stat-label">最後報告</div>
              </div>
              <div class="stat-item" v-if="analysis.lastReportedProgress !== undefined">
                <div class="stat-value">{{ analysis.lastReportedProgress }}%</div>
                <div class="stat-label">最後報告進度</div>
              </div>
              <div class="stat-item" v-if="analysis.recentReports">
                <div class="stat-value">{{ analysis.recentReports }}</div>
                <div class="stat-label">近期報告</div>
              </div>
            </div>
          </div>

          <!-- 自動化建議 -->
          <div v-if="analysis.recommendations && analysis.recommendations.length > 0" class="recommendations">
            <h5>自動化建議</h5>
            <div class="recommendation-list">
              <div 
                v-for="(recommendation, index) in analysis.recommendations" 
                :key="index"
                class="recommendation-item"
              >
                <el-icon class="recommendation-icon"><Warning /></el-icon>
                <span>{{ recommendation }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按鈕 -->
        <div class="automation-actions">
          <el-button 
            type="primary" 
            @click="recalculateProgress"
            :loading="recalculating"
          >
            <el-icon><Refresh /></el-icon>
            重新計算進度
          </el-button>
          <el-button @click="viewProgressHistory">
            <el-icon><Clock /></el-icon>
            查看進度歷史
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Refresh, 
  TrendCharts, 
  Document, 
  Clock, 
  Warning 
} from '@element-plus/icons-vue'
import { useDateFormat, useProgressStatus } from '../../composables/useI18n'
import api from '../../services/api'

interface Props {
  taskId: number
}

const props = defineProps<Props>()

const loading = ref(false)
const recalculating = ref(false)
const analysis = ref<any>(null)

const { formatDate } = useDateFormat()
const { getProgressStatusLabel, getProgressStatusType } = useProgressStatus()

// 方法
const fetchAnalysis = async () => {
  loading.value = true
  try {
    const response = await api.get(`/tasks/${props.taskId}/progress-analysis`)
    analysis.value = response.data
  } catch (error) {
    console.error('Failed to fetch progress analysis:', error)
    ElMessage.error('獲取進度分析失敗')
  } finally {
    loading.value = false
  }
}

const refreshAnalysis = () => {
  fetchAnalysis()
}

const recalculateProgress = async () => {
  recalculating.value = true
  try {
    await api.post(`/tasks/${props.taskId}/calculate-progress`)
    ElMessage.success('進度重新計算成功')
    await fetchAnalysis() // 重新獲取分析數據
  } catch (error) {
    console.error('Failed to recalculate progress:', error)
    ElMessage.error('進度重新計算失敗')
  } finally {
    recalculating.value = false
  }
}

const viewProgressHistory = () => {
  // 這裡可以打開進度歷史對話框或跳轉到歷史頁面
  ElMessage.info('進度歷史功能開發中')
}

const formatProgress = (percentage: number) => `${percentage}%`

const formatProgressDifference = (difference: number) => {
  if (difference > 0) {
    return `+${difference}%`
  } else if (difference < 0) {
    return `${difference}%`
  } else {
    return '0%'
  }
}

const getProgressDifferenceClass = (difference: number) => {
  if (difference > 10) {
    return 'progress-ahead'
  } else if (difference < -10) {
    return 'progress-behind'
  } else {
    return 'progress-normal'
  }
}

onMounted(() => {
  fetchAnalysis()
})
</script>

<style scoped>
.task-progress-automation {
  width: 100%;
}

.automation-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.automation-content {
  padding: 10px 0;
}

.automation-explanation {
  margin-bottom: 25px;
}

.automation-explanation h4 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #303133;
}

.explanation-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.explanation-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.item-icon {
  font-size: 18px;
  color: #409eff;
  margin-top: 2px;
}

.item-content {
  flex: 1;
}

.item-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.item-description {
  font-size: 12px;
  color: #606266;
  line-height: 1.4;
}

.progress-analysis h4,
.reports-stats h5,
.recommendations h5 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #303133;
}

.analysis-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 25px;
}

.analysis-item {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.analysis-label {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
}

.analysis-value {
  font-size: 14px;
  color: #303133;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
  margin-bottom: 25px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 11px;
  color: #606266;
}

.recommendation-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.recommendation-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 10px;
  background-color: #fef0e6;
  border-radius: 4px;
  border-left: 3px solid #e6a23c;
  font-size: 13px;
  line-height: 1.4;
}

.recommendation-icon {
  font-size: 14px;
  color: #e6a23c;
  margin-top: 1px;
}

.automation-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.progress-ahead {
  color: #67c23a;
  font-weight: bold;
}

.progress-behind {
  color: #f56c6c;
  font-weight: bold;
}

.progress-normal {
  color: #606266;
}
</style>
