<template>
  <div class="app-container">
    <AppHeader />
    <div class="main-container">
      <div class="sidebar-container">
        <el-button
          class="toggle-sidebar-btn"
          :icon="isCollapse ? Expand : Fold"
          @click="toggleSidebar"
          circle
        />
        <AppSidebar :is-collapse="isCollapse" />
      </div>
      <div class="content-container">
        <el-main>
          <slot></slot>
        </el-main>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Expand, Fold } from '@element-plus/icons-vue'
import AppHeader from './AppHeader.vue'
import AppSidebar from './AppSidebar.vue'

const isCollapse = ref(false)

const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}
</script>

<style scoped>
.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.sidebar-container {
  position: relative;
  background-color: #fff;
  transition: width 0.3s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.content-container {
  flex: 1;
  overflow: auto;
  background-color: #f5f7fa;
}

.toggle-sidebar-btn {
  position: absolute;
  right: -15px;
  top: 20px;
  z-index: 10;
}

.el-main {
  padding: 20px;
}
</style>
