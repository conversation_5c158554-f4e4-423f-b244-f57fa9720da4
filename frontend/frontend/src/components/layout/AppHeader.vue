<template>
  <el-header class="app-header">
    <div class="logo">
      <router-link to="/">
        <h1>專案管理系統</h1>
      </router-link>
    </div>
    <div class="header-right">
      <el-dropdown v-if="isAuthenticated" @command="handleCommand">
        <span class="el-dropdown-link">
          {{ username }}
          <el-icon class="el-icon--right"><arrow-down /></el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">個人資料</el-dropdown-item>
            <el-dropdown-item command="logout">登出</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <div v-if="isAuthenticated" class="notification-icon">
        <el-badge :value="unreadCount" :max="99" class="notification-badge">
          <el-button circle @click="goToNotifications">
            <el-icon><bell /></el-icon>
          </el-button>
        </el-badge>
      </div>
      <div v-else>
        <el-button type="primary" @click="goToLogin">登入</el-button>
      </div>
    </div>
  </el-header>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { ArrowDown, Bell } from '@element-plus/icons-vue'
import api from '../../services/api'

const router = useRouter()
const authStore = useAuthStore()
const unreadCount = ref(0)

const isAuthenticated = computed(() => authStore.isAuthenticated)
const username = computed(() => authStore.username)

onMounted(async () => {
  if (isAuthenticated.value) {
    try {
      const response = await api.getMyUnreadNotifications()
      unreadCount.value = response.data.length
    } catch (error) {
      console.error('Failed to fetch notifications:', error)
    }
  }
})

const handleCommand = (command: string) => {
  if (command === 'logout') {
    authStore.logout()
  } else if (command === 'profile') {
    router.push('/profile')
  }
}

const goToLogin = () => {
  router.push('/login')
}

const goToNotifications = () => {
  router.push('/notifications')
}
</script>

<style scoped>
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 0 20px;
}

.logo {
  display: flex;
  align-items: center;
}

.logo h1 {
  font-size: 1.5rem;
  margin: 0;
  color: #409EFF;
}

.logo a {
  text-decoration: none;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.el-dropdown-link {
  cursor: pointer;
  display: flex;
  align-items: center;
}

.notification-icon {
  margin-left: 10px;
}

.notification-badge {
  margin-top: 10px;
}
</style>
