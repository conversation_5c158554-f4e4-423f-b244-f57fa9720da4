<template>
  <el-menu
    :default-active="activeIndex"
    class="el-menu-vertical"
    :collapse="isCollapse"
    @select="handleSelect"
  >
    <el-menu-item index="/dashboard">
      <el-icon><Monitor /></el-icon>
      <template #title>儀表板</template>
    </el-menu-item>

    <el-sub-menu index="projects">
      <template #title>
        <el-icon><Folder /></el-icon>
        <span>專案管理</span>
      </template>
      <el-menu-item index="/projects">專案列表</el-menu-item>
      <el-menu-item index="/tasks">任務列表</el-menu-item>
      <el-menu-item index="/task-reports">任務報告</el-menu-item>
    </el-sub-menu>

    <el-sub-menu index="customers">
      <template #title>
        <el-icon><User /></el-icon>
        <span>客戶管理</span>
      </template>
      <el-menu-item index="/customers">客戶列表</el-menu-item>
      <el-menu-item index="/contact-records">聯繫記錄</el-menu-item>
    </el-sub-menu>

    <el-sub-menu index="resources">
      <template #title>
        <el-icon><Collection /></el-icon>
        <span>資源管理</span>
      </template>
      <el-menu-item index="/resources">資源列表</el-menu-item>
      <el-menu-item index="/resource-allocations">資源分配</el-menu-item>
    </el-sub-menu>

    <el-menu-item index="/documents">
      <el-icon><Document /></el-icon>
      <template #title>文件管理</template>
    </el-menu-item>

    <el-sub-menu index="organization" v-if="isAdmin">
      <template #title>
        <el-icon><OfficeBuilding /></el-icon>
        <span>組織管理</span>
      </template>
      <el-menu-item index="/departments">部門管理</el-menu-item>
      <el-menu-item index="/users">使用者管理</el-menu-item>
    </el-sub-menu>

    <el-sub-menu index="system" v-if="isAdmin">
      <template #title>
        <el-icon><Setting /></el-icon>
        <span>系統管理</span>
      </template>
      <el-menu-item index="/tags">標籤管理</el-menu-item>
      <el-menu-item index="/settings">系統設置</el-menu-item>
    </el-sub-menu>

    <el-menu-item index="/profile">
      <el-icon><User /></el-icon>
      <template #title>個人設定</template>
    </el-menu-item>
  </el-menu>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import {
  Monitor,
  Folder,
  User,
  Collection,
  Document,
  OfficeBuilding,
  Setting
} from '@element-plus/icons-vue'

const props = defineProps({
  isCollapse: {
    type: Boolean,
    default: false
  }
})

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const activeIndex = ref(route.path)

const isAdmin = computed(() => {
  return authStore.roles.some(role => ['ROLE_ADMIN', 'ROLE_SUPER_ADMIN'].includes(role))
})

onMounted(() => {
  activeIndex.value = route.path
})

watch(() => route.path, (newPath) => {
  activeIndex.value = newPath
})

const handleSelect = (key: string) => {
  router.push(key)
}
</script>

<style scoped>
.el-menu-vertical:not(.el-menu--collapse) {
  width: 240px;
  min-height: calc(100vh - 60px);
}

.el-menu--collapse {
  width: 64px;
  min-height: calc(100vh - 60px);
}
</style>
