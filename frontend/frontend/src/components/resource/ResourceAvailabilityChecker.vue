<template>
  <div class="resource-availability-checker">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>資源可用性檢查</span>
          <el-button type="primary" @click="checkAvailability" :loading="loading">
            檢查可用性
          </el-button>
        </div>
      </template>

      <el-form :model="form" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="資源">
              <el-select v-model="form.resourceId" placeholder="選擇資源" style="width: 100%">
                <el-option
                  v-for="resource in resources"
                  :key="resource.id"
                  :label="`${resource.name} (${getResourceTypeLabel(resource.type)})`"
                  :value="resource.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所需數量">
              <el-input-number
                v-model="form.requiredQuantity"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="開始日期">
              <el-date-picker
                v-model="form.startDate"
                type="date"
                placeholder="選擇開始日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="結束日期">
              <el-date-picker
                v-model="form.endDate"
                type="date"
                placeholder="選擇結束日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <!-- 檢查結果 -->
      <div v-if="result" class="availability-result">
        <el-divider content-position="left">檢查結果</el-divider>

        <el-alert
          :title="result.available ? '資源可用' : '資源不足'"
          :type="result.available ? 'success' : 'error'"
          :description="result.message"
          show-icon
          :closable="false"
        />

        <div class="result-details" style="margin-top: 15px;">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="可用數量">
              {{ result.availableQuantity }}
            </el-descriptions-item>
            <el-descriptions-item label="所需數量">
              {{ form.requiredQuantity }}
            </el-descriptions-item>
            <el-descriptions-item label="狀態">
              <el-tag :type="result.available ? 'success' : 'danger'">
                {{ result.available ? '可用' : '不足' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="檢查時間">
              {{ new Date().toLocaleString() }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 如果資源不足，顯示通知選項 -->
        <div v-if="!result.available" class="notification-options" style="margin-top: 15px;">
          <el-divider content-position="left">通知選項</el-divider>
          <el-space>
            <el-button type="warning" @click="notifyProjectManager" :loading="notifyLoading">
              通知專案負責人
            </el-button>
            <el-button type="primary" @click="notifyProcurementOfficer" :loading="notifyLoading">
              通知採購人員
            </el-button>
          </el-space>
        </div>
      </div>

      <!-- 資源使用統計 -->
      <div v-if="statistics" class="usage-statistics">
        <el-divider content-position="left">使用統計</el-divider>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-statistic title="總分配次數" :value="statistics.totalAllocations" />
          </el-col>
          <el-col :span="8">
            <el-statistic title="總使用數量" :value="statistics.totalQuantityUsed" />
          </el-col>
          <el-col :span="8">
            <el-statistic title="使用率" :value="statistics.utilizationRate" suffix="%" />
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import api from '@/services/api'

interface Resource {
  id: number
  name: string
  type: string
  available: boolean
}

interface AvailabilityResult {
  available: boolean
  message: string
  availableQuantity: number
}

interface UsageStatistics {
  resourceId: number
  resourceName: string
  totalAllocations: number
  totalQuantityUsed: number
  utilizationRate: number
}

const loading = ref(false)
const notifyLoading = ref(false)
const resources = ref<Resource[]>([])
const result = ref<AvailabilityResult | null>(null)
const statistics = ref<UsageStatistics | null>(null)

const form = reactive({
  resourceId: '',
  requiredQuantity: 1,
  startDate: '',
  endDate: ''
})

const getResourceTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    'HUMAN': '人力',
    'MATERIAL': '材料',
    'EQUIPMENT': '設備'
  }
  return typeMap[type] || type
}

const fetchResources = async () => {
  try {
    const response = await api.getResources()
    resources.value = response.data
  } catch (error) {
    ElMessage.error('獲取資源列表失敗')
  }
}

const checkAvailability = async () => {
  if (!form.resourceId || !form.startDate || !form.endDate || !form.requiredQuantity) {
    ElMessage.warning('請填寫完整的檢查信息')
    return
  }

  loading.value = true
  try {
    // 檢查可用性
    const availabilityResponse = await api.checkResourceAvailability({
      resourceId: form.resourceId,
      startDate: form.startDate,
      endDate: form.endDate,
      requiredQuantity: form.requiredQuantity
    })
    result.value = availabilityResponse.data

    // 獲取使用統計
    const statsResponse = await api.getResourceUsageStatistics(
      form.resourceId,
      form.startDate,
      form.endDate
    )
    statistics.value = statsResponse.data

    ElMessage.success('檢查完成')
  } catch (error) {
    ElMessage.error('檢查失敗')
  } finally {
    loading.value = false
  }
}

const notifyProjectManager = async () => {
  notifyLoading.value = true
  try {
    // 這裡需要實現通知專案負責人的邏輯
    ElMessage.success('已通知專案負責人')
  } catch (error) {
    ElMessage.error('通知失敗')
  } finally {
    notifyLoading.value = false
  }
}

const notifyProcurementOfficer = async () => {
  notifyLoading.value = true
  try {
    // 這裡需要實現通知採購人員的邏輯
    ElMessage.success('已通知採購人員')
  } catch (error) {
    ElMessage.error('通知失敗')
  } finally {
    notifyLoading.value = false
  }
}

onMounted(() => {
  fetchResources()
})
</script>

<style scoped>
.resource-availability-checker {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.availability-result {
  margin-top: 20px;
}

.result-details {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.notification-options {
  background-color: #fff7e6;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ffd591;
}

.usage-statistics {
  margin-top: 20px;
  background-color: #f0f9ff;
  padding: 15px;
  border-radius: 4px;
}
</style>
