<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="120px"
    @submit.prevent="handleSubmit"
  >
    <el-form-item label="資源" prop="resourceId">
      <el-select
        v-model="form.resourceId"
        placeholder="請選擇資源"
        filterable
        style="width: 100%"
        :loading="resourcesLoading"
      >
        <el-option
          v-for="resource in resources"
          :key="resource.id"
          :label="`${resource.name} (${resource.type})`"
          :value="resource.id"
        />
      </el-select>
    </el-form-item>

    <el-form-item label="分配類型" prop="allocationType">
      <el-radio-group v-model="form.allocationType">
        <el-radio value="project">專案</el-radio>
        <el-radio value="task">任務</el-radio>
      </el-radio-group>
    </el-form-item>

    <el-form-item v-if="form.allocationType === 'project'" label="專案" prop="projectId">
      <el-select
        v-model="form.projectId"
        placeholder="請選擇專案"
        filterable
        style="width: 100%"
        :loading="projectsLoading"
      >
        <el-option
          v-for="project in projects"
          :key="project.id"
          :label="project.name"
          :value="project.id"
        />
      </el-select>
    </el-form-item>

    <el-form-item v-if="form.allocationType === 'task'" label="任務" prop="taskId">
      <el-select
        v-model="form.taskId"
        placeholder="請選擇任務"
        filterable
        style="width: 100%"
        :loading="tasksLoading"
      >
        <el-option
          v-for="task in tasks"
          :key="task.id"
          :label="task.name"
          :value="task.id"
        />
      </el-select>
    </el-form-item>

    <el-form-item label="分配數量" prop="allocatedQuantity">
      <el-input-number
        v-model="form.allocatedQuantity"
        :min="1"
        :max="9999"
        placeholder="請輸入分配數量"
        style="width: 100%"
      />
    </el-form-item>

    <el-form-item label="開始日期" prop="startDate">
      <el-date-picker
        v-model="form.startDate"
        type="date"
        placeholder="選擇開始日期"
        style="width: 100%"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
      />
    </el-form-item>

    <el-form-item label="結束日期" prop="endDate">
      <el-date-picker
        v-model="form.endDate"
        type="date"
        placeholder="選擇結束日期"
        style="width: 100%"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
      />
    </el-form-item>

    <el-form-item label="備註" prop="notes">
      <el-input
        v-model="form.notes"
        type="textarea"
        :rows="3"
        placeholder="請輸入備註（可選）"
        maxlength="500"
        show-word-limit
      />
    </el-form-item>

    <el-form-item>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        {{ isEdit ? '更新' : '創建' }}
      </el-button>
      <el-button @click="handleCancel">取消</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useResourceAllocationStore, type ResourceAllocation } from '../../stores/resourceAllocation'
import api from '../../services/api'

interface Props {
  resourceAllocation?: ResourceAllocation | null
  isEdit?: boolean
}

interface Emits {
  (e: 'success', resourceAllocation: ResourceAllocation): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  resourceAllocation: null,
  isEdit: false
})

const emit = defineEmits<Emits>()

const resourceAllocationStore = useResourceAllocationStore()
const formRef = ref<FormInstance>()
const loading = ref(false)
const resourcesLoading = ref(false)
const projectsLoading = ref(false)
const tasksLoading = ref(false)

const resources = ref<any[]>([])
const projects = ref<any[]>([])
const tasks = ref<any[]>([])

const form = reactive({
  resourceId: '',
  allocationType: 'project',
  projectId: '',
  taskId: '',
  allocatedQuantity: 1,
  startDate: '',
  endDate: '',
  notes: ''
})

const rules: FormRules = {
  resourceId: [
    { required: true, message: '請選擇資源', trigger: 'change' }
  ],
  allocationType: [
    { required: true, message: '請選擇分配類型', trigger: 'change' }
  ],
  projectId: [
    { 
      required: true, 
      message: '請選擇專案', 
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (form.allocationType === 'project' && !value) {
          callback(new Error('請選擇專案'))
        } else {
          callback()
        }
      }
    }
  ],
  taskId: [
    { 
      required: true, 
      message: '請選擇任務', 
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (form.allocationType === 'task' && !value) {
          callback(new Error('請選擇任務'))
        } else {
          callback()
        }
      }
    }
  ],
  allocatedQuantity: [
    { required: true, message: '請輸入分配數量', trigger: 'blur' },
    { type: 'number', min: 1, message: '分配數量必須大於0', trigger: 'blur' }
  ],
  startDate: [
    { required: true, message: '請選擇開始日期', trigger: 'change' }
  ],
  endDate: [
    { required: true, message: '請選擇結束日期', trigger: 'change' },
    {
      validator: (rule, value, callback) => {
        if (value && form.startDate && new Date(value) <= new Date(form.startDate)) {
          callback(new Error('結束日期必須晚於開始日期'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

const fetchResources = async () => {
  resourcesLoading.value = true
  try {
    const response = await api.getResources()
    resources.value = response.data
  } catch (error) {
    ElMessage.error('獲取資源列表失敗')
  } finally {
    resourcesLoading.value = false
  }
}

const fetchProjects = async () => {
  projectsLoading.value = true
  try {
    const response = await api.getProjects()
    projects.value = response.data
  } catch (error) {
    ElMessage.error('獲取專案列表失敗')
  } finally {
    projectsLoading.value = false
  }
}

const fetchTasks = async () => {
  tasksLoading.value = true
  try {
    const response = await api.getTasks()
    tasks.value = response.data
  } catch (error) {
    ElMessage.error('獲取任務列表失敗')
  } finally {
    tasksLoading.value = false
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    const allocationData = {
      resource: { id: Number(form.resourceId) },
      allocatedQuantity: form.allocatedQuantity,
      startDate: form.startDate,
      endDate: form.endDate,
      notes: form.notes || undefined,
      ...(form.allocationType === 'project' 
        ? { project: { id: Number(form.projectId) } }
        : { task: { id: Number(form.taskId) } }
      )
    }

    let result: ResourceAllocation
    if (props.isEdit && props.resourceAllocation?.id) {
      result = await resourceAllocationStore.updateResourceAllocation(props.resourceAllocation.id, allocationData)
    } else {
      result = await resourceAllocationStore.createResourceAllocation(allocationData)
    }

    emit('success', result)
  } catch (error) {
    console.error('提交資源分配失敗:', error)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.resourceId = ''
  form.allocationType = 'project'
  form.projectId = ''
  form.taskId = ''
  form.allocatedQuantity = 1
  form.startDate = ''
  form.endDate = ''
  form.notes = ''
}

// Watch for resourceAllocation changes to populate form
watch(() => props.resourceAllocation, (newAllocation) => {
  if (newAllocation && props.isEdit) {
    form.resourceId = newAllocation.resource.id.toString()
    form.allocationType = newAllocation.project ? 'project' : 'task'
    form.projectId = newAllocation.project?.id.toString() || ''
    form.taskId = newAllocation.task?.id.toString() || ''
    form.allocatedQuantity = newAllocation.allocatedQuantity
    form.startDate = newAllocation.startDate
    form.endDate = newAllocation.endDate
    form.notes = newAllocation.notes || ''
  } else {
    resetForm()
  }
}, { immediate: true })

onMounted(() => {
  fetchResources()
  fetchProjects()
  fetchTasks()
})

defineExpose({
  resetForm
})
</script>

<style scoped>
.el-form {
  max-width: 600px;
}
</style>
