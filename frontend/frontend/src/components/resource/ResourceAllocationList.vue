<template>
  <div class="resource-allocation-list">
    <!-- 搜索和篩選 -->
    <div class="filter-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-select
            v-model="filters.resourceId"
            placeholder="篩選資源"
            clearable
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="resource in resources"
              :key="resource.id"
              :label="`${resource.name} (${resource.type})`"
              :value="resource.id"
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="filters.allocationType"
            placeholder="分配類型"
            clearable
            style="width: 100%"
          >
            <el-option label="專案" value="project" />
            <el-option label="任務" value="task" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="開始日期"
            end-placeholder="結束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="6" class="text-right">
          <el-button type="primary" @click="handleCreate">
            <el-icon><Plus /></el-icon>
            新增分配
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 分配列表 -->
    <el-table
      :data="resourceAllocations"
      v-loading="loading"
      stripe
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="80" />
      
      <el-table-column label="資源" min-width="150">
        <template #default="{ row }">
          <div>
            <div class="resource-name">{{ row.resource.name }}</div>
            <el-tag size="small" type="info">{{ row.resource.type }}</el-tag>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="分配對象" min-width="180">
        <template #default="{ row }">
          <div v-if="row.project">
            <el-tag type="success">專案</el-tag>
            <span class="ml-2">{{ row.project.name }}</span>
          </div>
          <div v-else-if="row.task">
            <el-tag type="warning">任務</el-tag>
            <span class="ml-2">{{ row.task.name }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="allocatedQuantity" label="分配數量" width="100" />

      <el-table-column label="分配期間" min-width="200">
        <template #default="{ row }">
          <div class="date-range">
            <div>開始：{{ row.startDate }}</div>
            <div>結束：{{ row.endDate }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="狀態" width="100">
        <template #default="{ row }">
          <el-tag :type="getAllocationStatus(row).type">
            {{ getAllocationStatus(row).text }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="備註" min-width="150">
        <template #default="{ row }">
          <el-tooltip v-if="row.notes" :content="row.notes" placement="top">
            <span class="notes-preview">{{ truncateText(row.notes, 30) }}</span>
          </el-tooltip>
          <span v-else class="text-muted">無</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="handleView(row)"
          >
            查看
          </el-button>
          <el-button
            type="warning"
            size="small"
            @click="handleEdit(row)"
          >
            編輯
          </el-button>
          <el-popconfirm
            title="確定要刪除這個資源分配嗎？"
            @confirm="handleDelete(row.id)"
          >
            <template #reference>
              <el-button
                type="danger"
                size="small"
              >
                刪除
              </el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分頁 -->
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 查看詳情對話框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="資源分配詳情"
      width="600px"
    >
      <div v-if="selectedAllocation" class="allocation-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="資源名稱">
            {{ selectedAllocation.resource.name }}
          </el-descriptions-item>
          <el-descriptions-item label="資源類型">
            {{ selectedAllocation.resource.type }}
          </el-descriptions-item>
          <el-descriptions-item label="分配對象">
            <div v-if="selectedAllocation.project">
              <el-tag type="success">專案</el-tag>
              <span class="ml-2">{{ selectedAllocation.project.name }}</span>
            </div>
            <div v-else-if="selectedAllocation.task">
              <el-tag type="warning">任務</el-tag>
              <span class="ml-2">{{ selectedAllocation.task.name }}</span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="分配數量">
            {{ selectedAllocation.allocatedQuantity }}
          </el-descriptions-item>
          <el-descriptions-item label="開始日期">
            {{ selectedAllocation.startDate }}
          </el-descriptions-item>
          <el-descriptions-item label="結束日期">
            {{ selectedAllocation.endDate }}
          </el-descriptions-item>
          <el-descriptions-item label="狀態">
            <el-tag :type="getAllocationStatus(selectedAllocation).type">
              {{ getAllocationStatus(selectedAllocation).text }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
        
        <div v-if="selectedAllocation.notes" class="notes-section">
          <h4>備註</h4>
          <p>{{ selectedAllocation.notes }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { useResourceAllocationStore, type ResourceAllocation } from '../../stores/resourceAllocation'
import api from '../../services/api'

interface Emits {
  (e: 'create'): void
  (e: 'edit', allocation: ResourceAllocation): void
}

const emit = defineEmits<Emits>()

const resourceAllocationStore = useResourceAllocationStore()
const loading = ref(false)
const resources = ref<any[]>([])
const viewDialogVisible = ref(false)
const selectedAllocation = ref<ResourceAllocation | null>(null)

const filters = reactive({
  resourceId: '',
  allocationType: '',
  dateRange: [] as string[]
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const resourceAllocations = computed(() => resourceAllocationStore.resourceAllocations)

const getAllocationStatus = (allocation: ResourceAllocation) => {
  const today = new Date().toISOString().split('T')[0]
  const startDate = allocation.startDate
  const endDate = allocation.endDate

  if (today < startDate) {
    return { type: 'info', text: '未開始' }
  } else if (today >= startDate && today <= endDate) {
    return { type: 'success', text: '進行中' }
  } else {
    return { type: 'danger', text: '已結束' }
  }
}

const truncateText = (text: string, maxLength: number) => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const fetchResources = async () => {
  try {
    const response = await api.getResources()
    resources.value = response.data
  } catch (error) {
    console.error('獲取資源列表失敗:', error)
  }
}

const fetchResourceAllocations = async () => {
  loading.value = true
  try {
    await resourceAllocationStore.fetchResourceAllocations()
    pagination.total = resourceAllocations.value.length
  } catch (error) {
    console.error('獲取資源分配失敗:', error)
  } finally {
    loading.value = false
  }
}

const handleCreate = () => {
  emit('create')
}

const handleView = (allocation: ResourceAllocation) => {
  selectedAllocation.value = allocation
  viewDialogVisible.value = true
}

const handleEdit = (allocation: ResourceAllocation) => {
  emit('edit', allocation)
}

const handleDelete = async (id: number) => {
  try {
    await resourceAllocationStore.deleteResourceAllocation(id)
    fetchResourceAllocations()
  } catch (error) {
    console.error('刪除資源分配失敗:', error)
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  fetchResourceAllocations()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  fetchResourceAllocations()
}

onMounted(() => {
  fetchResources()
  fetchResourceAllocations()
})
</script>

<style scoped>
.resource-allocation-list {
  padding: 20px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
}

.text-right {
  text-align: right;
}

.resource-name {
  font-weight: 600;
  margin-bottom: 4px;
}

.ml-2 {
  margin-left: 8px;
}

.date-range div {
  margin-bottom: 2px;
}

.notes-preview {
  cursor: pointer;
}

.text-muted {
  color: #999;
}

.pagination-section {
  margin-top: 20px;
  text-align: center;
}

.allocation-detail {
  padding: 20px 0;
}

.notes-section {
  margin-top: 20px;
}

.notes-section h4 {
  margin-bottom: 10px;
  color: #333;
}

.notes-section p {
  line-height: 1.6;
  color: #666;
}
</style>
