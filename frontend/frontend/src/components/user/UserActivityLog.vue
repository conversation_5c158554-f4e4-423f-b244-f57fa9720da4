<template>
  <div class="user-activity-log">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>用戶活動記錄</span>
          <div class="header-actions">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="開始日期"
              end-placeholder="結束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleDateChange"
            />
            <el-button @click="refreshLogs">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 統計信息 -->
      <div class="stats-row">
        <el-statistic title="總活動數" :value="totalActivities" />
        <el-statistic title="今日活動" :value="todayActivities" />
        <el-statistic title="本週活動" :value="weekActivities" />
        <el-statistic title="最後活動" :value="lastActivityTime" />
      </div>

      <!-- 活動類型過濾 -->
      <div class="filter-row">
        <el-select
          v-model="selectedActivityType"
          placeholder="活動類型"
          style="width: 200px"
          clearable
          @change="handleFilterChange"
        >
          <el-option label="登入" value="LOGIN" />
          <el-option label="登出" value="LOGOUT" />
          <el-option label="創建" value="CREATE" />
          <el-option label="更新" value="UPDATE" />
          <el-option label="刪除" value="DELETE" />
          <el-option label="查看" value="VIEW" />
        </el-select>
      </div>

      <!-- 活動時間線 -->
      <div class="timeline-container">
        <el-timeline>
          <el-timeline-item
            v-for="log in paginatedLogs"
            :key="log.id"
            :timestamp="formatDateTime(log.createdAt)"
            placement="top"
            :type="getActivityTypeColor(log.activityType)"
            :icon="getActivityIcon(log.activityType)"
          >
            <el-card class="activity-card">
              <div class="activity-header">
                <el-tag :type="getActivityTypeColor(log.activityType)" size="small">
                  {{ getActivityTypeText(log.activityType) }}
                </el-tag>
                <span class="activity-time">{{ formatTime(log.createdAt) }}</span>
              </div>
              <div class="activity-content">
                <p class="activity-description">{{ log.description }}</p>
                <div class="activity-meta">
                  <span class="meta-item">
                    <el-icon><Location /></el-icon>
                    IP: {{ log.ipAddress || '未知' }}
                  </span>
                  <span class="meta-item">
                    <el-icon><Clock /></el-icon>
                    {{ formatRelativeTime(log.createdAt) }}
                  </span>
                </div>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>

        <!-- 加載更多 -->
        <div v-if="hasMore" class="load-more">
          <el-button @click="loadMore" :loading="loading">
            加載更多
          </el-button>
        </div>

        <!-- 空狀態 -->
        <el-empty v-if="filteredLogs.length === 0 && !loading" description="暫無活動記錄" />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh,
  Location,
  Clock,
  User,
  Edit,
  Delete,
  View,
  Plus,
  Switch
} from '@element-plus/icons-vue'
import api from '../../services/api'

interface Props {
  userId: number
}

const props = defineProps<Props>()

// 響應式數據
const loading = ref(false)
const activityLogs = ref<any[]>([])
const dateRange = ref<string[]>([])
const selectedActivityType = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const hasMore = ref(true)

// 計算屬性
const filteredLogs = computed(() => {
  let filtered = activityLogs.value

  // 日期過濾
  if (dateRange.value && dateRange.value.length === 2) {
    const [startDate, endDate] = dateRange.value
    filtered = filtered.filter(log => {
      const logDate = log.createdAt.split('T')[0]
      return logDate >= startDate && logDate <= endDate
    })
  }

  // 活動類型過濾
  if (selectedActivityType.value) {
    filtered = filtered.filter(log => log.activityType === selectedActivityType.value)
  }

  return filtered
})

const paginatedLogs = computed(() => {
  const start = 0
  const end = currentPage.value * pageSize.value
  return filteredLogs.value.slice(start, end)
})

const totalActivities = computed(() => activityLogs.value.length)

const todayActivities = computed(() => {
  const today = new Date().toISOString().split('T')[0]
  return activityLogs.value.filter(log => log.createdAt.startsWith(today)).length
})

const weekActivities = computed(() => {
  const weekAgo = new Date()
  weekAgo.setDate(weekAgo.getDate() - 7)
  const weekAgoStr = weekAgo.toISOString().split('T')[0]
  return activityLogs.value.filter(log => log.createdAt.split('T')[0] >= weekAgoStr).length
})

const lastActivityTime = computed(() => {
  if (activityLogs.value.length === 0) return '-'
  return formatRelativeTime(activityLogs.value[0].createdAt)
})

// 方法
const fetchActivityLogs = async () => {
  loading.value = true
  try {
    const response = await api.getUserActivityLogs(props.userId)
    activityLogs.value = response.data.sort((a: any, b: any) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )
    hasMore.value = false // 假設一次性加載所有數據
  } catch (error) {
    console.error('獲取活動記錄失敗:', error)
    ElMessage.error('獲取活動記錄失敗')
  } finally {
    loading.value = false
  }
}

const refreshLogs = () => {
  currentPage.value = 1
  fetchActivityLogs()
}

const loadMore = () => {
  currentPage.value++
}

const handleDateChange = () => {
  currentPage.value = 1
}

const handleFilterChange = () => {
  currentPage.value = 1
}

const getActivityTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'LOGIN': '登入',
    'LOGOUT': '登出',
    'CREATE': '創建',
    'UPDATE': '更新',
    'DELETE': '刪除',
    'VIEW': '查看'
  }
  return typeMap[type] || type
}

const getActivityTypeColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'LOGIN': 'success',
    'LOGOUT': 'info',
    'CREATE': 'primary',
    'UPDATE': 'warning',
    'DELETE': 'danger',
    'VIEW': 'info'
  }
  return colorMap[type] || 'info'
}

const getActivityIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    'LOGIN': Switch,
    'LOGOUT': Switch,
    'CREATE': Plus,
    'UPDATE': Edit,
    'DELETE': Delete,
    'VIEW': View
  }
  return iconMap[type] || User
}

const formatDateTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatTime = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleTimeString('zh-TW', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatRelativeTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 1) return '剛剛'
  if (minutes < 60) return `${minutes}分鐘前`
  if (hours < 24) return `${hours}小時前`
  if (days < 7) return `${days}天前`
  return formatDateTime(dateString)
}

onMounted(() => {
  fetchActivityLogs()
})
</script>

<style scoped>
.user-activity-log {
  max-width: 800px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.filter-row {
  margin-bottom: 20px;
}

.timeline-container {
  max-height: 600px;
  overflow-y: auto;
}

.activity-card {
  margin-bottom: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.activity-time {
  font-size: 12px;
  color: #909399;
}

.activity-content {
  color: #606266;
}

.activity-description {
  margin: 0 0 8px 0;
  font-size: 14px;
  line-height: 1.5;
}

.activity-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #909399;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.load-more {
  text-align: center;
  margin-top: 20px;
}

:deep(.el-timeline-item__timestamp) {
  font-size: 12px;
  color: #909399;
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 28px;
}
</style>
