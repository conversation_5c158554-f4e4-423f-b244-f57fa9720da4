<template>
  <div class="permission-manager">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>用戶權限管理</span>
          <el-button type="primary" @click="savePermissions" :loading="saving">
            保存權限
          </el-button>
        </div>
      </template>

      <div class="permission-content">
        <!-- 角色管理 -->
        <div class="section">
          <h3>角色分配</h3>
          <el-checkbox-group v-model="selectedRoles">
            <el-checkbox
              v-for="role in availableRoles"
              :key="role.id"
              :label="role.id"
              :disabled="!canManageRole(role)"
            >
              <div class="role-item">
                <span class="role-name">{{ getRoleLabel(role.name) }}</span>
                <span class="role-description">{{ role.description }}</span>
              </div>
            </el-checkbox>
          </el-checkbox-group>
        </div>

        <!-- 部門權限 -->
        <div class="section">
          <h3>部門權限</h3>
          <el-select
            v-model="selectedDepartment"
            placeholder="選擇部門"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="dept in departments"
              :key="dept.id"
              :label="dept.name"
              :value="dept.id"
            />
          </el-select>
        </div>

        <!-- 專案權限 -->
        <div class="section">
          <h3>專案權限</h3>
          <el-transfer
            v-model="selectedProjects"
            :data="availableProjects"
            :titles="['可用專案', '已分配專案']"
            :button-texts="['移除', '分配']"
            :format="{
              noChecked: '${total}',
              hasChecked: '${checked}/${total}'
            }"
            filterable
            filter-placeholder="搜索專案"
          />
        </div>

        <!-- 功能權限 -->
        <div class="section">
          <h3>功能權限</h3>
          <el-tree
            ref="permissionTree"
            :data="permissionTree"
            :props="treeProps"
            show-checkbox
            node-key="id"
            :default-checked-keys="selectedPermissions"
            @check="handlePermissionCheck"
          />
        </div>

        <!-- 權限預覽 -->
        <div class="section">
          <h3>權限預覽</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="角色">
              <el-tag
                v-for="roleId in selectedRoles"
                :key="roleId"
                style="margin-right: 8px"
              >
                {{ getRoleLabel(getRoleById(roleId)?.name) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="部門">
              {{ getDepartmentName(selectedDepartment) }}
            </el-descriptions-item>
            <el-descriptions-item label="專案數量">
              {{ selectedProjects.length }} 個專案
            </el-descriptions-item>
            <el-descriptions-item label="功能權限">
              {{ selectedPermissions.length }} 項權限
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import api from '../../services/api'

interface Props {
  userId: number
}

const props = defineProps<Props>()

// 響應式數據
const saving = ref(false)
const availableRoles = ref<any[]>([])
const departments = ref<any[]>([])
const availableProjects = ref<any[]>([])
const selectedRoles = ref<number[]>([])
const selectedDepartment = ref<number | null>(null)
const selectedProjects = ref<number[]>([])
const selectedPermissions = ref<string[]>([])

// 權限樹配置
const treeProps = {
  children: 'children',
  label: 'label'
}

// 權限樹數據
const permissionTree = ref([
  {
    id: 'user',
    label: '用戶管理',
    children: [
      { id: 'user.view', label: '查看用戶' },
      { id: 'user.create', label: '創建用戶' },
      { id: 'user.edit', label: '編輯用戶' },
      { id: 'user.delete', label: '刪除用戶' },
      { id: 'user.permission', label: '管理權限' }
    ]
  },
  {
    id: 'project',
    label: '專案管理',
    children: [
      { id: 'project.view', label: '查看專案' },
      { id: 'project.create', label: '創建專案' },
      { id: 'project.edit', label: '編輯專案' },
      { id: 'project.delete', label: '刪除專案' },
      { id: 'project.member', label: '管理成員' }
    ]
  },
  {
    id: 'task',
    label: '任務管理',
    children: [
      { id: 'task.view', label: '查看任務' },
      { id: 'task.create', label: '創建任務' },
      { id: 'task.edit', label: '編輯任務' },
      { id: 'task.delete', label: '刪除任務' },
      { id: 'task.assign', label: '分配任務' }
    ]
  },
  {
    id: 'resource',
    label: '資源管理',
    children: [
      { id: 'resource.view', label: '查看資源' },
      { id: 'resource.create', label: '創建資源' },
      { id: 'resource.edit', label: '編輯資源' },
      { id: 'resource.delete', label: '刪除資源' },
      { id: 'resource.allocate', label: '分配資源' }
    ]
  }
])

// 計算屬性
const permissionTree = ref()

// 方法
const fetchUserPermissions = async () => {
  try {
    const response = await api.getUser(props.userId)
    const user = response.data
    
    selectedRoles.value = user.roles?.map((role: any) => role.id) || []
    selectedDepartment.value = user.department?.id || null
    // 這裡需要根據實際API獲取用戶的專案和權限
  } catch (error) {
    console.error('獲取用戶權限失敗:', error)
    ElMessage.error('獲取用戶權限失敗')
  }
}

const fetchRoles = async () => {
  try {
    const response = await api.getRoles()
    availableRoles.value = response.data
  } catch (error) {
    console.error('獲取角色列表失敗:', error)
  }
}

const fetchDepartments = async () => {
  try {
    const response = await api.getDepartments()
    departments.value = response.data
  } catch (error) {
    console.error('獲取部門列表失敗:', error)
  }
}

const fetchProjects = async () => {
  try {
    const response = await api.getProjects()
    availableProjects.value = response.data.map((project: any) => ({
      key: project.id,
      label: project.name,
      disabled: false
    }))
  } catch (error) {
    console.error('獲取專案列表失敗:', error)
  }
}

const canManageRole = (role: any) => {
  // 這裡可以根據當前用戶的權限來判斷是否可以管理某個角色
  return true
}

const getRoleLabel = (roleName: string) => {
  const roleMap: Record<string, string> = {
    'SUPER_ADMIN': '超級管理員',
    'ADMIN': '管理員',
    'DEPARTMENT_HEAD': '部門主管',
    'PROJECT_MANAGER': '專案經理',
    'EMPLOYEE': '員工'
  }
  return roleMap[roleName] || roleName
}

const getRoleById = (id: number) => {
  return availableRoles.value.find(role => role.id === id)
}

const getDepartmentName = (id: number | null) => {
  if (!id) return '-'
  const dept = departments.value.find(d => d.id === id)
  return dept?.name || '-'
}

const handlePermissionCheck = (data: any, checked: any) => {
  selectedPermissions.value = checked.checkedKeys
}

const savePermissions = async () => {
  saving.value = true
  try {
    const permissionData = {
      roles: selectedRoles.value.map(id => ({ id })),
      department: selectedDepartment.value ? { id: selectedDepartment.value } : null,
      projects: selectedProjects.value,
      permissions: selectedPermissions.value
    }

    await api.updateUser(props.userId, permissionData)
    ElMessage.success('權限保存成功')
  } catch (error: any) {
    console.error('保存權限失敗:', error)
    ElMessage.error('保存權限失敗')
  } finally {
    saving.value = false
  }
}

onMounted(() => {
  fetchUserPermissions()
  fetchRoles()
  fetchDepartments()
  fetchProjects()
})
</script>

<style scoped>
.permission-manager {
  max-width: 800px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.permission-content {
  max-height: 600px;
  overflow-y: auto;
}

.section {
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid #ebeef5;
}

.section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.role-item {
  display: flex;
  flex-direction: column;
  margin-left: 8px;
}

.role-name {
  font-weight: 600;
  color: #303133;
}

.role-description {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

:deep(.el-checkbox) {
  margin-bottom: 12px;
  width: 100%;
}

:deep(.el-transfer) {
  text-align: left;
}

:deep(.el-tree) {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
}
</style>
