<template>
  <div class="file-version-history">
    <div class="history-header">
      <h3>版本歷史: {{ file.name }}</h3>
      <p>查看文件的所有版本記錄</p>
    </div>

    <div class="upload-new-version">
      <el-button type="primary" @click="showUploadDialog = true">
        <el-icon><Upload /></el-icon>
        上傳新版本
      </el-button>
    </div>

    <div class="version-timeline">
      <el-timeline>
        <el-timeline-item
          v-for="(version, index) in versions"
          :key="version.id"
          :timestamp="formatDateTime(version.createdAt)"
          placement="top"
          :type="index === 0 ? 'primary' : 'info'"
          :icon="index === 0 ? Star : Document"
        >
          <el-card class="version-card">
            <div class="version-header">
              <div class="version-info">
                <h4>
                  版本 {{ version.version }}
                  <el-tag v-if="index === 0" type="success" size="small">當前版本</el-tag>
                </h4>
                <div class="version-meta">
                  <span class="uploader">
                    上傳者: {{ version.uploadedBy?.firstName }} {{ version.uploadedBy?.lastName }}
                  </span>
                  <span class="file-size">
                    大小: {{ formatFileSize(version.size) }}
                  </span>
                </div>
              </div>
              <div class="version-actions">
                <el-button size="small" @click="previewVersion(version)">
                  <el-icon><View /></el-icon>
                  預覽
                </el-button>
                <el-button size="small" @click="downloadVersion(version)">
                  <el-icon><Download /></el-icon>
                  下載
                </el-button>
                <el-button
                  v-if="index !== 0"
                  size="small"
                  type="warning"
                  @click="restoreVersion(version)"
                >
                  <el-icon><RefreshRight /></el-icon>
                  恢復
                </el-button>
                <el-button
                  v-if="index !== 0 && versions.length > 1"
                  size="small"
                  type="danger"
                  @click="deleteVersion(version)"
                >
                  <el-icon><Delete /></el-icon>
                  刪除
                </el-button>
              </div>
            </div>

            <div v-if="version.changeLog" class="change-log">
              <h5>更新說明</h5>
              <p>{{ version.changeLog }}</p>
            </div>

            <div class="version-details">
              <el-descriptions :column="3" size="small">
                <el-descriptions-item label="文件名">
                  {{ version.fileName }}
                </el-descriptions-item>
                <el-descriptions-item label="文件大小">
                  {{ formatFileSize(version.size) }}
                </el-descriptions-item>
                <el-descriptions-item label="MD5校驗">
                  <code class="md5-hash">{{ version.md5Hash || '未計算' }}</code>
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <!-- 版本比較 -->
            <div v-if="index > 0" class="version-compare">
              <el-button
                size="small"
                @click="compareVersions(versions[index - 1], version)"
              >
                <el-icon><Switch /></el-icon>
                與上一版本比較
              </el-button>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>

      <!-- 空狀態 -->
      <el-empty v-if="versions.length === 0" description="暫無版本記錄" />
    </div>

    <!-- 上傳新版本對話框 -->
    <el-dialog
      v-model="showUploadDialog"
      title="上傳新版本"
      width="500px"
      @close="resetUploadForm"
    >
      <el-form ref="uploadFormRef" :model="uploadForm" :rules="uploadRules" label-width="100px">
        <el-form-item label="選擇文件" prop="file">
          <el-upload
            ref="uploadRef"
            class="version-upload"
            drag
            :auto-upload="false"
            :on-change="handleFileChange"
            :file-list="uploadFileList"
            :limit="1"
            accept="*"
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              將新版本文件拖到此處，或<em>點擊上傳</em>
            </div>
          </el-upload>
        </el-form-item>

        <el-form-item label="版本號" prop="version">
          <el-input
            v-model="uploadForm.version"
            placeholder="例如: 2.0, 1.1.1"
            maxlength="20"
          />
        </el-form-item>

        <el-form-item label="更新說明" prop="changeLog">
          <el-input
            v-model="uploadForm.changeLog"
            type="textarea"
            placeholder="描述此版本的更新內容"
            :rows="3"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showUploadDialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="uploadNewVersion"
            :loading="uploading"
            :disabled="!uploadForm.file"
          >
            {{ uploading ? '上傳中...' : '上傳' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 版本比較對話框 -->
    <el-dialog
      v-model="showCompareDialog"
      title="版本比較"
      width="80%"
      top="5vh"
    >
      <div v-if="compareData" class="version-comparison">
        <div class="compare-header">
          <div class="compare-item">
            <h4>版本 {{ compareData.newVersion.version }}</h4>
            <p>{{ formatDateTime(compareData.newVersion.createdAt) }}</p>
          </div>
          <div class="compare-vs">VS</div>
          <div class="compare-item">
            <h4>版本 {{ compareData.oldVersion.version }}</h4>
            <p>{{ formatDateTime(compareData.oldVersion.createdAt) }}</p>
          </div>
        </div>

        <el-descriptions title="版本差異" :column="2" border>
          <el-descriptions-item label="文件大小">
            <div class="size-compare">
              <span>{{ formatFileSize(compareData.newVersion.size) }}</span>
              <span class="size-diff" :class="getSizeDiffClass()">
                {{ getSizeDiff() }}
              </span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="文件大小">
            {{ formatFileSize(compareData.oldVersion.size) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新說明">
            {{ compareData.newVersion.changeLog || '無' }}
          </el-descriptions-item>
          <el-descriptions-item label="更新說明">
            {{ compareData.oldVersion.changeLog || '無' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Upload,
  View,
  Download,
  RefreshRight,
  Delete,
  Switch,
  Star,
  Document,
  UploadFilled
} from '@element-plus/icons-vue'
import type { FormInstance, FormRules, UploadFile, UploadFiles } from 'element-plus'
import api from '../../services/api'

interface Props {
  file: any
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
}>()

// 響應式數據
const versions = ref<any[]>([])
const loading = ref(false)
const showUploadDialog = ref(false)
const showCompareDialog = ref(false)
const uploading = ref(false)
const uploadFormRef = ref<FormInstance>()
const uploadRef = ref()
const uploadFileList = ref<UploadFile[]>([])
const compareData = ref<any>(null)

// 上傳表單
const uploadForm = reactive({
  file: null as File | null,
  version: '',
  changeLog: ''
})

// 上傳表單驗證規則
const uploadRules: FormRules = {
  file: [
    { required: true, message: '請選擇要上傳的文件', trigger: 'change' }
  ],
  version: [
    { required: true, message: '請輸入版本號', trigger: 'blur' }
  ]
}

// 方法
const fetchVersions = async () => {
  loading.value = true
  try {
    const response = await api.getFileVersions(props.file.id)
    versions.value = response.data.sort((a: any, b: any) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    )
  } catch (error) {
    console.error('獲取版本歷史失敗:', error)
    ElMessage.error('獲取版本歷史失敗')
  } finally {
    loading.value = false
  }
}

const previewVersion = (version: any) => {
  // 實現版本預覽
  console.log('預覽版本:', version)
}

const downloadVersion = async (version: any) => {
  try {
    const response = await api.downloadFileVersion(version.id)
    // 創建下載鏈接
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', version.fileName)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('版本下載成功')
  } catch (error) {
    console.error('下載版本失敗:', error)
    ElMessage.error('下載版本失敗')
  }
}

const restoreVersion = async (version: any) => {
  try {
    await ElMessageBox.confirm(
      `確定要恢復到版本 ${version.version} 嗎？這將創建一個新的版本。`,
      '恢復版本',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await api.restoreFileVersion(props.file.id, version.id)
    ElMessage.success('版本恢復成功')
    fetchVersions()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('恢復版本失敗:', error)
      ElMessage.error('恢復版本失敗')
    }
  }
}

const deleteVersion = async (version: any) => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除版本 ${version.version} 嗎？此操作不可恢復。`,
      '刪除版本',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await api.deleteFileVersion(version.id)
    ElMessage.success('版本已刪除')
    fetchVersions()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('刪除版本失敗:', error)
      ElMessage.error('刪除版本失敗')
    }
  }
}

const compareVersions = (newVersion: any, oldVersion: any) => {
  compareData.value = { newVersion, oldVersion }
  showCompareDialog.value = true
}

const handleFileChange = (file: UploadFile, files: UploadFiles) => {
  uploadForm.file = file.raw || null
  uploadFileList.value = files
}

const uploadNewVersion = async () => {
  if (!uploadFormRef.value) return

  try {
    await uploadFormRef.value.validate()
    
    uploading.value = true

    const formData = new FormData()
    formData.append('file', uploadForm.file!)
    formData.append('version', uploadForm.version)
    formData.append('changeLog', uploadForm.changeLog)

    await api.uploadFileVersion(props.file.id, formData)
    ElMessage.success('新版本上傳成功')
    
    showUploadDialog.value = false
    resetUploadForm()
    fetchVersions()
  } catch (error) {
    console.error('上傳新版本失敗:', error)
    ElMessage.error('上傳新版本失敗')
  } finally {
    uploading.value = false
  }
}

const resetUploadForm = () => {
  uploadForm.file = null
  uploadForm.version = ''
  uploadForm.changeLog = ''
  uploadFileList.value = []
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
}

const getSizeDiff = () => {
  if (!compareData.value) return ''
  
  const diff = compareData.value.newVersion.size - compareData.value.oldVersion.size
  if (diff > 0) {
    return `+${formatFileSize(diff)}`
  } else if (diff < 0) {
    return `-${formatFileSize(Math.abs(diff))}`
  }
  return '無變化'
}

const getSizeDiffClass = () => {
  if (!compareData.value) return ''
  
  const diff = compareData.value.newVersion.size - compareData.value.oldVersion.size
  if (diff > 0) return 'size-increase'
  if (diff < 0) return 'size-decrease'
  return 'size-same'
}

const formatFileSize = (bytes: number) => {
  if (!bytes) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDateTime = (dateString: string) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

onMounted(() => {
  fetchVersions()
})
</script>

<style scoped>
.file-version-history {
  max-height: 80vh;
  overflow-y: auto;
}

.history-header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.history-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.history-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.upload-new-version {
  margin-bottom: 24px;
  text-align: right;
}

.version-timeline {
  max-height: 500px;
  overflow-y: auto;
}

.version-card {
  margin-bottom: 8px;
}

.version-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.version-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.version-meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #909399;
}

.version-actions {
  display: flex;
  gap: 4px;
}

.change-log {
  margin-bottom: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.change-log h5 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
}

.change-log p {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.version-details {
  margin-bottom: 12px;
}

.md5-hash {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
}

.version-compare {
  text-align: center;
}

.version-upload {
  width: 100%;
}

.version-comparison {
  padding: 16px 0;
}

.compare-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.compare-item {
  text-align: center;
  flex: 1;
}

.compare-item h4 {
  margin: 0 0 4px 0;
  color: #303133;
}

.compare-item p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.compare-vs {
  font-weight: bold;
  color: #409eff;
  font-size: 18px;
  margin: 0 20px;
}

.size-compare {
  display: flex;
  align-items: center;
  gap: 8px;
}

.size-diff {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 3px;
}

.size-increase {
  background-color: #fef0f0;
  color: #f56c6c;
}

.size-decrease {
  background-color: #f0f9ff;
  color: #409eff;
}

.size-same {
  background-color: #f5f7fa;
  color: #909399;
}

:deep(.el-timeline-item__timestamp) {
  font-size: 12px;
  color: #909399;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--small) {
  padding: 5px 11px;
  font-size: 12px;
}
</style>
