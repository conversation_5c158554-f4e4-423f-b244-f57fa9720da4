<template>
  <div class="file-share">
    <div class="share-header">
      <h3>分享文件: {{ file.name }}</h3>
      <p>設置文件的分享權限和有效期</p>
    </div>

    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="分享方式" prop="shareType">
        <el-radio-group v-model="form.shareType">
          <el-radio label="LINK">生成分享鏈接</el-radio>
          <el-radio label="USER">指定用戶分享</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 指定用戶分享 -->
      <el-form-item v-if="form.shareType === 'USER'" label="分享給" prop="sharedUsers">
        <el-select
          v-model="form.sharedUsers"
          multiple
          filterable
          remote
          placeholder="搜索並選擇用戶"
          :remote-method="searchUsers"
          :loading="userSearchLoading"
          style="width: 100%"
        >
          <el-option
            v-for="user in availableUsers"
            :key="user.id"
            :label="`${user.firstName} ${user.lastName} (${user.username})`"
            :value="user.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="訪問權限" prop="permission">
        <el-radio-group v-model="form.permission">
          <el-radio label="VIEW">僅查看</el-radio>
          <el-radio label="DOWNLOAD">可下載</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="有效期" prop="expiryType">
        <el-radio-group v-model="form.expiryType" @change="handleExpiryTypeChange">
          <el-radio label="NEVER">永不過期</el-radio>
          <el-radio label="DAYS">指定天數</el-radio>
          <el-radio label="DATE">指定日期</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item v-if="form.expiryType === 'DAYS'" label="有效天數" prop="expiryDays">
        <el-input-number
          v-model="form.expiryDays"
          :min="1"
          :max="365"
          placeholder="請輸入有效天數"
        />
        <span class="expiry-tip">天後過期</span>
      </el-form-item>

      <el-form-item v-if="form.expiryType === 'DATE'" label="過期日期" prop="expiryDate">
        <el-date-picker
          v-model="form.expiryDate"
          type="datetime"
          placeholder="選擇過期日期"
          :disabled-date="disabledDate"
          format="YYYY-MM-DD HH:mm"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>

      <el-form-item label="訪問密碼" prop="password">
        <el-input
          v-model="form.password"
          placeholder="設置訪問密碼（可選）"
          maxlength="20"
          show-password
        />
        <div class="password-tip">
          <small>設置密碼後，訪問者需要輸入密碼才能查看文件</small>
        </div>
      </el-form-item>

      <el-form-item label="分享說明" prop="message">
        <el-input
          v-model="form.message"
          type="textarea"
          placeholder="添加分享說明（可選）"
          :rows="3"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <!-- 分享結果 -->
    <div v-if="shareResult" class="share-result">
      <h4>分享成功</h4>

      <div v-if="form.shareType === 'LINK'" class="share-link">
        <el-input
          v-model="shareResult.shareUrl"
          readonly
          class="share-url-input"
        >
          <template #append>
            <el-button @click="copyShareUrl">
              <el-icon><DocumentCopy /></el-icon>
              複製
            </el-button>
          </template>
        </el-input>

        <div class="share-info">
          <p><strong>分享鏈接：</strong>{{ shareResult.shareUrl }}</p>
          <p v-if="form.password"><strong>訪問密碼：</strong>{{ form.password }}</p>
          <p><strong>有效期：</strong>{{ getExpiryText() }}</p>
          <p><strong>權限：</strong>{{ form.permission === 'VIEW' ? '僅查看' : '可下載' }}</p>
        </div>
      </div>

      <div v-else class="share-users">
        <p>已成功分享給以下用戶：</p>
        <div class="shared-user-list">
          <el-tag
            v-for="userId in form.sharedUsers"
            :key="userId"
            size="large"
            style="margin-right: 8px; margin-bottom: 8px"
          >
            {{ getUserName(userId) }}
          </el-tag>
        </div>
      </div>

      <!-- 二維碼 -->
      <div v-if="form.shareType === 'LINK'" class="qr-code">
        <h5>二維碼分享</h5>
        <div class="qr-placeholder">
          <el-icon><Picture /></el-icon>
          <p>二維碼功能開發中</p>
        </div>
      </div>
    </div>

    <div class="form-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button
        type="primary"
        @click="handleShare"
        :loading="sharing"
        :disabled="!canShare"
      >
        {{ sharing ? '分享中...' : '確認分享' }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { DocumentCopy, Picture } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import api from '../../services/api'

interface Props {
  file: any
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'share-success': []
  cancel: []
}>()

// 響應式數據
const formRef = ref<FormInstance>()
const sharing = ref(false)
const userSearchLoading = ref(false)
const availableUsers = ref<any[]>([])
const shareResult = ref<any>(null)

// 表單數據
const form = reactive({
  shareType: 'LINK',
  sharedUsers: [] as number[],
  permission: 'VIEW',
  expiryType: 'NEVER',
  expiryDays: 7,
  expiryDate: '',
  password: '',
  message: ''
})

// 表單驗證規則
const rules: FormRules = {
  shareType: [
    { required: true, message: '請選擇分享方式', trigger: 'change' }
  ],
  sharedUsers: [
    {
      validator: (rule, value, callback) => {
        if (form.shareType === 'USER' && (!value || value.length === 0)) {
          callback(new Error('請選擇要分享的用戶'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  permission: [
    { required: true, message: '請選擇訪問權限', trigger: 'change' }
  ],
  expiryType: [
    { required: true, message: '請選擇有效期類型', trigger: 'change' }
  ],
  expiryDays: [
    {
      validator: (rule, value, callback) => {
        if (form.expiryType === 'DAYS' && (!value || value < 1)) {
          callback(new Error('請輸入有效的天數'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  expiryDate: [
    {
      validator: (rule, value, callback) => {
        if (form.expiryType === 'DATE' && !value) {
          callback(new Error('請選擇過期日期'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 計算屬性
const canShare = computed(() => {
  if (form.shareType === 'USER') {
    return form.sharedUsers.length > 0
  }
  return true
})

// 方法
const searchUsers = async (query: string) => {
  if (!query) {
    availableUsers.value = []
    return
  }

  userSearchLoading.value = true
  try {
    const response = await api.searchUsers(query)
    availableUsers.value = response.data
  } catch (error) {
    console.error('搜索用戶失敗:', error)
  } finally {
    userSearchLoading.value = false
  }
}

const handleExpiryTypeChange = () => {
  if (form.expiryType === 'NEVER') {
    form.expiryDays = 7
    form.expiryDate = ''
  }
}

const disabledDate = (time: Date) => {
  return time.getTime() < Date.now()
}

const handleShare = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    sharing.value = true

    const shareData = {
      fileId: props.file.id,
      shareType: form.shareType,
      sharedUsers: form.shareType === 'USER' ? form.sharedUsers : [],
      permission: form.permission,
      expiryType: form.expiryType,
      expiryDays: form.expiryType === 'DAYS' ? form.expiryDays : null,
      expiryDate: form.expiryType === 'DATE' ? form.expiryDate : null,
      password: form.password || null,
      message: form.message || null
    }

    const response = await api.shareFile(shareData)
    shareResult.value = response.data

    ElMessage.success('文件分享成功')

    // 延遲觸發成功事件，讓用戶看到分享結果
    setTimeout(() => {
      emit('share-success')
    }, 3000)

  } catch (error) {
    console.error('分享文件失敗:', error)
    ElMessage.error('分享文件失敗')
  } finally {
    sharing.value = false
  }
}

const copyShareUrl = async () => {
  try {
    await navigator.clipboard.writeText(shareResult.value.shareUrl)
    ElMessage.success('分享鏈接已複製到剪貼板')
  } catch (error) {
    console.error('複製失敗:', error)
    ElMessage.error('複製失敗，請手動複製')
  }
}

const getUserName = (userId: number) => {
  const user = availableUsers.value.find(u => u.id === userId)
  return user ? `${user.firstName} ${user.lastName}` : `用戶 ${userId}`
}

const getExpiryText = () => {
  switch (form.expiryType) {
    case 'NEVER':
      return '永不過期'
    case 'DAYS':
      return `${form.expiryDays} 天後過期`
    case 'DATE':
      return `${form.expiryDate} 過期`
    default:
      return '未知'
  }
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.file-share {
  max-width: 600px;
}

.share-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.share-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.share-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.expiry-tip {
  margin-left: 8px;
  color: #909399;
  font-size: 14px;
}

.password-tip {
  margin-top: 8px;
  color: #909399;
}

.share-result {
  margin: 24px 0;
  padding: 20px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 8px;
}

.share-result h4 {
  margin: 0 0 16px 0;
  color: #409eff;
}

.share-link {
  margin-bottom: 20px;
}

.share-url-input {
  margin-bottom: 16px;
}

.share-info p {
  margin: 8px 0;
  color: #606266;
  font-size: 14px;
}

.shared-user-list {
  margin-top: 12px;
}

.qr-code {
  text-align: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e1ecff;
}

.qr-code h5 {
  margin: 0 0 12px 0;
  color: #303133;
}

.qr-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 40px;
  background-color: white;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  color: #909399;
}

.qr-placeholder .el-icon {
  font-size: 48px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #ebeef5;
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #303133;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}
</style>
