<template>
  <div class="file-upload">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="文件上傳" prop="files">
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          multiple
          :auto-upload="false"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :file-list="fileList"
          :accept="acceptedTypes"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            將文件拖到此處，或<em>點擊上傳</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 jpg/png/gif/pdf/doc/docx/xls/xlsx/ppt/pptx 等格式，單個文件不超過 100MB
            </div>
          </template>
        </el-upload>
      </el-form-item>

      <el-form-item label="相關專案" prop="projectId">
        <el-select
          v-model="form.projectId"
          placeholder="選擇相關專案（可選）"
          style="width: 100%"
          clearable
          filterable
        >
          <el-option
            v-for="project in projects"
            :key="project.id"
            :label="project.name"
            :value="project.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="文件分類" prop="category">
        <el-select
          v-model="form.category"
          placeholder="選擇文件分類"
          style="width: 100%"
        >
          <el-option label="合約文件" value="CONTRACT" />
          <el-option label="需求文檔" value="REQUIREMENT" />
          <el-option label="設計文檔" value="DESIGN" />
          <el-option label="技術文檔" value="TECHNICAL" />
          <el-option label="測試文檔" value="TEST" />
          <el-option label="用戶手冊" value="MANUAL" />
          <el-option label="其他" value="OTHER" />
        </el-select>
      </el-form-item>

      <el-form-item label="文件描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          placeholder="請輸入文件描述（可選）"
          :rows="3"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="訪問權限" prop="accessLevel">
        <el-radio-group v-model="form.accessLevel">
          <el-radio label="PUBLIC">公開</el-radio>
          <el-radio label="PROJECT">專案成員</el-radio>
          <el-radio label="PRIVATE">僅自己</el-radio>
        </el-radio-group>
        <div class="access-level-tip">
          <small>
            公開：所有用戶可查看；專案成員：僅專案成員可查看；僅自己：只有上傳者可查看
          </small>
        </div>
      </el-form-item>

      <el-form-item label="標籤" prop="tags">
        <el-select
          v-model="form.tags"
          multiple
          filterable
          allow-create
          default-first-option
          placeholder="添加標籤（可選）"
          style="width: 100%"
        >
          <el-option
            v-for="tag in commonTags"
            :key="tag"
            :label="tag"
            :value="tag"
          />
        </el-select>
      </el-form-item>

      <!-- 上傳進度 -->
      <div v-if="uploading" class="upload-progress">
        <el-progress
          :percentage="uploadProgress"
          :status="uploadStatus"
          :stroke-width="8"
        />
        <p class="progress-text">{{ progressText }}</p>
      </div>

      <!-- 上傳結果 -->
      <div v-if="uploadResults.length > 0" class="upload-results">
        <h4>上傳結果</h4>
        <div
          v-for="result in uploadResults"
          :key="result.name"
          class="result-item"
        >
          <el-icon
            :class="result.success ? 'success-icon' : 'error-icon'"
          >
            <Check v-if="result.success" />
            <Close v-else />
          </el-icon>
          <span class="file-name">{{ result.name }}</span>
          <span class="result-message">{{ result.message }}</span>
        </div>
      </div>
    </el-form>

    <div class="form-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button
        type="primary"
        @click="handleUpload"
        :loading="uploading"
        :disabled="fileList.length === 0"
      >
        {{ uploading ? '上傳中...' : '開始上傳' }}
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled, Check, Close } from '@element-plus/icons-vue'
import type { FormInstance, FormRules, UploadFile, UploadFiles } from 'element-plus'
import api from '../../services/api'

// Props
const props = defineProps<{
  defaultProjectId?: number
}>()

const emit = defineEmits<{
  'upload-success': []
  cancel: []
}>()

// 響應式數據
const formRef = ref<FormInstance>()
const uploadRef = ref()
const uploading = ref(false)
const uploadProgress = ref(0)
const uploadStatus = ref<'success' | 'exception' | ''>('')
const progressText = ref('')
const fileList = ref<UploadFile[]>([])
const projects = ref<any[]>([])
const uploadResults = ref<any[]>([])

// 表單數據
const form = reactive({
  projectId: props.defaultProjectId || null as number | null,
  category: 'OTHER',
  description: '',
  accessLevel: 'PROJECT',
  tags: [] as string[]
})

// 常用標籤
const commonTags = [
  '重要', '緊急', '草稿', '最終版', '審核中', '已批准',
  '設計', '開發', '測試', '部署', '維護', '培訓'
]

// 接受的文件類型
const acceptedTypes = '.jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.zip,.rar'

// 表單驗證規則
const rules: FormRules = {
  files: [
    { required: true, message: '請選擇要上傳的文件', trigger: 'change' }
  ],
  category: [
    { required: true, message: '請選擇文件分類', trigger: 'change' }
  ],
  accessLevel: [
    { required: true, message: '請選擇訪問權限', trigger: 'change' }
  ]
}

// 方法
const fetchProjects = async () => {
  try {
    const response = await api.getProjects()
    projects.value = response.data
  } catch (error) {
    console.error('獲取專案列表失敗:', error)
  }
}

const handleFileChange = (file: UploadFile, files: UploadFiles) => {
  // 檢查文件大小
  if (file.size && file.size > 100 * 1024 * 1024) {
    ElMessage.error(`文件 ${file.name} 超過 100MB 限制`)
    files.splice(files.indexOf(file), 1)
    return
  }

  // 檢查文件類型
  const allowedTypes = [
    'image/jpeg', 'image/png', 'image/gif',
    'application/pdf',
    'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'application/zip', 'application/x-rar-compressed'
  ]

  if (file.raw && !allowedTypes.includes(file.raw.type)) {
    ElMessage.error(`不支持的文件類型: ${file.name}`)
    files.splice(files.indexOf(file), 1)
    return
  }

  fileList.value = files
}

const handleFileRemove = (file: UploadFile, files: UploadFiles) => {
  fileList.value = files
}

const handleUpload = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    if (fileList.value.length === 0) {
      ElMessage.error('請選擇要上傳的文件')
      return
    }

    uploading.value = true
    uploadProgress.value = 0
    uploadStatus.value = ''
    uploadResults.value = []
    progressText.value = '準備上傳...'

    const totalFiles = fileList.value.length
    let completedFiles = 0

    for (const file of fileList.value) {
      try {
        progressText.value = `正在上傳 ${file.name}...`

        const formData = new FormData()
        formData.append('file', file.raw!)
        formData.append('projectId', form.projectId?.toString() || '')
        formData.append('category', form.category)
        formData.append('description', form.description)
        formData.append('accessLevel', form.accessLevel)
        formData.append('tags', JSON.stringify(form.tags))

        await api.uploadFile(formData)

        uploadResults.value.push({
          name: file.name,
          success: true,
          message: '上傳成功'
        })

        completedFiles++
        uploadProgress.value = Math.round((completedFiles / totalFiles) * 100)

      } catch (error) {
        console.error(`上傳文件 ${file.name} 失敗:`, error)
        uploadResults.value.push({
          name: file.name,
          success: false,
          message: '上傳失敗'
        })
        completedFiles++
        uploadProgress.value = Math.round((completedFiles / totalFiles) * 100)
      }
    }

    uploadStatus.value = uploadResults.value.every(r => r.success) ? 'success' : 'exception'
    progressText.value = uploadStatus.value === 'success' ? '所有文件上傳完成' : '部分文件上傳失敗'

    if (uploadStatus.value === 'success') {
      ElMessage.success('文件上傳成功')
      setTimeout(() => {
        emit('upload-success')
      }, 1500)
    } else {
      ElMessage.warning('部分文件上傳失敗，請檢查上傳結果')
    }

  } catch (error) {
    console.error('表單驗證失敗:', error)
  } finally {
    uploading.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}

onMounted(() => {
  fetchProjects()
})
</script>

<style scoped>
.file-upload {
  max-width: 600px;
}

.upload-demo {
  width: 100%;
}

.access-level-tip {
  margin-top: 8px;
  color: #909399;
}

.upload-progress {
  margin: 20px 0;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.progress-text {
  margin: 8px 0 0 0;
  text-align: center;
  color: #606266;
  font-size: 14px;
}

.upload-results {
  margin: 20px 0;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.upload-results h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding: 8px;
  background-color: white;
  border-radius: 4px;
}

.success-icon {
  color: #67c23a;
}

.error-icon {
  color: #f56c6c;
}

.file-name {
  flex: 1;
  font-weight: 600;
  color: #303133;
}

.result-message {
  font-size: 12px;
  color: #909399;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #ebeef5;
}

:deep(.el-upload-dragger) {
  border-radius: 8px;
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: #303133;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}
</style>
