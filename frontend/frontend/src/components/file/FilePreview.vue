<template>
  <div class="file-preview">
    <div class="preview-header">
      <div class="file-info">
        <h3>{{ file.name }}</h3>
        <div class="file-meta">
          <el-tag size="small">{{ getFileTypeLabel(file.type) }}</el-tag>
          <span class="file-size">{{ formatFileSize(file.size) }}</span>
          <span class="upload-time">{{ formatDateTime(file.createdAt) }}</span>
        </div>
      </div>
      <div class="preview-actions">
        <el-button @click="downloadFile">
          <el-icon><Download /></el-icon>
          下載
        </el-button>
        <el-button @click="handleClose">
          <el-icon><Close /></el-icon>
          關閉
        </el-button>
      </div>
    </div>

    <div class="preview-content">
      <!-- 圖片預覽 -->
      <div v-if="file.type === 'IMAGE'" class="image-preview">
        <img :src="file.url" :alt="file.name" class="preview-image" />
      </div>

      <!-- PDF 預覽 -->
      <div v-else-if="file.type === 'DOCUMENT' && file.name.toLowerCase().endsWith('.pdf')" class="pdf-preview">
        <iframe :src="file.url" class="pdf-iframe" frameborder="0"></iframe>
      </div>

      <!-- 文本文件預覽 -->
      <div v-else-if="file.type === 'DOCUMENT' && isTextFile(file.name)" class="text-preview">
        <div v-if="textContent" class="text-content">
          <pre>{{ textContent }}</pre>
        </div>
        <div v-else class="loading-text">
          <el-icon class="is-loading"><Loading /></el-icon>
          正在加載文件內容...
        </div>
      </div>

      <!-- 視頻預覽 -->
      <div v-else-if="file.type === 'VIDEO'" class="video-preview">
        <video :src="file.url" controls class="preview-video">
          您的瀏覽器不支持視頻播放
        </video>
      </div>

      <!-- 音頻預覽 -->
      <div v-else-if="file.type === 'AUDIO'" class="audio-preview">
        <div class="audio-container">
          <el-icon class="audio-icon"><Microphone /></el-icon>
          <audio :src="file.url" controls class="preview-audio">
            您的瀏覽器不支持音頻播放
          </audio>
        </div>
      </div>

      <!-- Office 文檔預覽 -->
      <div v-else-if="isOfficeFile(file.name)" class="office-preview">
        <div class="office-container">
          <el-icon class="office-icon"><Document /></el-icon>
          <h4>{{ file.name }}</h4>
          <p>此文件類型需要下載後使用相應軟件打開</p>
          <el-button type="primary" @click="downloadFile">
            <el-icon><Download /></el-icon>
            下載文件
          </el-button>
        </div>
      </div>

      <!-- 不支持預覽的文件 -->
      <div v-else class="unsupported-preview">
        <div class="unsupported-container">
          <el-icon class="unsupported-icon"><Warning /></el-icon>
          <h4>無法預覽此文件</h4>
          <p>文件類型：{{ getFileTypeLabel(file.type) }}</p>
          <p>請下載文件後使用相應軟件打開</p>
          <el-button type="primary" @click="downloadFile">
            <el-icon><Download /></el-icon>
            下載文件
          </el-button>
        </div>
      </div>
    </div>

    <!-- 文件詳細信息 -->
    <div class="file-details">
      <el-descriptions title="文件信息" :column="2" border>
        <el-descriptions-item label="文件名">
          {{ file.name }}
        </el-descriptions-item>
        <el-descriptions-item label="文件大小">
          {{ formatFileSize(file.size) }}
        </el-descriptions-item>
        <el-descriptions-item label="文件類型">
          {{ getFileTypeLabel(file.type) }}
        </el-descriptions-item>
        <el-descriptions-item label="上傳者">
          {{ file.uploadedBy?.firstName }} {{ file.uploadedBy?.lastName }}
        </el-descriptions-item>
        <el-descriptions-item label="上傳時間">
          {{ formatDateTime(file.createdAt) }}
        </el-descriptions-item>
        <el-descriptions-item label="最後修改">
          {{ formatDateTime(file.updatedAt) }}
        </el-descriptions-item>
        <el-descriptions-item label="相關專案">
          {{ file.project?.name || '無' }}
        </el-descriptions-item>
        <el-descriptions-item label="訪問權限">
          {{ getAccessLevelLabel(file.accessLevel) }}
        </el-descriptions-item>
        <el-descriptions-item label="文件描述" :span="2">
          {{ file.description || '無描述' }}
        </el-descriptions-item>
        <el-descriptions-item label="標籤" :span="2">
          <el-tag
            v-for="tag in file.tags"
            :key="tag"
            size="small"
            style="margin-right: 8px"
          >
            {{ tag }}
          </el-tag>
          <span v-if="!file.tags || file.tags.length === 0">無標籤</span>
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Download,
  Close,
  Loading,
  Microphone,
  Document,
  Warning
} from '@element-plus/icons-vue'
import api from '../../services/api'

interface Props {
  file: any
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
}>()

// 響應式數據
const textContent = ref('')

// 方法
const downloadFile = async () => {
  try {
    const response = await api.downloadFile(props.file.id)
    // 創建下載鏈接
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', props.file.name)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)

    ElMessage.success('文件下載成功')
  } catch (error) {
    console.error('下載文件失敗:', error)
    ElMessage.error('下載文件失敗')
  }
}

const loadTextContent = async () => {
  if (!isTextFile(props.file.name)) return

  try {
    const response = await api.getFileContent(props.file.id)
    textContent.value = response.data
  } catch (error) {
    console.error('加載文件內容失敗:', error)
    textContent.value = '無法加載文件內容'
  }
}

const isTextFile = (fileName: string) => {
  const textExtensions = ['.txt', '.md', '.json', '.xml', '.csv', '.log']
  return textExtensions.some(ext => fileName.toLowerCase().endsWith(ext))
}

const isOfficeFile = (fileName: string) => {
  const officeExtensions = ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx']
  return officeExtensions.some(ext => fileName.toLowerCase().endsWith(ext))
}

const getFileTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    'DOCUMENT': '文檔',
    'IMAGE': '圖片',
    'VIDEO': '視頻',
    'AUDIO': '音頻',
    'ARCHIVE': '壓縮包',
    'OTHER': '其他'
  }
  return labelMap[type] || type
}

const getAccessLevelLabel = (level: string) => {
  const labelMap: Record<string, string> = {
    'PUBLIC': '公開',
    'PROJECT': '專案成員',
    'PRIVATE': '僅自己'
  }
  return labelMap[level] || level
}

const formatFileSize = (bytes: number) => {
  if (!bytes) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDateTime = (dateString: string) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const handleClose = () => {
  emit('close')
}

onMounted(() => {
  loadTextContent()
})
</script>

<style scoped>
.file-preview {
  max-height: 80vh;
  overflow-y: auto;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.file-info h3 {
  margin: 0 0 8px 0;
  color: #303133;
  word-break: break-all;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  color: #909399;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.preview-content {
  margin-bottom: 24px;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 圖片預覽 */
.image-preview {
  text-align: center;
  width: 100%;
}

.preview-image {
  max-width: 100%;
  max-height: 500px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* PDF 預覽 */
.pdf-preview {
  width: 100%;
  height: 500px;
}

.pdf-iframe {
  width: 100%;
  height: 100%;
  border-radius: 8px;
}

/* 文本預覽 */
.text-preview {
  width: 100%;
  height: 400px;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  overflow: auto;
}

.text-content {
  padding: 16px;
  height: 100%;
}

.text-content pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.loading-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

/* 視頻預覽 */
.video-preview {
  width: 100%;
}

.preview-video {
  width: 100%;
  max-height: 400px;
  border-radius: 8px;
}

/* 音頻預覽 */
.audio-preview {
  width: 100%;
}

.audio-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 40px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.audio-icon {
  font-size: 48px;
  color: #409eff;
}

.preview-audio {
  width: 100%;
  max-width: 400px;
}

/* Office 文檔預覽 */
.office-preview,
.unsupported-preview {
  width: 100%;
}

.office-container,
.unsupported-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 40px;
  background-color: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.office-icon,
.unsupported-icon {
  font-size: 48px;
  color: #409eff;
}

.unsupported-icon {
  color: #e6a23c;
}

.office-container h4,
.unsupported-container h4 {
  margin: 0;
  color: #303133;
}

.office-container p,
.unsupported-container p {
  margin: 0;
  color: #606266;
}

/* 文件詳細信息 */
.file-details {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #ebeef5;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}
</style>
