<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="120px"
    v-loading="loading"
  >
    <el-form-item label="客戶名稱" prop="name">
      <el-input v-model="form.name" placeholder="請輸入客戶名稱" />
    </el-form-item>
    
    <el-form-item label="聯絡人" prop="contactPerson">
      <el-input v-model="form.contactPerson" placeholder="請輸入聯絡人姓名" />
    </el-form-item>
    
    <el-form-item label="電話" prop="phone">
      <el-input v-model="form.phone" placeholder="請輸入電話號碼" />
    </el-form-item>
    
    <el-form-item label="電子郵件" prop="email">
      <el-input v-model="form.email" placeholder="請輸入電子郵件" />
    </el-form-item>
    
    <el-form-item label="地址" prop="address">
      <el-input v-model="form.address" placeholder="請輸入地址" type="textarea" :rows="2" />
    </el-form-item>
    
    <el-form-item label="客戶類型" prop="customerType">
      <el-select v-model="form.customerType" placeholder="請選擇客戶類型" clearable>
        <el-option
          v-for="type in customerTypes"
          :key="type.id"
          :label="type.name"
          :value="type.id"
        />
      </el-select>
    </el-form-item>
    
    <el-form-item label="標籤" prop="tags">
      <el-select
        v-model="form.tags"
        multiple
        filterable
        allow-create
        default-first-option
        placeholder="請選擇或新增標籤"
      >
        <el-option
          v-for="tag in tags"
          :key="tag.id"
          :label="tag.name"
          :value="tag.id"
          :style="{ backgroundColor: tag.color + '20' }"
        />
      </el-select>
    </el-form-item>
    
    <el-form-item>
      <el-button type="primary" @click="submitForm">{{ submitButtonText }}</el-button>
      <el-button @click="resetForm">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineProps, defineEmits } from 'vue'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import api from '../../services/api'

const props = defineProps({
  customerId: {
    type: Number,
    default: null
  },
  submitButtonText: {
    type: String,
    default: '儲存'
  }
})

const emit = defineEmits(['submit-success'])

const formRef = ref<FormInstance>()
const loading = ref(false)
const customerTypes = ref([])
const tags = ref([])

const form = reactive({
  name: '',
  contactPerson: '',
  phone: '',
  email: '',
  address: '',
  customerType: null as number | null,
  tags: [] as number[]
})

const rules = reactive<FormRules>({
  name: [
    { required: true, message: '請輸入客戶名稱', trigger: 'blur' },
    { max: 100, message: '長度不能超過 100 個字元', trigger: 'blur' }
  ],
  contactPerson: [
    { max: 50, message: '長度不能超過 50 個字元', trigger: 'blur' }
  ],
  phone: [
    { max: 20, message: '長度不能超過 20 個字元', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '請輸入有效的電子郵件地址', trigger: 'blur' },
    { max: 100, message: '長度不能超過 100 個字元', trigger: 'blur' }
  ],
  address: [
    { max: 255, message: '長度不能超過 255 個字元', trigger: 'blur' }
  ]
})

// Fetch customer types and tags
const fetchOptions = async () => {
  loading.value = true
  try {
    const [typesResponse, tagsResponse] = await Promise.all([
      api.getCustomerTypes(),
      api.getTags()
    ])
    
    customerTypes.value = typesResponse.data
    tags.value = tagsResponse.data
  } catch (error) {
    console.error('Failed to fetch options:', error)
    ElMessage.error('獲取選項資料失敗')
  } finally {
    loading.value = false
  }
}

// Fetch customer data if editing
const fetchCustomerData = async () => {
  if (!props.customerId) return
  
  loading.value = true
  try {
    const response = await api.getCustomer(props.customerId)
    const customer = response.data
    
    form.name = customer.name
    form.contactPerson = customer.contactPerson
    form.phone = customer.phone
    form.email = customer.email
    form.address = customer.address
    form.customerType = customer.customerType?.id || null
    form.tags = customer.tags?.map((tag: any) => tag.id) || []
  } catch (error) {
    console.error('Failed to fetch customer data:', error)
    ElMessage.error('獲取客戶資料失敗')
  } finally {
    loading.value = false
  }
}

// Submit form
const submitForm = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        // Prepare data
        const customerData = {
          name: form.name,
          contactPerson: form.contactPerson,
          phone: form.phone,
          email: form.email,
          address: form.address,
          customerType: form.customerType ? { id: form.customerType } : null,
          tags: form.tags.map(id => ({ id }))
        }
        
        // Create or update customer
        let response
        if (props.customerId) {
          response = await api.updateCustomer(props.customerId, customerData)
        } else {
          response = await api.createCustomer(customerData)
        }
        
        ElMessage.success(props.customerId ? '客戶資料已更新' : '客戶已新增')
        emit('submit-success', response.data)
        
        // Reset form if creating new customer
        if (!props.customerId) {
          resetForm()
        }
      } catch (error) {
        console.error('Failed to save customer:', error)
        ElMessage.error(props.customerId ? '更新客戶資料失敗' : '新增客戶失敗')
      } finally {
        loading.value = false
      }
    }
  })
}

// Reset form
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

onMounted(() => {
  fetchOptions()
  if (props.customerId) {
    fetchCustomerData()
  }
})
</script>

<style scoped>
.el-select {
  width: 100%;
}
</style>
