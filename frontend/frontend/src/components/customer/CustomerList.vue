<template>
  <div class="customer-list">
    <div class="customer-list-header">
      <div class="search-box">
        <el-input
          v-model="searchQuery"
          placeholder="搜尋客戶名稱"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #append>
            <el-button @click="handleSearch">
              <el-icon><Search /></el-icon>
            </el-button>
          </template>
        </el-input>
      </div>

      <div class="filter-box">
        <el-select
          v-model="selectedType"
          placeholder="客戶類型"
          clearable
          @change="handleTypeChange"
        >
          <el-option
            v-for="type in customerTypes"
            :key="type.id"
            :label="type.name"
            :value="type.id"
          />
        </el-select>

        <el-select
          v-model="selectedTag"
          placeholder="標籤"
          clearable
          @change="handleTagChange"
        >
          <el-option
            v-for="tag in tags"
            :key="tag.id"
            :label="tag.name"
            :value="tag.id"
            :style="{ backgroundColor: tag.color + '20' }"
          />
        </el-select>
      </div>

      <div class="action-box">
        <el-button type="primary" @click="goToCreateCustomer">
          <el-icon><Plus /></el-icon> 新增客戶
        </el-button>
      </div>
    </div>

    <el-table
      :data="customers"
      style="width: 100%"
      v-loading="loading"
      @row-click="handleRowClick"
    >
      <el-table-column prop="name" label="客戶名稱" min-width="150" />
      <el-table-column prop="contactPerson" label="聯絡人" width="120" />
      <el-table-column prop="phone" label="電話" width="120" />
      <el-table-column prop="email" label="電子郵件" min-width="180" />
      <el-table-column label="客戶類型" width="120">
        <template #default="scope">
          {{ scope.row.customerType?.name || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="標籤" width="200">
        <template #default="scope">
          <el-tag
            v-for="tag in scope.row.tags"
            :key="tag.id"
            :style="{ backgroundColor: tag.color + '20', marginRight: '5px' }"
            size="small"
          >
            {{ tag.name }}
          </el-tag>
          <span v-if="!scope.row.tags || scope.row.tags.length === 0">-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="scope">
          <el-button
            type="primary"
            size="small"
            @click.stop="viewCustomer(scope.row)"
          >
            查看
          </el-button>
          <el-button
            type="danger"
            size="small"
            @click.stop="handleDelete(scope.row)"
          >
            刪除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus } from '@element-plus/icons-vue'
import api from '../../services/api'

const emit = defineEmits(['view-customer', 'add-customer', 'refresh'])
const router = useRouter()

// Data
const customers = ref([])
const customerTypes = ref([])
const tags = ref([])
const loading = ref(false)
const searchQuery = ref('')
const selectedType = ref(null)
const selectedTag = ref(null)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// Fetch customers
const fetchCustomers = async () => {
  loading.value = true
  try {
    let response

    if (searchQuery.value) {
      response = await api.searchCustomers(searchQuery.value)
    } else if (selectedType.value) {
      response = await api.getCustomersByType(selectedType.value)
    } else if (selectedTag.value) {
      response = await api.getCustomersByTag(selectedTag.value)
    } else {
      response = await api.getCustomers()
    }

    customers.value = response.data
    total.value = response.data.length
  } catch (error) {
    console.error('Failed to fetch customers:', error)
    ElMessage.error('獲取客戶資料失敗')
  } finally {
    loading.value = false
  }
}

// Fetch customer types and tags
const fetchOptions = async () => {
  try {
    const [typesResponse, tagsResponse] = await Promise.all([
      api.getCustomerTypes(),
      api.getTags()
    ])

    customerTypes.value = typesResponse.data
    tags.value = tagsResponse.data
  } catch (error) {
    console.error('Failed to fetch options:', error)
    ElMessage.error('獲取選項資料失敗')
  }
}

// Handle search
const handleSearch = () => {
  currentPage.value = 1
  fetchCustomers()
}

// Handle type change
const handleTypeChange = () => {
  currentPage.value = 1
  selectedTag.value = null
  fetchCustomers()
}

// Handle tag change
const handleTagChange = () => {
  currentPage.value = 1
  selectedType.value = null
  fetchCustomers()
}

// Navigation methods
const goToCreateCustomer = () => {
  router.push({ name: 'customer-create' })
}

const viewCustomer = (customer: any) => {
  router.push({ name: 'customer-detail', params: { id: customer.id } })
}

// Handle row click
const handleRowClick = (row: any) => {
  viewCustomer(row)
}

// Handle delete
const handleDelete = (customer: any) => {
  ElMessageBox.confirm(
    `確定要刪除客戶 "${customer.name}" 嗎？`,
    '刪除客戶',
    {
      confirmButtonText: '確定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await api.deleteCustomer(customer.id)
      ElMessage.success('客戶已刪除')
      fetchCustomers()
      emit('refresh')
    } catch (error) {
      console.error('Failed to delete customer:', error)
      ElMessage.error('刪除客戶失敗')
    }
  }).catch(() => {
    // User cancelled
  })
}

// Pagination
const handleSizeChange = (val: number) => {
  pageSize.value = val
  fetchCustomers()
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  fetchCustomers()
}

// Watch for changes in search query
watch(searchQuery, (newVal, oldVal) => {
  if (newVal === '' && oldVal !== '') {
    fetchCustomers()
  }
})

onMounted(() => {
  fetchCustomers()
  fetchOptions()
})
</script>

<style scoped>
.customer-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.search-box {
  flex: 1;
  min-width: 250px;
}

.filter-box {
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .customer-list-header {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box, .filter-box, .action-box {
    width: 100%;
    margin-bottom: 10px;
  }

  .filter-box {
    flex-direction: column;
  }
}
</style>
