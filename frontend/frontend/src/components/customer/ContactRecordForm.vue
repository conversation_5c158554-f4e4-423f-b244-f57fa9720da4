<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="120px"
    @submit.prevent="handleSubmit"
  >
    <el-form-item label="客戶" prop="customerId">
      <el-select
        v-model="form.customerId"
        placeholder="請選擇客戶"
        filterable
        style="width: 100%"
        :loading="customersLoading"
      >
        <el-option
          v-for="customer in customers"
          :key="customer.id"
          :label="customer.name"
          :value="customer.id"
        />
      </el-select>
    </el-form-item>

    <el-form-item label="聯繫類型" prop="contactType">
      <el-select
        v-model="form.contactType"
        placeholder="請選擇聯繫類型"
        style="width: 100%"
      >
        <el-option label="電話" value="PHONE" />
        <el-option label="郵件" value="EMAIL" />
        <el-option label="會議" value="MEETING" />
        <el-option label="拜訪" value="VISIT" />
        <el-option label="其他" value="OTHER" />
      </el-select>
    </el-form-item>

    <el-form-item label="主題" prop="subject">
      <el-input
        v-model="form.subject"
        placeholder="請輸入聯繫主題"
        maxlength="200"
        show-word-limit
      />
    </el-form-item>

    <el-form-item label="聯繫內容" prop="content">
      <el-input
        v-model="form.content"
        type="textarea"
        :rows="4"
        placeholder="請輸入聯繫內容"
        maxlength="1000"
        show-word-limit
      />
    </el-form-item>

    <el-form-item label="聯繫日期" prop="contactDate">
      <el-date-picker
        v-model="form.contactDate"
        type="datetime"
        placeholder="選擇聯繫日期"
        style="width: 100%"
        format="YYYY-MM-DD HH:mm"
        value-format="YYYY-MM-DD HH:mm:ss"
      />
    </el-form-item>

    <el-form-item label="跟進日期" prop="followUpDate">
      <el-date-picker
        v-model="form.followUpDate"
        type="date"
        placeholder="選擇跟進日期（可選）"
        style="width: 100%"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
      />
    </el-form-item>

    <el-form-item label="結果" prop="outcome">
      <el-input
        v-model="form.outcome"
        type="textarea"
        :rows="3"
        placeholder="請輸入聯繫結果（可選）"
        maxlength="500"
        show-word-limit
      />
    </el-form-item>

    <el-form-item>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        {{ isEdit ? '更新' : '創建' }}
      </el-button>
      <el-button @click="handleCancel">取消</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useContactRecordStore, type ContactRecord } from '../../stores/contactRecord'
import api from '../../services/api'

interface Props {
  contactRecord?: ContactRecord | null
  isEdit?: boolean
}

interface Emits {
  (e: 'success', contactRecord: ContactRecord): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  contactRecord: null,
  isEdit: false
})

const emit = defineEmits<Emits>()

const contactRecordStore = useContactRecordStore()
const formRef = ref<FormInstance>()
const loading = ref(false)
const customersLoading = ref(false)
const customers = ref<any[]>([])

const form = reactive({
  customerId: '',
  contactType: 'PHONE' as 'PHONE' | 'EMAIL' | 'MEETING' | 'VISIT' | 'OTHER',
  subject: '',
  content: '',
  contactDate: '',
  followUpDate: '',
  outcome: ''
})

const rules: FormRules = {
  customerId: [
    { required: true, message: '請選擇客戶', trigger: 'change' }
  ],
  contactType: [
    { required: true, message: '請選擇聯繫類型', trigger: 'change' }
  ],
  subject: [
    { required: true, message: '請輸入聯繫主題', trigger: 'blur' },
    { min: 2, max: 200, message: '主題長度在 2 到 200 個字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '請輸入聯繫內容', trigger: 'blur' },
    { min: 10, message: '聯繫內容至少需要10個字符', trigger: 'blur' }
  ],
  contactDate: [
    { required: true, message: '請選擇聯繫日期', trigger: 'change' }
  ]
}

const fetchCustomers = async () => {
  customersLoading.value = true
  try {
    const response = await api.getCustomers()
    customers.value = response.data
  } catch (error) {
    ElMessage.error('獲取客戶列表失敗')
  } finally {
    customersLoading.value = false
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    const recordData = {
      customer: { id: Number(form.customerId) },
      contactType: form.contactType,
      subject: form.subject,
      content: form.content,
      contactDate: form.contactDate,
      followUpDate: form.followUpDate || undefined,
      outcome: form.outcome || undefined
    }

    let result: ContactRecord
    if (props.isEdit && props.contactRecord?.id) {
      result = await contactRecordStore.updateContactRecord(props.contactRecord.id, recordData)
    } else {
      result = await contactRecordStore.createContactRecord(recordData)
    }

    emit('success', result)
  } catch (error) {
    console.error('提交聯繫記錄失敗:', error)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.customerId = ''
  form.contactType = 'PHONE'
  form.subject = ''
  form.content = ''
  form.contactDate = ''
  form.followUpDate = ''
  form.outcome = ''
}

// Watch for contactRecord changes to populate form
watch(() => props.contactRecord, (newRecord) => {
  if (newRecord && props.isEdit) {
    form.customerId = newRecord.customer.id.toString()
    form.contactType = newRecord.contactType
    form.subject = newRecord.subject
    form.content = newRecord.content
    form.contactDate = newRecord.contactDate
    form.followUpDate = newRecord.followUpDate || ''
    form.outcome = newRecord.outcome || ''
  } else {
    resetForm()
  }
}, { immediate: true })

onMounted(() => {
  fetchCustomers()
  // Set default contact date to now
  if (!props.isEdit) {
    form.contactDate = new Date().toISOString().slice(0, 19).replace('T', ' ')
  }
})

defineExpose({
  resetForm
})
</script>

<style scoped>
.el-form {
  max-width: 600px;
}
</style>
