<template>
  <div class="contact-record-list">
    <!-- 搜索和篩選 -->
    <div class="filter-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-select
            v-model="filters.customerId"
            placeholder="篩選客戶"
            clearable
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="customer in customers"
              :key="customer.id"
              :label="customer.name"
              :value="customer.id"
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="filters.contactType"
            placeholder="聯繫類型"
            clearable
            style="width: 100%"
          >
            <el-option label="電話" value="PHONE" />
            <el-option label="郵件" value="EMAIL" />
            <el-option label="會議" value="MEETING" />
            <el-option label="拜訪" value="VISIT" />
            <el-option label="其他" value="OTHER" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-date-picker
            v-model="filters.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="開始日期"
            end-placeholder="結束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%"
          />
        </el-col>
        <el-col :span="6" class="text-right">
          <el-button type="primary" @click="handleCreate">
            <el-icon><Plus /></el-icon>
            新增記錄
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 記錄列表 -->
    <el-table
      :data="contactRecords"
      v-loading="loading"
      stripe
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="80" />
      
      <el-table-column label="客戶" min-width="150">
        <template #default="{ row }">
          <div>
            <div class="customer-name">{{ row.customer.name }}</div>
            <div class="customer-contact">
              <span v-if="row.customer.email">{{ row.customer.email }}</span>
              <span v-if="row.customer.phone">{{ row.customer.phone }}</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="聯繫類型" width="100">
        <template #default="{ row }">
          <el-tag :type="getContactTypeColor(row.contactType)">
            {{ getContactTypeText(row.contactType) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="subject" label="主題" min-width="200" />

      <el-table-column label="聯繫內容" min-width="200">
        <template #default="{ row }">
          <el-tooltip :content="row.content" placement="top">
            <span class="content-preview">{{ truncateText(row.content, 50) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column label="聯繫人" min-width="120">
        <template #default="{ row }">
          {{ row.user.firstName }} {{ row.user.lastName }}
        </template>
      </el-table-column>

      <el-table-column prop="contactDate" label="聯繫日期" width="160">
        <template #default="{ row }">
          {{ formatDateTime(row.contactDate) }}
        </template>
      </el-table-column>

      <el-table-column label="跟進日期" width="120">
        <template #default="{ row }">
          <span v-if="row.followUpDate">{{ row.followUpDate }}</span>
          <span v-else class="text-muted">無</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="handleView(row)"
          >
            查看
          </el-button>
          <el-button
            type="warning"
            size="small"
            @click="handleEdit(row)"
          >
            編輯
          </el-button>
          <el-popconfirm
            title="確定要刪除這個聯繫記錄嗎？"
            @confirm="handleDelete(row.id)"
          >
            <template #reference>
              <el-button
                type="danger"
                size="small"
              >
                刪除
              </el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分頁 -->
    <div class="pagination-section">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 查看詳情對話框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="聯繫記錄詳情"
      width="600px"
    >
      <div v-if="selectedRecord" class="record-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="客戶">
            {{ selectedRecord.customer.name }}
          </el-descriptions-item>
          <el-descriptions-item label="聯繫類型">
            <el-tag :type="getContactTypeColor(selectedRecord.contactType)">
              {{ getContactTypeText(selectedRecord.contactType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="主題">
            {{ selectedRecord.subject }}
          </el-descriptions-item>
          <el-descriptions-item label="聯繫人">
            {{ selectedRecord.user.firstName }} {{ selectedRecord.user.lastName }}
          </el-descriptions-item>
          <el-descriptions-item label="聯繫日期">
            {{ formatDateTime(selectedRecord.contactDate) }}
          </el-descriptions-item>
          <el-descriptions-item label="跟進日期">
            {{ selectedRecord.followUpDate || '無' }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="content-section">
          <h4>聯繫內容</h4>
          <p>{{ selectedRecord.content }}</p>
        </div>
        
        <div v-if="selectedRecord.outcome" class="outcome-section">
          <h4>聯繫結果</h4>
          <p>{{ selectedRecord.outcome }}</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { useContactRecordStore, type ContactRecord } from '../../stores/contactRecord'
import api from '../../services/api'

interface Emits {
  (e: 'create'): void
  (e: 'edit', record: ContactRecord): void
}

const emit = defineEmits<Emits>()

const contactRecordStore = useContactRecordStore()
const loading = ref(false)
const customers = ref<any[]>([])
const viewDialogVisible = ref(false)
const selectedRecord = ref<ContactRecord | null>(null)

const filters = reactive({
  customerId: '',
  contactType: '',
  dateRange: [] as string[]
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const contactRecords = computed(() => contactRecordStore.contactRecords)

const getContactTypeColor = (type: string) => {
  const colors = {
    PHONE: 'success',
    EMAIL: 'primary',
    MEETING: 'warning',
    VISIT: 'danger',
    OTHER: 'info'
  }
  return colors[type as keyof typeof colors] || 'info'
}

const getContactTypeText = (type: string) => {
  const texts = {
    PHONE: '電話',
    EMAIL: '郵件',
    MEETING: '會議',
    VISIT: '拜訪',
    OTHER: '其他'
  }
  return texts[type as keyof typeof texts] || type
}

const truncateText = (text: string, maxLength: number) => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-TW')
}

const fetchCustomers = async () => {
  try {
    const response = await api.getCustomers()
    customers.value = response.data
  } catch (error) {
    console.error('獲取客戶列表失敗:', error)
  }
}

const fetchContactRecords = async () => {
  loading.value = true
  try {
    await contactRecordStore.fetchContactRecords()
    pagination.total = contactRecords.value.length
  } catch (error) {
    console.error('獲取聯繫記錄失敗:', error)
  } finally {
    loading.value = false
  }
}

const handleCreate = () => {
  emit('create')
}

const handleView = (record: ContactRecord) => {
  selectedRecord.value = record
  viewDialogVisible.value = true
}

const handleEdit = (record: ContactRecord) => {
  emit('edit', record)
}

const handleDelete = async (id: number) => {
  try {
    await contactRecordStore.deleteContactRecord(id)
    fetchContactRecords()
  } catch (error) {
    console.error('刪除聯繫記錄失敗:', error)
  }
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  fetchContactRecords()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  fetchContactRecords()
}

onMounted(() => {
  fetchCustomers()
  fetchContactRecords()
})
</script>

<style scoped>
.contact-record-list {
  padding: 20px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
}

.text-right {
  text-align: right;
}

.customer-name {
  font-weight: 600;
  margin-bottom: 4px;
}

.customer-contact {
  font-size: 12px;
  color: #999;
}

.content-preview {
  cursor: pointer;
}

.text-muted {
  color: #999;
}

.pagination-section {
  margin-top: 20px;
  text-align: center;
}

.record-detail {
  padding: 20px 0;
}

.content-section,
.outcome-section {
  margin-top: 20px;
}

.content-section h4,
.outcome-section h4 {
  margin-bottom: 10px;
  color: #333;
}

.content-section p,
.outcome-section p {
  line-height: 1.6;
  color: #666;
}
</style>
