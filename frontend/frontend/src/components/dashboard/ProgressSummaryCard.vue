<template>
  <div class="progress-summary-card">
    <el-card class="summary-card">
      <template #header>
        <div class="card-header">
          <span>進度總覽</span>
          <el-button 
            type="text" 
            size="small" 
            @click="refreshData"
            :loading="loading"
          >
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>
      </template>

      <div v-loading="loading" class="summary-content">
        <!-- 整體統計 -->
        <div class="overall-stats">
          <div class="stat-row">
            <div class="stat-item">
              <div class="stat-icon projects">
                <el-icon><FolderOpened /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.totalProjects }}</div>
                <div class="stat-label">總專案數</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon tasks">
                <el-icon><List /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.totalTasks }}</div>
                <div class="stat-label">總任務數</div>
              </div>
            </div>
          </div>
          
          <div class="stat-row">
            <div class="stat-item">
              <div class="stat-icon completed">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.completedTasks }}</div>
                <div class="stat-label">已完成任務</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon overdue">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.overdueTasks }}</div>
                <div class="stat-label">逾期任務</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 進度指標 -->
        <div class="progress-indicators">
          <div class="indicator-item">
            <div class="indicator-header">
              <span class="indicator-label">整體完成率</span>
              <span class="indicator-value">{{ stats.overallCompletion }}%</span>
            </div>
            <el-progress 
              :percentage="stats.overallCompletion" 
              :color="getProgressColor(stats.overallCompletion)"
              :stroke-width="8"
            />
          </div>
          
          <div class="indicator-item">
            <div class="indicator-header">
              <span class="indicator-label">按時完成率</span>
              <span class="indicator-value">{{ stats.onTimeCompletion }}%</span>
            </div>
            <el-progress 
              :percentage="stats.onTimeCompletion" 
              color="#67c23a"
              :stroke-width="8"
            />
          </div>
        </div>

        <!-- 狀態分布 -->
        <div class="status-distribution">
          <div class="distribution-header">
            <span>專案狀態分布</span>
          </div>
          <div class="distribution-content">
            <div 
              v-for="(item, index) in statusDistribution" 
              :key="index"
              class="distribution-item"
            >
              <el-tag :type="item.type" size="small">{{ item.label }}</el-tag>
              <span class="distribution-count">{{ item.count }}</span>
              <span class="distribution-percentage">({{ item.percentage }}%)</span>
            </div>
          </div>
        </div>

        <!-- 近期活動 -->
        <div class="recent-activities">
          <div class="activities-header">
            <span>近期活動</span>
          </div>
          <div class="activities-content">
            <div 
              v-for="activity in recentActivities" 
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon" :class="activity.type">
                <el-icon>
                  <component :is="activity.icon" />
                </el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-time">{{ formatTime(activity.time) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Refresh,
  FolderOpened, 
  List, 
  CircleCheck, 
  Warning,
  Plus,
  Edit,
  Check
} from '@element-plus/icons-vue'
import api from '../../services/api'

const loading = ref(false)
const projects = ref<any[]>([])
const tasks = ref<any[]>([])

// 計算統計數據
const stats = computed(() => {
  const totalProjects = projects.value.length
  const totalTasks = tasks.value.length
  const completedTasks = tasks.value.filter(task => task.status === 'COMPLETED').length
  const overdueTasks = tasks.value.filter(task => {
    const now = new Date()
    return new Date(task.dueDate) < now && task.status !== 'COMPLETED'
  }).length
  
  const overallCompletion = totalTasks > 0 ? 
    Math.round((completedTasks / totalTasks) * 100) : 0
  
  const onTimeCompletion = totalTasks > 0 ? 
    Math.round(((completedTasks - overdueTasks) / totalTasks) * 100) : 0

  return {
    totalProjects,
    totalTasks,
    completedTasks,
    overdueTasks,
    overallCompletion,
    onTimeCompletion
  }
})

// 狀態分布
const statusDistribution = computed(() => {
  const statusCount = {}
  projects.value.forEach(project => {
    statusCount[project.status] = (statusCount[project.status] || 0) + 1
  })
  
  const statusMap = {
    'PLANNING': { label: '規劃中', type: 'info' },
    'IN_PROGRESS': { label: '進行中', type: 'warning' },
    'COMPLETED': { label: '已完成', type: 'success' },
    'DELAYED': { label: '延期', type: 'danger' },
    'CLOSED': { label: '已結案', type: 'success' }
  }
  
  return Object.entries(statusCount).map(([status, count]) => ({
    label: statusMap[status]?.label || status,
    type: statusMap[status]?.type || 'info',
    count: count as number,
    percentage: projects.value.length > 0 ? 
      Math.round((count as number / projects.value.length) * 100) : 0
  }))
})

// 近期活動（模擬數據）
const recentActivities = computed(() => [
  {
    id: 1,
    title: '新增專案「網站重構」',
    type: 'create',
    icon: 'Plus',
    time: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2小時前
  },
  {
    id: 2,
    title: '更新任務「UI設計」進度至80%',
    type: 'update',
    icon: 'Edit',
    time: new Date(Date.now() - 4 * 60 * 60 * 1000) // 4小時前
  },
  {
    id: 3,
    title: '完成任務「需求分析」',
    type: 'complete',
    icon: 'Check',
    time: new Date(Date.now() - 6 * 60 * 60 * 1000) // 6小時前
  }
])

// 方法
const fetchData = async () => {
  loading.value = true
  try {
    const [projectsResponse, tasksResponse] = await Promise.all([
      api.getProjects(),
      api.getTasks()
    ])
    
    projects.value = projectsResponse.data
    tasks.value = tasksResponse.data
  } catch (error) {
    console.error('Failed to fetch data:', error)
    ElMessage.error('獲取數據失敗')
  } finally {
    loading.value = false
  }
}

const refreshData = () => {
  fetchData()
}

const getProgressColor = (percentage: number) => {
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const hours = Math.floor(diff / (1000 * 60 * 60))
  
  if (hours < 1) {
    const minutes = Math.floor(diff / (1000 * 60))
    return `${minutes}分鐘前`
  } else if (hours < 24) {
    return `${hours}小時前`
  } else {
    const days = Math.floor(hours / 24)
    return `${days}天前`
  }
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.progress-summary-card {
  width: 100%;
}

.summary-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-content {
  padding: 10px 0;
}

.overall-stats {
  margin-bottom: 25px;
}

.stat-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.stat-row:last-child {
  margin-bottom: 0;
}

.stat-item {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.stat-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
}

.stat-icon.projects { background-color: #409eff; }
.stat-icon.tasks { background-color: #67c23a; }
.stat-icon.completed { background-color: #e6a23c; }
.stat-icon.overdue { background-color: #f56c6c; }

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 11px;
  color: #606266;
}

.progress-indicators {
  margin-bottom: 25px;
}

.indicator-item {
  margin-bottom: 15px;
}

.indicator-item:last-child {
  margin-bottom: 0;
}

.indicator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.indicator-label {
  font-size: 13px;
  color: #606266;
}

.indicator-value {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.status-distribution,
.recent-activities {
  margin-bottom: 20px;
}

.distribution-header,
.activities-header {
  font-size: 13px;
  color: #606266;
  margin-bottom: 10px;
  font-weight: 500;
}

.distribution-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.distribution-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.distribution-count {
  font-weight: bold;
  color: #303133;
}

.distribution-percentage {
  color: #909399;
}

.activities-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.activity-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
}

.activity-icon.create { background-color: #409eff; }
.activity-icon.update { background-color: #e6a23c; }
.activity-icon.complete { background-color: #67c23a; }

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 12px;
  color: #303133;
  margin-bottom: 2px;
}

.activity-time {
  font-size: 11px;
  color: #909399;
}
</style>
