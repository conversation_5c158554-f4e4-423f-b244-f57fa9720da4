<template>
  <div class="notification-list" v-loading="loading">
    <el-empty v-if="notifications.length === 0" description="暫無通知" />
    
    <el-card
      v-for="notification in notifications"
      :key="notification.id"
      class="notification-item"
      :class="{ 'notification-unread': !notification.read }"
    >
      <div class="notification-content">
        <div class="notification-icon">
          <el-icon :size="24" :color="getNotificationColor(notification.type)">
            <component :is="getNotificationIcon(notification.type)" />
          </el-icon>
        </div>
        <div class="notification-info">
          <div class="notification-title">{{ notification.title }}</div>
          <div class="notification-message">{{ notification.message }}</div>
          <div class="notification-time">{{ formatDate(notification.createdAt) }}</div>
        </div>
        <div class="notification-actions">
          <el-button
            v-if="!notification.read"
            type="primary"
            size="small"
            plain
            @click="$emit('mark-read', notification.id)"
          >
            標為已讀
          </el-button>
          <el-button
            type="primary"
            size="small"
            @click="$emit('view', notification)"
          >
            查看
          </el-button>
          <el-button
            type="danger"
            size="small"
            plain
            @click="$emit('delete', notification.id)"
          >
            刪除
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { 
  Bell, 
  Message, 
  Document, 
  Warning, 
  InfoFilled 
} from '@element-plus/icons-vue'

const props = defineProps({
  notifications: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})

defineEmits(['mark-read', 'delete', 'view'])

// Helper functions
const getNotificationIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    'TASK_ASSIGNED': Document,
    'TASK_DUE_SOON': Warning,
    'TASK_OVERDUE': Warning,
    'PROJECT_UPDATE': InfoFilled,
    'SYSTEM': Bell
  }
  return iconMap[type] || Message
}

const getNotificationColor = (type: string) => {
  const colorMap: Record<string, string> = {
    'TASK_ASSIGNED': '#409EFF',
    'TASK_DUE_SOON': '#E6A23C',
    'TASK_OVERDUE': '#F56C6C',
    'PROJECT_UPDATE': '#67C23A',
    'SYSTEM': '#909399'
  }
  return colorMap[type] || '#409EFF'
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-TW')
}
</script>

<style scoped>
.notification-list {
  min-height: 200px;
}

.notification-item {
  margin-bottom: 16px;
  transition: all 0.3s;
}

.notification-unread {
  border-left: 4px solid #409EFF;
}

.notification-content {
  display: flex;
  align-items: flex-start;
}

.notification-icon {
  margin-right: 16px;
  padding-top: 4px;
}

.notification-info {
  flex: 1;
}

.notification-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
  color: #303133;
}

.notification-message {
  color: #606266;
  margin-bottom: 8px;
}

.notification-time {
  color: #909399;
  font-size: 12px;
}

.notification-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

@media (max-width: 768px) {
  .notification-content {
    flex-direction: column;
  }
  
  .notification-icon {
    margin-bottom: 12px;
  }
  
  .notification-actions {
    margin-top: 12px;
    flex-direction: row;
  }
}
</style>
