<template>
  <AppLayout>
    <div class="tag-view">
      <div class="page-header">
        <h1>標籤管理</h1>
        <p>管理系統標籤，用於分類和標記</p>
      </div>

      <div class="content-container">
        <!-- 操作區域 -->
        <div class="action-section">
          <el-button type="primary" @click="handleCreate">
            <el-icon><Plus /></el-icon>
            新增標籤
          </el-button>
        </div>

        <!-- 標籤列表 -->
        <el-table
          :data="tags"
          v-loading="loading"
          stripe
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" />
          
          <el-table-column label="標籤名稱" min-width="150">
            <template #default="{ row }">
              <el-tag
                :color="row.color"
                :style="{ color: getTextColor(row.color) }"
                size="large"
              >
                {{ row.name }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="color" label="顏色" width="120">
            <template #default="{ row }">
              <div class="color-display">
                <div
                  class="color-box"
                  :style="{ backgroundColor: row.color }"
                ></div>
                <span>{{ row.color }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column prop="description" label="描述" min-width="200">
            <template #default="{ row }">
              <span v-if="row.description">{{ row.description }}</span>
              <span v-else class="text-muted">無描述</span>
            </template>
          </el-table-column>

          <el-table-column prop="createdAt" label="創建時間" width="180">
            <template #default="{ row }">
              {{ formatDate(row.createdAt) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button
                type="warning"
                size="small"
                @click="handleEdit(row)"
              >
                編輯
              </el-button>
              <el-popconfirm
                title="確定要刪除這個標籤嗎？"
                @confirm="handleDelete(row.id)"
              >
                <template #reference>
                  <el-button
                    type="danger"
                    size="small"
                  >
                    刪除
                  </el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 創建/編輯標籤對話框 -->
      <el-dialog
        v-model="dialogVisible"
        :title="isEdit ? '編輯標籤' : '創建標籤'"
        width="500px"
      >
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="80px"
        >
          <el-form-item label="名稱" prop="name">
            <el-input
              v-model="form.name"
              placeholder="請輸入標籤名稱"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="顏色" prop="color">
            <div class="color-picker-container">
              <el-color-picker
                v-model="form.color"
                :predefine="predefineColors"
              />
              <el-input
                v-model="form.color"
                placeholder="#000000"
                style="margin-left: 10px; width: 120px;"
              />
            </div>
          </el-form-item>

          <el-form-item label="描述" prop="description">
            <el-input
              v-model="form.description"
              type="textarea"
              :rows="3"
              placeholder="請輸入標籤描述（可選）"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="預覽">
            <el-tag
              :color="form.color"
              :style="{ color: getTextColor(form.color) }"
              size="large"
            >
              {{ form.name || '標籤預覽' }}
            </el-tag>
          </el-form-item>
        </el-form>

        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleSubmit" :loading="submitting">
              {{ isEdit ? '更新' : '創建' }}
            </el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import AppLayout from '../components/layout/AppLayout.vue'
import { useTagStore, type Tag } from '../stores/tag'

const tagStore = useTagStore()
const formRef = ref<FormInstance>()
const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const currentTagId = ref<number | null>(null)

const form = reactive({
  name: '',
  color: '#409EFF',
  description: ''
})

const rules: FormRules = {
  name: [
    { required: true, message: '請輸入標籤名稱', trigger: 'blur' },
    { min: 1, max: 50, message: '標籤名稱長度在 1 到 50 個字符', trigger: 'blur' }
  ],
  color: [
    { required: true, message: '請選擇標籤顏色', trigger: 'change' }
  ]
}

const predefineColors = [
  '#ff4500',
  '#ff8c00',
  '#ffd700',
  '#90ee90',
  '#00ced1',
  '#1e90ff',
  '#c71585',
  '#409EFF',
  '#67C23A',
  '#E6A23C',
  '#F56C6C',
  '#909399'
]

const tags = computed(() => tagStore.sortedTags)

const getTextColor = (backgroundColor: string) => {
  if (!backgroundColor) return '#000000'
  
  // 移除 # 符號
  const hex = backgroundColor.replace('#', '')
  
  // 轉換為 RGB
  const r = parseInt(hex.substr(0, 2), 16)
  const g = parseInt(hex.substr(2, 2), 16)
  const b = parseInt(hex.substr(4, 2), 16)
  
  // 計算亮度
  const brightness = (r * 299 + g * 587 + b * 114) / 1000
  
  // 根據亮度選擇文字顏色
  return brightness > 128 ? '#000000' : '#ffffff'
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-TW')
}

const fetchTags = async () => {
  loading.value = true
  try {
    await tagStore.fetchTags()
  } catch (error) {
    console.error('獲取標籤失敗:', error)
  } finally {
    loading.value = false
  }
}

const handleCreate = () => {
  resetForm()
  isEdit.value = false
  currentTagId.value = null
  dialogVisible.value = true
}

const handleEdit = (tag: Tag) => {
  form.name = tag.name
  form.color = tag.color || '#409EFF'
  form.description = tag.description || ''
  isEdit.value = true
  currentTagId.value = tag.id!
  dialogVisible.value = true
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    const tagData = {
      name: form.name,
      color: form.color,
      description: form.description || undefined
    }

    if (isEdit.value && currentTagId.value) {
      await tagStore.updateTag(currentTagId.value, tagData)
    } else {
      await tagStore.createTag(tagData)
    }

    dialogVisible.value = false
    fetchTags()
  } catch (error) {
    console.error('提交標籤失敗:', error)
  } finally {
    submitting.value = false
  }
}

const handleDelete = async (id: number) => {
  try {
    await tagStore.deleteTag(id)
    fetchTags()
  } catch (error) {
    console.error('刪除標籤失敗:', error)
  }
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.name = ''
  form.color = '#409EFF'
  form.description = ''
}

onMounted(() => {
  fetchTags()
})
</script>

<style scoped>
.tag-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.action-section {
  margin-bottom: 20px;
}

.color-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.color-box {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}

.text-muted {
  color: #999;
}

.color-picker-container {
  display: flex;
  align-items: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
