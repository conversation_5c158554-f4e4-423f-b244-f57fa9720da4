<template>
  <AppLayout>
    <div class="notification-container">
      <div class="notification-header">
        <h1 class="notification-title">通知中心</h1>
        <div class="notification-actions">
          <el-button type="primary" @click="markAllAsRead" :disabled="!hasUnread">
            全部標為已讀
          </el-button>
        </div>
      </div>
      
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="全部通知" name="all">
          <NotificationList 
            :notifications="notifications" 
            :loading="loading"
            @mark-read="markAsRead"
            @delete="deleteNotification"
            @view="viewNotification"
          />
        </el-tab-pane>
        <el-tab-pane label="未讀通知" name="unread">
          <NotificationList 
            :notifications="unreadNotifications" 
            :loading="loading"
            @mark-read="markAsRead"
            @delete="deleteNotification"
            @view="viewNotification"
          />
        </el-tab-pane>
      </el-tabs>
      
      <el-pagination
        v-if="notifications.length > 0"
        layout="prev, pager, next"
        :total="total"
        :page-size="pageSize"
        :current-page="currentPage"
        @current-change="handlePageChange"
        class="notification-pagination"
      />
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import AppLayout from '../components/layout/AppLayout.vue'
import NotificationList from '../components/notification/NotificationList.vue'
import api from '../services/api'

const router = useRouter()
const loading = ref(false)
const activeTab = ref('all')
const notifications = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// Computed properties
const unreadNotifications = computed(() => {
  return notifications.value.filter((notification: any) => !notification.read)
})

const hasUnread = computed(() => {
  return unreadNotifications.value.length > 0
})

// Fetch notifications
const fetchNotifications = async () => {
  loading.value = true
  try {
    const response = await api.getMyNotifications()
    notifications.value = response.data
    total.value = response.data.length
  } catch (error) {
    console.error('Failed to fetch notifications:', error)
    ElMessage.error('獲取通知失敗')
  } finally {
    loading.value = false
  }
}

// Mark notification as read
const markAsRead = async (id: number) => {
  try {
    await api.markNotificationAsRead(id)
    
    // Update local state
    const notification = notifications.value.find((n: any) => n.id === id)
    if (notification) {
      notification.read = true
    }
    
    ElMessage.success('已標記為已讀')
  } catch (error) {
    console.error('Failed to mark notification as read:', error)
    ElMessage.error('標記已讀失敗')
  }
}

// Mark all notifications as read
const markAllAsRead = async () => {
  try {
    await api.markAllNotificationsAsRead()
    
    // Update local state
    notifications.value.forEach((notification: any) => {
      notification.read = true
    })
    
    ElMessage.success('已全部標記為已讀')
  } catch (error) {
    console.error('Failed to mark all notifications as read:', error)
    ElMessage.error('標記全部已讀失敗')
  }
}

// Delete notification
const deleteNotification = async (id: number) => {
  ElMessageBox.confirm(
    '確定要刪除此通知嗎？',
    '刪除通知',
    {
      confirmButtonText: '確定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await api.deleteNotification(id)
      
      // Update local state
      notifications.value = notifications.value.filter((n: any) => n.id !== id)
      total.value = notifications.value.length
      
      ElMessage.success('通知已刪除')
    } catch (error) {
      console.error('Failed to delete notification:', error)
      ElMessage.error('刪除通知失敗')
    }
  }).catch(() => {
    // User cancelled
  })
}

// View notification
const viewNotification = async (notification: any) => {
  // Mark as read if not already read
  if (!notification.read) {
    await markAsRead(notification.id)
  }
  
  // Navigate to the linked page if available
  if (notification.link) {
    router.push(notification.link)
  }
}

// Handle tab click
const handleTabClick = () => {
  currentPage.value = 1
}

// Handle page change
const handlePageChange = (page: number) => {
  currentPage.value = page
}

// Fetch notifications on mount
onMounted(() => {
  fetchNotifications()
})
</script>

<style scoped>
.notification-container {
  padding: 20px;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.notification-title {
  font-size: 24px;
  color: #303133;
  margin: 0;
}

.notification-pagination {
  margin-top: 20px;
  text-align: center;
}
</style>
