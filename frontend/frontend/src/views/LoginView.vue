<template>
  <div class="login-container">
    <div class="login-card">
      <h1 class="login-title">專案管理系統</h1>
      <el-tabs v-model="activeTab" class="login-tabs">
        <el-tab-pane label="登入" name="login">
          <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="loginRules"
            label-position="top"
            @submit.prevent="handleLogin"
          >
            <el-form-item label="使用者名稱" prop="username">
              <el-input v-model="loginForm.username" placeholder="請輸入使用者名稱" />
            </el-form-item>
            <el-form-item label="密碼" prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="請輸入密碼"
                show-password
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" native-type="submit" :loading="loading" class="login-button">
                登入
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="註冊" name="register" v-if="showRegister">
          <el-form
            ref="registerFormRef"
            :model="registerForm"
            :rules="registerRules"
            label-position="top"
            @submit.prevent="handleRegister"
          >
            <el-form-item label="使用者名稱" prop="username">
              <el-input v-model="registerForm.username" placeholder="請輸入使用者名稱" />
            </el-form-item>
            <el-form-item label="名字" prop="firstName">
              <el-input v-model="registerForm.firstName" placeholder="請輸入名字" />
            </el-form-item>
            <el-form-item label="姓氏" prop="lastName">
              <el-input v-model="registerForm.lastName" placeholder="請輸入姓氏" />
            </el-form-item>
            <el-form-item label="電子郵件" prop="email">
              <el-input v-model="registerForm.email" placeholder="請輸入電子郵件" />
            </el-form-item>
            <el-form-item label="密碼" prop="password">
              <el-input
                v-model="registerForm.password"
                type="password"
                placeholder="請輸入密碼"
                show-password
              />
            </el-form-item>
            <el-form-item label="確認密碼" prop="confirmPassword">
              <el-input
                v-model="registerForm.confirmPassword"
                type="password"
                placeholder="請再次輸入密碼"
                show-password
              />
            </el-form-item>
            <el-form-item label="角色">
              <el-select v-model="registerForm.roles" placeholder="請選擇角色" style="width: 100%" multiple>
                <el-option label="超級管理員" value="super_admin" />
                <el-option label="管理員" value="admin" />
                <el-option label="部門主管" value="department_head" />
                <el-option label="員工" value="employee" />
                <el-option label="訪客" value="guest" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" native-type="submit" :loading="loading" class="login-button">
                註冊
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import { useAuthStore } from '../stores/auth'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// Show register tab (can be disabled in production)
const showRegister = ref(true)

// Active tab
const activeTab = ref('login')

// Loading state
const loading = ref(false)

// Form references
const loginFormRef = ref<FormInstance>()
const registerFormRef = ref<FormInstance>()

// Login form
const loginForm = reactive({
  username: '',
  password: ''
})

// Register form
const registerForm = reactive({
  username: '',
  firstName: '',
  lastName: '',
  email: '',
  password: '',
  confirmPassword: '',
  roles: ['super_admin'] as string[] // 默認為超級管理員
})

// Validation rules
const loginRules = reactive<FormRules>({
  username: [
    { required: true, message: '請輸入使用者名稱', trigger: 'blur' },
    { min: 3, max: 20, message: '長度必須在 3 到 20 個字元之間', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '請輸入密碼', trigger: 'blur' },
    { min: 6, max: 40, message: '長度必須在 6 到 40 個字元之間', trigger: 'blur' }
  ]
})

const validateConfirmPassword = (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('請再次輸入密碼'))
  } else if (value !== registerForm.password) {
    callback(new Error('兩次輸入的密碼不一致'))
  } else {
    callback()
  }
}

const registerRules = reactive<FormRules>({
  username: [
    { required: true, message: '請輸入使用者名稱', trigger: 'blur' },
    { min: 3, max: 20, message: '長度必須在 3 到 20 個字元之間', trigger: 'blur' }
  ],
  firstName: [
    { required: true, message: '請輸入名字', trigger: 'blur' },
    { max: 50, message: '長度不能超過 50 個字元', trigger: 'blur' }
  ],
  lastName: [
    { required: true, message: '請輸入姓氏', trigger: 'blur' },
    { max: 50, message: '長度不能超過 50 個字元', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '請輸入電子郵件', trigger: 'blur' },
    { type: 'email', message: '請輸入有效的電子郵件地址', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '請輸入密碼', trigger: 'blur' },
    { min: 6, max: 40, message: '長度必須在 6 到 40 個字元之間', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '請再次輸入密碼', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
})

// Handle login
const handleLogin = async () => {
  if (!loginFormRef.value) return

  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        await authStore.login(loginForm.username, loginForm.password)
        // Redirect to the original requested page or dashboard
        const redirectPath = route.query.redirect as string || '/dashboard'
        router.push(redirectPath)
      } catch (error: any) {
        ElMessage.error(error.response?.data?.message || '登入失敗，請檢查您的憑證')
      } finally {
        loading.value = false
      }
    }
  })
}

// Handle register
const handleRegister = async () => {
  if (!registerFormRef.value) return

  await registerFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        await authStore.register(
          registerForm.username,
          registerForm.firstName,
          registerForm.lastName,
          registerForm.email,
          registerForm.password,
          registerForm.roles
        )
        ElMessage.success('註冊成功，請登入')
        activeTab.value = 'login'
        // Clear register form
        registerForm.username = ''
        registerForm.firstName = ''
        registerForm.lastName = ''
        registerForm.email = ''
        registerForm.password = ''
        registerForm.confirmPassword = ''
        registerForm.roles = ['super_admin']
      } catch (error: any) {
        ElMessage.error(error.response?.data?.message || '註冊失敗，請稍後再試')
      } finally {
        loading.value = false
      }
    }
  })
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f7fa;
}

.login-card {
  width: 400px;
  padding: 30px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.login-title {
  text-align: center;
  margin-bottom: 20px;
  color: #409EFF;
}

.login-tabs {
  margin-bottom: 20px;
}

.login-button {
  width: 100%;
}
</style>
