<template>
  <div class="resource-view">
    <h1>資源管理</h1>
    
    <el-tabs v-model="activeTab">
      <el-tab-pane label="人力資源" name="human">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>人力資源使用狀況</span>
              <el-button type="primary" @click="exportHumanResourceData">匯出報表</el-button>
            </div>
          </template>
          
          <el-table :data="humanResources" style="width: 100%">
            <el-table-column prop="name" label="姓名" />
            <el-table-column prop="department" label="部門" />
            <el-table-column prop="role" label="職位" />
            <el-table-column prop="assignedTasks" label="指派任務數" />
            <el-table-column prop="workload" label="工作負載">
              <template #default="scope">
                <el-progress 
                  :percentage="scope.row.workload" 
                  :status="getWorkloadStatus(scope.row.workload)" 
                />
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-button size="small" @click="viewUserTasks(scope.row)">查看任務</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>
      
      <el-tab-pane label="設備資源" name="equipment">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>設備資源管理</span>
              <el-button type="primary" @click="openAddEquipmentDialog">新增設備</el-button>
            </div>
          </template>
          
          <el-table :data="equipments" style="width: 100%">
            <el-table-column prop="name" label="設備名稱" />
            <el-table-column prop="type" label="類型" />
            <el-table-column prop="status" label="狀態">
              <template #default="scope">
                <el-tag :type="getEquipmentStatusType(scope.row.status)">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="assignedTo" label="使用者" />
            <el-table-column prop="project" label="專案" />
            <el-table-column label="操作">
              <template #default="scope">
                <el-button size="small" @click="editEquipment(scope.row)">編輯</el-button>
                <el-button size="small" type="primary" @click="assignEquipment(scope.row)">分配</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>
      
      <el-tab-pane label="材料資源" name="material">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>材料資源管理</span>
              <el-button type="primary" @click="openAddMaterialDialog">新增材料</el-button>
            </div>
          </template>
          
          <el-table :data="materials" style="width: 100%">
            <el-table-column prop="name" label="材料名稱" />
            <el-table-column prop="type" label="類型" />
            <el-table-column prop="quantity" label="數量" />
            <el-table-column prop="unit" label="單位" />
            <el-table-column prop="project" label="使用專案" />
            <el-table-column label="操作">
              <template #default="scope">
                <el-button size="small" @click="editMaterial(scope.row)">編輯</el-button>
                <el-button size="small" type="primary" @click="assignMaterial(scope.row)">分配</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const activeTab = ref('human')

// Mock data for demonstration
const humanResources = ref([
  {
    id: 1,
    name: '王大明',
    department: '管理部',
    role: '部門主管',
    assignedTasks: 3,
    workload: 65
  },
  {
    id: 2,
    name: '李小華',
    department: '業務部',
    role: '業務經理',
    assignedTasks: 5,
    workload: 85
  },
  {
    id: 3,
    name: '張小明',
    department: '技術部',
    role: '資深工程師',
    assignedTasks: 4,
    workload: 75
  },
  {
    id: 4,
    name: '陳小美',
    department: '技術部',
    role: '工程師',
    assignedTasks: 2,
    workload: 45
  }
])

const equipments = ref([
  {
    id: 1,
    name: '筆記型電腦 A',
    type: '電腦設備',
    status: '使用中',
    assignedTo: '張小明',
    project: '專案管理系統開發'
  },
  {
    id: 2,
    name: '投影機 B',
    type: '會議設備',
    status: '閒置中',
    assignedTo: '',
    project: ''
  },
  {
    id: 3,
    name: '伺服器 C',
    type: '網路設備',
    status: '使用中',
    assignedTo: '系統管理員',
    project: '多個專案'
  }
])

const materials = ref([
  {
    id: 1,
    name: '辦公用紙',
    type: '辦公用品',
    quantity: 500,
    unit: '張',
    project: '多個專案'
  },
  {
    id: 2,
    name: '墨水匣',
    type: '辦公用品',
    quantity: 10,
    unit: '個',
    project: '多個專案'
  },
  {
    id: 3,
    name: '網路線',
    type: '網路設備',
    quantity: 100,
    unit: '米',
    project: '網路升級專案'
  }
])

const getWorkloadStatus = (workload: number) => {
  if (workload > 80) return 'exception'
  if (workload > 60) return 'warning'
  return 'success'
}

const getEquipmentStatusType = (status: string) => {
  switch (status) {
    case '使用中':
      return 'success'
    case '閒置中':
      return 'info'
    case '維修中':
      return 'warning'
    case '報廢':
      return 'danger'
    default:
      return ''
  }
}

const exportHumanResourceData = () => {
  // Implement export functionality
  console.log('Export human resource data')
}

const viewUserTasks = (user: any) => {
  // Implement view user tasks functionality
  console.log('View user tasks', user)
}

const openAddEquipmentDialog = () => {
  // Implement add equipment functionality
  console.log('Open add equipment dialog')
}

const editEquipment = (equipment: any) => {
  // Implement edit equipment functionality
  console.log('Edit equipment', equipment)
}

const assignEquipment = (equipment: any) => {
  // Implement assign equipment functionality
  console.log('Assign equipment', equipment)
}

const openAddMaterialDialog = () => {
  // Implement add material functionality
  console.log('Open add material dialog')
}

const editMaterial = (material: any) => {
  // Implement edit material functionality
  console.log('Edit material', material)
}

const assignMaterial = (material: any) => {
  // Implement assign material functionality
  console.log('Assign material', material)
}

onMounted(() => {
  // In a real application, you would fetch resources from the API
  console.log('ResourceView component mounted')
})
</script>

<style scoped>
.resource-view {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.el-tabs {
  margin-top: 20px;
}
</style>
