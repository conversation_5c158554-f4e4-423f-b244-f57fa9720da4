<template>
  <AppLayout>
    <div class="resource-view">
      <div class="page-header">
        <h1>資源管理</h1>
        <p>管理系統中的所有資源</p>
        <div class="header-actions">
          <el-button type="primary" @click="goToCreateResource">
            <el-icon><Plus /></el-icon>
            新增資源
          </el-button>
        </div>
      </div>

      <!-- 搜索和篩選 -->
      <div class="filter-section">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              v-model="searchQuery"
              placeholder="搜索資源名稱..."
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="selectedType"
              placeholder="資源類型"
              clearable
              @change="fetchResources"
            >
              <el-option label="人力" value="HUMAN" />
              <el-option label="材料" value="MATERIAL" />
              <el-option label="設備" value="EQUIPMENT" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="selectedAvailability"
              placeholder="可用狀態"
              clearable
              @change="fetchResources"
            >
              <el-option label="可用" :value="true" />
              <el-option label="不可用" :value="false" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button @click="resetFilters">重置篩選</el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 資源列表 -->
      <el-table
        :data="filteredResources"
        v-loading="loading"
        stripe
        style="width: 100%"
        @row-click="handleRowClick"
      >
        <el-table-column prop="id" label="ID" width="80" />

        <el-table-column prop="name" label="資源名稱" min-width="150">
          <template #default="{ row }">
            <div class="resource-name">{{ row.name }}</div>
          </template>
        </el-table-column>

        <el-table-column prop="type" label="類型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)">
              {{ getTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />

        <el-table-column prop="available" label="可用狀態" width="100">
          <template #default="{ row }">
            <el-tag :type="row.available ? 'success' : 'danger'">
              {{ row.available ? '可用' : '不可用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="統計信息" width="150">
          <template #default="{ row }">
            <div class="stats">
              <div>分配數: {{ row.allocationCount || 0 }}</div>
              <div>使用率: {{ (row.utilizationRate || 0).toFixed(1) }}%</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="createdAt" label="創建時間" width="180">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click.stop="viewResource(row)"
            >
              查看
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click.stop="editResource(row)"
            >
              編輯
            </el-button>
            <el-popconfirm
              title="確定要刪除這個資源嗎？"
              @confirm="handleDelete(row.id)"
            >
              <template #reference>
                <el-button
                  type="danger"
                  size="small"
                  @click.stop
                >
                  刪除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分頁 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import AppLayout from '../components/layout/AppLayout.vue'
import api from '../services/api'

const router = useRouter()

// Data
const resources = ref<any[]>([])
const loading = ref(false)
const searchQuery = ref('')
const selectedType = ref('')
const selectedAvailability = ref<boolean | null>(null)

// Pagination
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// Computed
const filteredResources = computed(() => {
  let filtered = resources.value

  if (searchQuery.value) {
    filtered = filtered.filter(resource =>
      resource.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  if (selectedType.value) {
    filtered = filtered.filter(resource => resource.type === selectedType.value)
  }

  if (selectedAvailability.value !== null) {
    filtered = filtered.filter(resource => resource.available === selectedAvailability.value)
  }

  total.value = filtered.length
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filtered.slice(start, end)
})

// Methods
const fetchResources = async () => {
  loading.value = true
  try {
    const response = await api.getResources()
    resources.value = response.data
  } catch (error) {
    console.error('獲取資源列表失敗:', error)
    ElMessage.error('獲取資源列表失敗')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
}

const resetFilters = () => {
  searchQuery.value = ''
  selectedType.value = ''
  selectedAvailability.value = null
  currentPage.value = 1
  fetchResources()
}

const goToCreateResource = () => {
  router.push({ name: 'resource-create' })
}

const viewResource = (resource: any) => {
  router.push({ name: 'resource-detail', params: { id: resource.id } })
}

const editResource = (resource: any) => {
  router.push({ name: 'resource-edit', params: { id: resource.id } })
}

const handleRowClick = (row: any) => {
  viewResource(row)
}

const handleDelete = async (id: number) => {
  try {
    await api.deleteResource(id)
    ElMessage.success('資源已刪除')
    fetchResources()
  } catch (error) {
    console.error('刪除資源失敗:', error)
    ElMessage.error('刪除資源失敗')
  }
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    HUMAN: '人力',
    MATERIAL: '材料',
    EQUIPMENT: '設備'
  }
  return labels[type] || type
}

const getTypeTagType = (type: string) => {
  const types: Record<string, string> = {
    HUMAN: 'success',
    MATERIAL: 'warning',
    EQUIPMENT: 'info'
  }
  return types[type] || ''
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-TW')
}

onMounted(() => {
  fetchResources()
})
</script>

<style scoped>
.resource-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #303133;
}

.page-header p {
  margin: 5px 0 0 0;
  color: #606266;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
}

.resource-name {
  font-weight: 600;
  color: #303133;
}

.stats {
  font-size: 12px;
  color: #606266;
}

.stats div {
  margin-bottom: 2px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-actions {
    margin-top: 10px;
  }
}
</style>
