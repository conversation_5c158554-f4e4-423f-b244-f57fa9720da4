<template>
  <AppLayout>
    <div class="resource-detail-view">
      <div class="page-header">
        <div class="header-left">
          <el-button @click="goBack" type="text">
            <el-icon><ArrowLeft /></el-icon>
            返回資源列表
          </el-button>
          <h1>{{ resource?.name || '資源詳情' }}</h1>
        </div>
        <div class="header-actions">
          <el-button type="warning" @click="editResource">
            <el-icon><Edit /></el-icon>
            編輯資源
          </el-button>
        </div>
      </div>

      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <div v-else-if="resource" class="resource-content">
        <!-- 基本信息 -->
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
            </div>
          </template>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>資源名稱：</label>
                <span>{{ resource.name }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>資源類型：</label>
                <el-tag :type="getTypeTagType(resource.type)">
                  {{ getTypeLabel(resource.type) }}
                </el-tag>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>可用狀態：</label>
                <el-tag :type="resource.available ? 'success' : 'danger'">
                  {{ resource.available ? '可用' : '不可用' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>創建時間：</label>
                <span>{{ formatDate(resource.createdAt) }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <div class="info-item">
                <label>描述：</label>
                <span>{{ resource.description || '無描述' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 統計信息 -->
        <el-card class="stats-card">
          <template #header>
            <div class="card-header">
              <span>統計信息</span>
            </div>
          </template>

          <el-row :gutter="20">
            <el-col :span="8">
              <div class="stat-item">
                <div class="stat-value">{{ resource.allocationCount || 0 }}</div>
                <div class="stat-label">總分配數</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-item">
                <div class="stat-value">{{ resource.activeAllocationCount || 0 }}</div>
                <div class="stat-label">活躍分配數</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="stat-item">
                <div class="stat-value">{{ (resource.utilizationRate || 0).toFixed(1) }}%</div>
                <div class="stat-label">使用率</div>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 資源分配記錄 -->
        <el-card class="allocations-card">
          <template #header>
            <div class="card-header">
              <span>資源分配記錄</span>
              <el-button type="primary" size="small" @click="showCreateAllocationDialog">
                新增分配
              </el-button>
            </div>
          </template>

          <el-table
            :data="allocations"
            v-loading="allocationsLoading"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="id" label="ID" width="80" />

            <el-table-column label="專案" min-width="150">
              <template #default="{ row }">
                <span v-if="row.project">{{ row.project.name }}</span>
                <span v-else class="text-muted">無專案</span>
              </template>
            </el-table-column>

            <el-table-column label="任務" min-width="150">
              <template #default="{ row }">
                <span v-if="row.task">{{ row.task.title }}</span>
                <span v-else class="text-muted">無任務</span>
              </template>
            </el-table-column>

            <el-table-column prop="startDate" label="開始日期" width="120">
              <template #default="{ row }">
                {{ formatDate(row.startDate) }}
              </template>
            </el-table-column>

            <el-table-column prop="endDate" label="結束日期" width="120">
              <template #default="{ row }">
                {{ formatDate(row.endDate) }}
              </template>
            </el-table-column>

            <el-table-column prop="quantity" label="數量" width="80" />

            <el-table-column prop="cost" label="成本" width="100">
              <template #default="{ row }">
                {{ row.cost ? `$${row.cost}` : '-' }}
              </template>
            </el-table-column>

            <el-table-column label="狀態" width="100">
              <template #default="{ row }">
                <el-tag :type="getAllocationStatusType(row)">
                  {{ getAllocationStatus(row) }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="150" fixed="right">
              <template #default="{ row }">
                <el-button
                  type="warning"
                  size="small"
                  @click="editAllocation(row)"
                >
                  編輯
                </el-button>
                <el-popconfirm
                  title="確定要刪除這個分配記錄嗎？"
                  @confirm="deleteAllocation(row.id)"
                >
                  <template #reference>
                    <el-button
                      type="danger"
                      size="small"
                    >
                      刪除
                    </el-button>
                  </template>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>

      <div v-else class="error-container">
        <el-result
          icon="error"
          title="資源不存在"
          sub-title="請檢查資源ID是否正確"
        >
          <template #extra>
            <el-button type="primary" @click="goBack">返回資源列表</el-button>
          </template>
        </el-result>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Edit } from '@element-plus/icons-vue'
import AppLayout from '../components/layout/AppLayout.vue'
import api from '../services/api'

const router = useRouter()
const route = useRoute()

// Data
const resource = ref<any>(null)
const allocations = ref<any[]>([])
const loading = ref(false)
const allocationsLoading = ref(false)

// Methods
const fetchResource = async () => {
  const id = Number(route.params.id)
  if (!id) return

  loading.value = true
  try {
    const response = await api.getResource(id)
    resource.value = response.data
  } catch (error) {
    console.error('獲取資源詳情失敗:', error)
    ElMessage.error('獲取資源詳情失敗')
  } finally {
    loading.value = false
  }
}

const fetchAllocations = async () => {
  const id = Number(route.params.id)
  if (!id) return

  allocationsLoading.value = true
  try {
    const response = await api.getResourceAllocationsByResourceId(id)
    allocations.value = response.data
  } catch (error) {
    console.error('獲取資源分配記錄失敗:', error)
    ElMessage.error('獲取資源分配記錄失敗')
  } finally {
    allocationsLoading.value = false
  }
}

const goBack = () => {
  router.push({ name: 'resources' })
}

const editResource = () => {
  router.push({ name: 'resource-edit', params: { id: route.params.id } })
}

const showCreateAllocationDialog = () => {
  // TODO: 實現新增分配對話框
  ElMessage.info('新增分配功能開發中...')
}

const editAllocation = (allocation: any) => {
  // TODO: 實現編輯分配功能
  ElMessage.info('編輯分配功能開發中...')
}

const deleteAllocation = async (id: number) => {
  try {
    await api.deleteResourceAllocation(id)
    ElMessage.success('分配記錄已刪除')
    fetchAllocations()
  } catch (error) {
    console.error('刪除分配記錄失敗:', error)
    ElMessage.error('刪除分配記錄失敗')
  }
}

const getTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    HUMAN: '人力',
    MATERIAL: '材料',
    EQUIPMENT: '設備'
  }
  return labels[type] || type
}

const getTypeTagType = (type: string) => {
  const types: Record<string, string> = {
    HUMAN: 'success',
    MATERIAL: 'warning',
    EQUIPMENT: 'info'
  }
  return types[type] || ''
}

const getAllocationStatus = (allocation: any) => {
  const now = new Date()
  const startDate = new Date(allocation.startDate)
  const endDate = new Date(allocation.endDate)

  if (now < startDate) return '未開始'
  if (now > endDate) return '已結束'
  return '進行中'
}

const getAllocationStatusType = (allocation: any) => {
  const status = getAllocationStatus(allocation)
  const types: Record<string, string> = {
    '未開始': 'info',
    '進行中': 'success',
    '已結束': 'warning'
  }
  return types[status] || ''
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-TW')
}

onMounted(() => {
  fetchResource()
  fetchAllocations()
})
</script>

<style scoped>
.resource-detail-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.header-left h1 {
  margin: 10px 0 0 0;
  color: #303133;
}

.loading-container,
.error-container {
  padding: 40px;
  text-align: center;
}

.resource-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-card,
.stats-card,
.allocations-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item {
  margin-bottom: 15px;
}

.info-item label {
  font-weight: 600;
  color: #606266;
  margin-right: 10px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.text-muted {
  color: #909399;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-actions {
    margin-top: 10px;
  }
}
</style>
