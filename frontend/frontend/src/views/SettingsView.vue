<template>
  <AppLayout>
    <div class="settings-view">
      <div class="page-header">
        <h1>系統設置</h1>
        <p>管理系統配置和自定義設置</p>
      </div>

      <div class="content-container">
        <!-- 設置分類標籤 -->
        <el-tabs v-model="activeTab" type="card">
          <el-tab-pane label="系統設置" name="system">
            <div class="settings-section">
              <div class="section-header">
                <h3>系統設置</h3>
                <el-button type="primary" @click="handleCreate('SYSTEM')">
                  <el-icon><Plus /></el-icon>
                  新增系統設置
                </el-button>
              </div>
              
              <el-table
                :data="systemSettings"
                v-loading="loading"
                stripe
                style="width: 100%"
              >
                <el-table-column prop="key" label="設置鍵" min-width="200" />
                <el-table-column label="設置值" min-width="200">
                  <template #default="{ row }">
                    <div class="setting-value">
                      <span v-if="row.dataType === 'BOOLEAN'">
                        <el-tag :type="row.value === 'true' ? 'success' : 'danger'">
                          {{ row.value === 'true' ? '是' : '否' }}
                        </el-tag>
                      </span>
                      <span v-else>{{ row.value }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="dataType" label="數據類型" width="120">
                  <template #default="{ row }">
                    <el-tag size="small" type="info">{{ row.dataType }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述" min-width="200">
                  <template #default="{ row }">
                    <span v-if="row.description">{{ row.description }}</span>
                    <span v-else class="text-muted">無描述</span>
                  </template>
                </el-table-column>
                <el-table-column label="公開" width="80">
                  <template #default="{ row }">
                    <el-tag :type="row.isPublic ? 'success' : 'warning'" size="small">
                      {{ row.isPublic ? '是' : '否' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150" fixed="right">
                  <template #default="{ row }">
                    <el-button
                      type="warning"
                      size="small"
                      @click="handleEdit(row)"
                    >
                      編輯
                    </el-button>
                    <el-popconfirm
                      title="確定要刪除這個設置嗎？"
                      @confirm="handleDelete(row.id)"
                    >
                      <template #reference>
                        <el-button
                          type="danger"
                          size="small"
                        >
                          刪除
                        </el-button>
                      </template>
                    </el-popconfirm>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="自定義設置" name="custom">
            <div class="settings-section">
              <div class="section-header">
                <h3>自定義設置</h3>
                <el-button type="primary" @click="handleCreate('CUSTOM')">
                  <el-icon><Plus /></el-icon>
                  新增自定義設置
                </el-button>
              </div>
              
              <el-table
                :data="customSettings"
                v-loading="loading"
                stripe
                style="width: 100%"
              >
                <el-table-column prop="key" label="設置鍵" min-width="200" />
                <el-table-column label="設置值" min-width="200">
                  <template #default="{ row }">
                    <div class="setting-value">
                      <span v-if="row.dataType === 'BOOLEAN'">
                        <el-tag :type="row.value === 'true' ? 'success' : 'danger'">
                          {{ row.value === 'true' ? '是' : '否' }}
                        </el-tag>
                      </span>
                      <span v-else>{{ row.value }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="dataType" label="數據類型" width="120">
                  <template #default="{ row }">
                    <el-tag size="small" type="info">{{ row.dataType }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述" min-width="200">
                  <template #default="{ row }">
                    <span v-if="row.description">{{ row.description }}</span>
                    <span v-else class="text-muted">無描述</span>
                  </template>
                </el-table-column>
                <el-table-column label="公開" width="80">
                  <template #default="{ row }">
                    <el-tag :type="row.isPublic ? 'success' : 'warning'" size="small">
                      {{ row.isPublic ? '是' : '否' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150" fixed="right">
                  <template #default="{ row }">
                    <el-button
                      type="warning"
                      size="small"
                      @click="handleEdit(row)"
                    >
                      編輯
                    </el-button>
                    <el-popconfirm
                      title="確定要刪除這個設置嗎？"
                      @confirm="handleDelete(row.id)"
                    >
                      <template #reference>
                        <el-button
                          type="danger"
                          size="small"
                        >
                          刪除
                        </el-button>
                      </template>
                    </el-popconfirm>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 創建/編輯設置對話框 -->
      <el-dialog
        v-model="dialogVisible"
        :title="isEdit ? '編輯設置' : '創建設置'"
        width="600px"
      >
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="100px"
        >
          <el-form-item label="設置鍵" prop="key">
            <el-input
              v-model="form.key"
              placeholder="請輸入設置鍵"
              :disabled="isEdit"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="設置值" prop="value">
            <el-input
              v-if="form.dataType !== 'BOOLEAN'"
              v-model="form.value"
              :type="form.dataType === 'JSON' ? 'textarea' : 'text'"
              :rows="form.dataType === 'JSON' ? 4 : 1"
              placeholder="請輸入設置值"
              maxlength="1000"
              show-word-limit
            />
            <el-switch
              v-else
              v-model="booleanValue"
              active-text="是"
              inactive-text="否"
            />
          </el-form-item>

          <el-form-item label="數據類型" prop="dataType">
            <el-select
              v-model="form.dataType"
              placeholder="請選擇數據類型"
              style="width: 100%"
              :disabled="isEdit"
            >
              <el-option label="字符串" value="STRING" />
              <el-option label="數字" value="NUMBER" />
              <el-option label="布爾值" value="BOOLEAN" />
              <el-option label="JSON" value="JSON" />
            </el-select>
          </el-form-item>

          <el-form-item label="分類" prop="category">
            <el-select
              v-model="form.category"
              placeholder="請選擇分類"
              style="width: 100%"
              :disabled="isEdit"
            >
              <el-option label="系統設置" value="SYSTEM" />
              <el-option label="自定義設置" value="CUSTOM" />
            </el-select>
          </el-form-item>

          <el-form-item label="是否公開" prop="isPublic">
            <el-switch
              v-model="form.isPublic"
              active-text="是"
              inactive-text="否"
            />
          </el-form-item>

          <el-form-item label="描述" prop="description">
            <el-input
              v-model="form.description"
              type="textarea"
              :rows="3"
              placeholder="請輸入設置描述（可選）"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-form>

        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleSubmit" :loading="submitting">
              {{ isEdit ? '更新' : '創建' }}
            </el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import AppLayout from '../components/layout/AppLayout.vue'
import { useSettingsStore, type Setting } from '../stores/settings'

const settingsStore = useSettingsStore()
const formRef = ref<FormInstance>()
const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const currentSettingId = ref<number | null>(null)
const activeTab = ref('system')
const booleanValue = ref(false)

const form = reactive({
  key: '',
  value: '',
  dataType: 'STRING' as 'STRING' | 'NUMBER' | 'BOOLEAN' | 'JSON',
  category: 'SYSTEM' as 'SYSTEM' | 'CUSTOM',
  isPublic: false,
  description: ''
})

const rules: FormRules = {
  key: [
    { required: true, message: '請輸入設置鍵', trigger: 'blur' },
    { min: 1, max: 100, message: '設置鍵長度在 1 到 100 個字符', trigger: 'blur' }
  ],
  value: [
    { required: true, message: '請輸入設置值', trigger: 'blur' }
  ],
  dataType: [
    { required: true, message: '請選擇數據類型', trigger: 'change' }
  ],
  category: [
    { required: true, message: '請選擇分類', trigger: 'change' }
  ]
}

const systemSettings = computed(() => settingsStore.systemSettings)
const customSettings = computed(() => settingsStore.customSettings)

// Watch boolean value changes
watch(booleanValue, (newValue) => {
  if (form.dataType === 'BOOLEAN') {
    form.value = newValue.toString()
  }
})

// Watch form dataType changes
watch(() => form.dataType, (newType) => {
  if (newType === 'BOOLEAN') {
    booleanValue.value = form.value === 'true'
  }
})

const fetchSettings = async () => {
  loading.value = true
  try {
    await settingsStore.fetchSettings()
  } catch (error) {
    console.error('獲取設置失敗:', error)
  } finally {
    loading.value = false
  }
}

const handleCreate = (category: 'SYSTEM' | 'CUSTOM') => {
  resetForm()
  form.category = category
  isEdit.value = false
  currentSettingId.value = null
  dialogVisible.value = true
}

const handleEdit = (setting: Setting) => {
  form.key = setting.key
  form.value = setting.value
  form.dataType = setting.dataType
  form.category = setting.category
  form.isPublic = setting.isPublic
  form.description = setting.description || ''
  
  if (setting.dataType === 'BOOLEAN') {
    booleanValue.value = setting.value === 'true'
  }
  
  isEdit.value = true
  currentSettingId.value = setting.id!
  dialogVisible.value = true
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    const settingData = {
      key: form.key,
      value: form.value,
      dataType: form.dataType,
      category: form.category,
      isPublic: form.isPublic,
      description: form.description || undefined
    }

    if (isEdit.value && currentSettingId.value) {
      await settingsStore.updateSetting(currentSettingId.value, settingData)
    } else {
      await settingsStore.createSetting(settingData)
    }

    dialogVisible.value = false
    fetchSettings()
  } catch (error) {
    console.error('提交設置失敗:', error)
  } finally {
    submitting.value = false
  }
}

const handleDelete = async (id: number) => {
  try {
    await settingsStore.deleteSetting(id)
    fetchSettings()
  } catch (error) {
    console.error('刪除設置失敗:', error)
  }
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.key = ''
  form.value = ''
  form.dataType = 'STRING'
  form.category = 'SYSTEM'
  form.isPublic = false
  form.description = ''
  booleanValue.value = false
}

onMounted(() => {
  fetchSettings()
})
</script>

<style scoped>
.settings-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.content-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.settings-section {
  padding: 20px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.setting-value {
  max-width: 200px;
  word-break: break-all;
}

.text-muted {
  color: #999;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
