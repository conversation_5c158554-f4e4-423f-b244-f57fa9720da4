<template>
  <div class="task-view">
    <h1>任務管理</h1>
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>任務列表</span>
          <el-button type="primary" @click="openAddTaskDialog">新增任務</el-button>
        </div>
      </template>
      <el-table :data="tasks" style="width: 100%">
        <el-table-column prop="name" label="任務名稱" />
        <el-table-column prop="project" label="所屬專案" />
        <el-table-column prop="assignee" label="負責人" />
        <el-table-column prop="dueDate" label="截止日期" />
        <el-table-column prop="priority" label="優先度">
          <template #default="scope">
            <el-tag :type="getPriorityType(scope.row.priority)">
              {{ scope.row.priority }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="狀態">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="progress" label="進度">
          <template #default="scope">
            <el-progress :percentage="scope.row.progress" />
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button size="small" @click="viewTaskDetail(scope.row)">查看</el-button>
            <el-button size="small" type="primary" @click="editTask(scope.row)">編輯</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// Mock data for demonstration
const tasks = ref([
  {
    id: 1,
    name: '需求分析',
    project: '專案管理系統開發',
    assignee: '王大明',
    dueDate: '2025-06-01',
    priority: '高',
    status: '進行中',
    progress: 75
  },
  {
    id: 2,
    name: '資料庫設計',
    project: '專案管理系統開發',
    assignee: '李小華',
    dueDate: '2025-05-25',
    priority: '中',
    status: '已完成',
    progress: 100
  },
  {
    id: 3,
    name: '前端開發',
    project: '專案管理系統開發',
    assignee: '張小明',
    dueDate: '2025-06-15',
    priority: '高',
    status: '進行中',
    progress: 50
  },
  {
    id: 4,
    name: '後端開發',
    project: '專案管理系統開發',
    assignee: '陳小美',
    dueDate: '2025-06-15',
    priority: '高',
    status: '進行中',
    progress: 40
  }
])

const getPriorityType = (priority: string) => {
  switch (priority) {
    case '高':
      return 'danger'
    case '中':
      return 'warning'
    case '低':
      return 'info'
    default:
      return ''
  }
}

const getStatusType = (status: string) => {
  switch (status) {
    case '待處理':
      return 'info'
    case '進行中':
      return 'warning'
    case '已完成':
      return 'success'
    case '已延期':
      return 'danger'
    default:
      return ''
  }
}

const openAddTaskDialog = () => {
  // Implement add task functionality
  console.log('Open add task dialog')
}

const viewTaskDetail = (task: any) => {
  router.push({ name: 'task-detail', params: { id: task.id } })
}

const editTask = (task: any) => {
  // Implement edit task functionality
  console.log('Edit task', task)
}

onMounted(() => {
  // In a real application, you would fetch tasks from the API
  console.log('TaskView component mounted')
})
</script>

<style scoped>
.task-view {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
