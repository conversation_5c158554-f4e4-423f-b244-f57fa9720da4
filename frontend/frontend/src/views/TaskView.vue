<template>
  <AppLayout>
    <div class="task-view">
      <div class="page-header">
        <div class="page-title">
          <h1>任務管理</h1>
          <p>管理和追蹤所有任務的進度</p>
        </div>

        <div class="page-actions">
          <el-button type="primary" @click="goToCreateTask">
            <el-icon><Plus /></el-icon> 新增任務
          </el-button>
        </div>
      </div>

      <!-- 篩選器 -->
      <el-card class="filter-card">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="filters.search"
              placeholder="搜尋任務名稱"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="filters.status" placeholder="狀態" clearable @change="handleSearch">
              <el-option label="未開始" value="NOT_STARTED" />
              <el-option label="進行中" value="IN_PROGRESS" />
              <el-option label="已完成" value="COMPLETED" />
              <el-option label="延期" value="DELAYED" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="filters.priority" placeholder="優先度" clearable @change="handleSearch">
              <el-option label="低" value="LOW" />
              <el-option label="中" value="MEDIUM" />
              <el-option label="高" value="HIGH" />
              <el-option label="緊急" value="URGENT" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select
              v-model="filters.assigneeId"
              placeholder="負責人"
              clearable
              filterable
              @change="handleSearch"
            >
              <el-option
                v-for="user in users"
                :key="user.id"
                :label="`${user.firstName} ${user.lastName}`"
                :value="user.id"
              />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-button @click="resetFilters">重置</el-button>
          </el-col>
        </el-row>
      </el-card>

      <!-- 任務列表 -->
      <el-card class="table-card">
        <el-table
          :data="filteredTasks"
          style="width: 100%"
          v-loading="loading"
          @row-click="viewTaskDetail"
        >
          <el-table-column prop="name" label="任務名稱" min-width="200" />
          <el-table-column label="所屬專案" min-width="150">
            <template #default="scope">
              {{ scope.row.project?.name || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="負責人" width="120">
            <template #default="scope">
              {{ scope.row.assignee ? `${scope.row.assignee.firstName} ${scope.row.assignee.lastName}` : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="截止日期" width="120">
            <template #default="scope">
              {{ formatDate(scope.row.dueDate) }}
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="優先度" width="100">
            <template #default="scope">
              <el-tag :type="getPriorityType(scope.row.priority)">
                {{ getPriorityLabel(scope.row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="狀態" width="100">
            <template #default="scope">
              <el-tag :type="getStatusType(scope.row.status)">
                {{ getStatusLabel(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="progress" label="進度" width="150">
            <template #default="scope">
              <el-progress :percentage="scope.row.progress || 0" :stroke-width="8" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <el-button size="small" @click.stop="viewTaskDetail(scope.row)">查看</el-button>
              <el-button size="small" type="primary" @click.stop="editTask(scope.row)">編輯</el-button>
              <el-button size="small" type="danger" @click.stop="deleteTask(scope.row)">刪除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import AppLayout from '../components/layout/AppLayout.vue'
import api from '../services/api'

const router = useRouter()

// Data
const tasks = ref([])
const users = ref([])
const loading = ref(false)

// Filters
const filters = ref({
  search: '',
  status: '',
  priority: '',
  assigneeId: ''
})

// Computed
const filteredTasks = computed(() => {
  let result = tasks.value

  if (filters.value.search) {
    result = result.filter(task =>
      task.name.toLowerCase().includes(filters.value.search.toLowerCase())
    )
  }

  if (filters.value.status) {
    result = result.filter(task => task.status === filters.value.status)
  }

  if (filters.value.priority) {
    result = result.filter(task => task.priority === filters.value.priority)
  }

  if (filters.value.assigneeId) {
    result = result.filter(task => task.assignee?.id === filters.value.assigneeId)
  }

  return result
})

// Methods
const fetchTasks = async () => {
  loading.value = true
  try {
    const response = await api.getTasks()
    tasks.value = response.data
  } catch (error) {
    console.error('Failed to fetch tasks:', error)
    ElMessage.error('獲取任務列表失敗')
  } finally {
    loading.value = false
  }
}

const fetchUsers = async () => {
  try {
    const response = await api.getUsers()
    users.value = response.data
  } catch (error) {
    console.error('Failed to fetch users:', error)
  }
}

const getPriorityType = (priority: string) => {
  switch (priority) {
    case 'HIGH':
    case 'URGENT':
      return 'danger'
    case 'MEDIUM':
      return 'warning'
    case 'LOW':
      return 'info'
    default:
      return ''
  }
}

const getPriorityLabel = (priority: string) => {
  const labels = {
    'LOW': '低',
    'MEDIUM': '中',
    'HIGH': '高',
    'URGENT': '緊急'
  }
  return labels[priority] || priority
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'NOT_STARTED':
      return 'info'
    case 'IN_PROGRESS':
      return 'warning'
    case 'COMPLETED':
      return 'success'
    case 'DELAYED':
      return 'danger'
    default:
      return ''
  }
}

const getStatusLabel = (status: string) => {
  const labels = {
    'NOT_STARTED': '未開始',
    'IN_PROGRESS': '進行中',
    'COMPLETED': '已完成',
    'DELAYED': '延期'
  }
  return labels[status] || status
}

const formatDate = (date: string) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-TW')
}

const goToCreateTask = () => {
  router.push('/tasks/create')
}

const viewTaskDetail = (task: any) => {
  router.push({ name: 'task-detail', params: { id: task.id } })
}

const editTask = (task: any) => {
  router.push(`/tasks/${task.id}/edit`)
}

const deleteTask = (task: any) => {
  ElMessageBox.confirm(
    '確定要刪除此任務嗎？',
    '刪除任務',
    {
      confirmButtonText: '確定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(async () => {
    try {
      await api.deleteTask(task.id)
      ElMessage.success('任務已刪除')
      fetchTasks()
    } catch (error) {
      console.error('Failed to delete task:', error)
      ElMessage.error('刪除任務失敗')
    }
  }).catch(() => {
    // User cancelled
  })
}

const handleSearch = () => {
  // Filtering is handled by computed property
}

const resetFilters = () => {
  filters.value = {
    search: '',
    status: '',
    priority: '',
    assigneeId: ''
  }
}

onMounted(() => {
  fetchTasks()
  fetchUsers()
})
</script>

<style scoped>
.task-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.page-title h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.page-title p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.filter-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.table-card :deep(.el-table__row) {
  cursor: pointer;
}

.table-card :deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}
</style>
