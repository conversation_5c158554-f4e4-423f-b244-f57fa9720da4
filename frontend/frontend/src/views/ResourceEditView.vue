<template>
  <AppLayout>
    <div class="resource-edit-view">
      <div class="page-header">
        <div class="header-left">
          <el-button @click="goBack" type="text">
            <el-icon><ArrowLeft /></el-icon>
            返回資源詳情
          </el-button>
          <h1>編輯資源</h1>
        </div>
      </div>

      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <el-card v-else class="form-card">
        <el-form
          ref="formRef"
          :model="form"
          :rules="rules"
          label-width="120px"
          @submit.prevent="handleSubmit"
        >
          <el-form-item label="資源名稱" prop="name">
            <el-input
              v-model="form.name"
              placeholder="請輸入資源名稱"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="資源類型" prop="type">
            <el-select
              v-model="form.type"
              placeholder="請選擇資源類型"
              style="width: 100%"
            >
              <el-option label="人力" value="HUMAN" />
              <el-option label="材料" value="MATERIAL" />
              <el-option label="設備" value="EQUIPMENT" />
            </el-select>
          </el-form-item>

          <el-form-item label="可用狀態" prop="available">
            <el-switch
              v-model="form.available"
              active-text="可用"
              inactive-text="不可用"
            />
          </el-form-item>

          <el-form-item label="描述" prop="description">
            <el-input
              v-model="form.description"
              type="textarea"
              :rows="4"
              placeholder="請輸入資源描述"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSubmit" :loading="submitting">
              更新資源
            </el-button>
            <el-button @click="resetForm">重置</el-button>
            <el-button @click="goBack">取消</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import AppLayout from '../components/layout/AppLayout.vue'
import api from '../services/api'

const router = useRouter()
const route = useRoute()

// Form
const formRef = ref<FormInstance>()
const loading = ref(false)
const submitting = ref(false)
const originalData = ref<any>(null)

const form = reactive({
  name: '',
  type: '',
  available: true,
  description: ''
})

const rules: FormRules = {
  name: [
    { required: true, message: '請輸入資源名稱', trigger: 'blur' },
    { min: 2, max: 100, message: '資源名稱長度應在 2 到 100 個字符之間', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '請選擇資源類型', trigger: 'change' }
  ],
  available: [
    { required: true, message: '請設置可用狀態', trigger: 'change' }
  ]
}

// Methods
const fetchResource = async () => {
  const id = Number(route.params.id)
  if (!id) return

  loading.value = true
  try {
    const response = await api.getResource(id)
    const resource = response.data
    
    originalData.value = resource
    
    // 填充表單
    form.name = resource.name
    form.type = resource.type
    form.available = resource.available
    form.description = resource.description || ''
    
  } catch (error) {
    console.error('獲取資源詳情失敗:', error)
    ElMessage.error('獲取資源詳情失敗')
    goBack()
  } finally {
    loading.value = false
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    submitting.value = true
    
    const id = Number(route.params.id)
    await api.updateResource(id, form)
    
    ElMessage.success('資源更新成功')
    
    // 導航到資源詳情頁面
    router.push({ name: 'resource-detail', params: { id } })
    
  } catch (error: any) {
    if (error.errors) {
      // 表單驗證錯誤
      return
    }
    
    console.error('更新資源失敗:', error)
    ElMessage.error('更新資源失敗')
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  if (!originalData.value) return
  
  form.name = originalData.value.name
  form.type = originalData.value.type
  form.available = originalData.value.available
  form.description = originalData.value.description || ''
}

const goBack = () => {
  const id = route.params.id
  router.push({ name: 'resource-detail', params: { id } })
}

onMounted(() => {
  fetchResource()
})
</script>

<style scoped>
.resource-edit-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.header-left h1 {
  margin: 10px 0 0 0;
  color: #303133;
}

.loading-container {
  padding: 40px;
  text-align: center;
}

.form-card {
  max-width: 600px;
}

@media (max-width: 768px) {
  .form-card {
    max-width: 100%;
  }
}
</style>
