<template>
  <AppLayout>
    <div class="project-detail-container" v-loading="loading">
      <div class="project-header">
        <div class="project-title">
          <h1>{{ project.name }}</h1>
          <el-tag :type="getStatusType(project.status)" size="large">
            {{ getStatusLabel(project.status) }}
          </el-tag>
        </div>

        <div class="project-actions">
          <el-button-group>
            <el-button type="primary" @click="editMode = !editMode">
              {{ editMode ? '取消編輯' : '編輯專案' }}
            </el-button>
            <el-button @click="goBack">返回列表</el-button>
          </el-button-group>
        </div>
      </div>

      <el-tabs v-model="activeTab">
        <el-tab-pane label="專案概覽" name="overview">
          <el-row :gutter="20" v-if="!editMode">
            <el-col :xs="24" :md="16">
              <el-card class="project-info-card">
                <template #header>
                  <div class="card-header">
                    <span>專案資訊</span>
                  </div>
                </template>

                <el-descriptions :column="2" border>
                  <el-descriptions-item label="專案名稱">{{ project.name }}</el-descriptions-item>
                  <el-descriptions-item label="客戶">{{ project.customer?.name || '-' }}</el-descriptions-item>
                  <el-descriptions-item label="開始日期">{{ formatDate(project.startDate) }}</el-descriptions-item>
                  <el-descriptions-item label="結束日期">{{ formatDate(project.endDate) }}</el-descriptions-item>
                  <el-descriptions-item label="專案負責人">
                    {{ project.manager ? `${project.manager.firstName} ${project.manager.lastName}` : '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="預算">{{ formatCurrency(project.budget) }}</el-descriptions-item>
                  <el-descriptions-item label="專案描述" :span="2">{{ project.description || '-' }}</el-descriptions-item>
                </el-descriptions>

                <div class="project-progress">
                  <h3>專案進度</h3>
                  <el-progress :percentage="project.progress" :format="percentageFormat" />
                </div>

                <div class="project-members">
                  <h3>專案成員</h3>
                  <el-tag
                    v-for="member in project.members"
                    :key="member.id"
                    class="member-tag"
                  >
                    {{ member.firstName }} {{ member.lastName }}
                  </el-tag>
                  <span v-if="!project.members || project.members.length === 0">暫無專案成員</span>
                </div>
              </el-card>

              <el-card class="project-tasks-card">
                <template #header>
                  <div class="card-header">
                    <span>任務列表</span>
                    <el-button type="primary" size="small" @click="showAddTaskDialog = true">
                      新增任務
                    </el-button>
                  </div>
                </template>

                <el-table :data="tasks" style="width: 100%">
                  <el-table-column prop="name" label="任務名稱" min-width="150" />
                  <el-table-column label="負責人" width="120">
                    <template #default="scope">
                      {{ scope.row.assignee ? `${scope.row.assignee.firstName} ${scope.row.assignee.lastName}` : '-' }}
                    </template>
                  </el-table-column>
                  <el-table-column label="截止日期" width="120">
                    <template #default="scope">
                      {{ formatDate(scope.row.dueDate) }}
                    </template>
                  </el-table-column>
                  <el-table-column label="狀態" width="100">
                    <template #default="scope">
                      <el-tag :type="getTaskStatusType(scope.row.status)">
                        {{ getTaskStatusLabel(scope.row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="進度" width="180">
                    <template #default="scope">
                      <el-progress :percentage="scope.row.progress" />
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="120" fixed="right">
                    <template #default="scope">
                      <el-button type="primary" size="small" @click="viewTask(scope.row.id)">
                        查看
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-card>
            </el-col>

            <el-col :xs="24" :md="8">
              <el-card class="project-stats-card">
                <template #header>
                  <div class="card-header">
                    <span>專案統計</span>
                  </div>
                </template>

                <div class="stat-item">
                  <div class="stat-label">總任務數</div>
                  <div class="stat-value">{{ tasks.length }}</div>
                </div>

                <div class="stat-item">
                  <div class="stat-label">已完成任務</div>
                  <div class="stat-value">{{ completedTasks.length }}</div>
                </div>

                <div class="stat-item">
                  <div class="stat-label">進行中任務</div>
                  <div class="stat-value">{{ inProgressTasks.length }}</div>
                </div>

                <div class="stat-item">
                  <div class="stat-label">延期任務</div>
                  <div class="stat-value">{{ delayedTasks.length }}</div>
                </div>

                <div class="stat-item">
                  <div class="stat-label">任務完成率</div>
                  <div class="stat-value">{{ taskCompletionRate }}%</div>
                </div>
              </el-card>

              <el-card class="project-documents-card">
                <template #header>
                  <div class="card-header">
                    <span>專案文件</span>
                    <el-button type="primary" size="small" @click="showUploadDialog = true">
                      上傳文件
                    </el-button>
                  </div>
                </template>

                <el-table :data="documents" style="width: 100%">
                  <el-table-column prop="name" label="文件名稱" min-width="150" />
                  <el-table-column prop="fileType" label="類型" width="80" />
                  <el-table-column prop="version" label="版本" width="80" />
                  <el-table-column label="上傳者" width="120">
                    <template #default="scope">
                      {{ scope.row.uploadedBy ? `${scope.row.uploadedBy.firstName} ${scope.row.uploadedBy.lastName}` : '-' }}
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="120">
                    <template #default="scope">
                      <el-button type="primary" size="small" @click="downloadDocument(scope.row)">
                        下載
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-card>
            </el-col>
          </el-row>

          <el-card v-else>
            <ProjectForm
              :project-id="projectId"
              @submit-success="handleUpdateSuccess"
              submit-button-text="更新專案"
            />
          </el-card>
        </el-tab-pane>

        <el-tab-pane label="甘特圖" name="gantt">
          <el-card>
            <GanttChart
              :tasks="tasks"
              :start-date="project.startDate"
              :end-date="project.endDate || getDefaultEndDate()"
              @task-click="viewTask"
            />
          </el-card>
        </el-tab-pane>


      </el-tabs>

      <!-- Add Task Dialog -->
      <el-dialog
        v-model="showAddTaskDialog"
        title="新增任務"
        width="50%"
      >
        <!-- Task form will be implemented separately -->
        <div>任務表單將在任務管理模組中實現</div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showAddTaskDialog = false">取消</el-button>
            <el-button type="primary" @click="showAddTaskDialog = false">確認</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- Upload Document Dialog -->
      <el-dialog
        v-model="showUploadDialog"
        title="上傳文件"
        width="50%"
      >
        <!-- Document upload form will be implemented separately -->
        <div>文件上傳表單將在文件管理模組中實現</div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showUploadDialog = false">取消</el-button>
            <el-button type="primary" @click="showUploadDialog = false">確認</el-button>
          </span>
        </template>
      </el-dialog>


    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import AppLayout from '../components/layout/AppLayout.vue'
import ProjectForm from '../components/project/ProjectForm.vue'
import GanttChart from '../components/project/GanttChart.vue'
import api from '../services/api'

const route = useRoute()
const router = useRouter()
const projectId = computed(() => Number(route.params.id))
const loading = ref(false)
const project = ref<any>({})
const tasks = ref([])
const documents = ref([])
const activeTab = ref('overview')
const editMode = ref(false)
const showAddTaskDialog = ref(false)
const showUploadDialog = ref(false)

// Computed properties
const completedTasks = computed(() => {
  return tasks.value.filter((task: any) => task.status === 'COMPLETED')
})

const inProgressTasks = computed(() => {
  return tasks.value.filter((task: any) => task.status === 'IN_PROGRESS')
})

const delayedTasks = computed(() => {
  return tasks.value.filter((task: any) => task.status === 'DELAYED')
})

const taskCompletionRate = computed(() => {
  if (tasks.value.length === 0) return 0
  return Math.round((completedTasks.value.length / tasks.value.length) * 100)
})

// Fetch project data
const fetchProjectData = async () => {
  loading.value = true
  try {
    const response = await api.getProject(projectId.value)
    project.value = response.data

    // Fetch related data
    await Promise.all([
      fetchTasks(),
      fetchDocuments()
    ])
  } catch (error) {
    console.error('Failed to fetch project data:', error)
    ElMessage.error('獲取專案資料失敗')
  } finally {
    loading.value = false
  }
}

// Fetch tasks
const fetchTasks = async () => {
  try {
    const response = await api.getProjectTasks(projectId.value)
    tasks.value = response.data
  } catch (error) {
    console.error('Failed to fetch tasks:', error)
    ElMessage.error('獲取任務資料失敗')
  }
}

// Fetch documents
const fetchDocuments = async () => {
  try {
    const response = await api.getProjectDocuments(projectId.value)
    documents.value = response.data
  } catch (error) {
    console.error('Failed to fetch documents:', error)
    ElMessage.error('獲取文件資料失敗')
  }
}



// Handle update success
const handleUpdateSuccess = (updatedProject: any) => {
  project.value = updatedProject
  editMode.value = false
  ElMessage.success('專案已更新')
}

// View task
const viewTask = (id: number) => {
  router.push(`/tasks/${id}`)
}

// Download document
const downloadDocument = (document: any) => {
  // This will be implemented in the document management module
  ElMessage.info('文件下載功能將在文件管理模組中實現')
}



// Go back to project list
const goBack = () => {
  router.push('/projects')
}

// Helper functions
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-TW')
}

const formatCurrency = (value: number) => {
  if (value === undefined || value === null) return '-'
  return new Intl.NumberFormat('zh-TW', {
    style: 'currency',
    currency: 'TWD',
    minimumFractionDigits: 0
  }).format(value)
}

const percentageFormat = (percentage: number) => {
  return `${percentage}%`
}

const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    'PLANNING': '規劃中',
    'IN_PROGRESS': '執行中',
    'COMPLETED': '已完成',
    'DELAYED': '延期',
    'CLOSED': '結案'
  }
  return statusMap[status] || status
}

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'PLANNING': 'info',
    'IN_PROGRESS': 'primary',
    'COMPLETED': 'success',
    'DELAYED': 'danger',
    'CLOSED': 'info'
  }
  return typeMap[status] || 'info'
}

const getTaskStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    'NOT_STARTED': '未開始',
    'IN_PROGRESS': '進行中',
    'COMPLETED': '已完成',
    'DELAYED': '延期'
  }
  return statusMap[status] || status
}

const getTaskStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'NOT_STARTED': 'info',
    'IN_PROGRESS': 'primary',
    'COMPLETED': 'success',
    'DELAYED': 'danger'
  }
  return typeMap[status] || 'info'
}



const getDefaultEndDate = () => {
  const date = new Date()
  date.setMonth(date.getMonth() + 1)
  return date.toISOString().split('T')[0]
}

onMounted(() => {
  fetchProjectData()
})
</script>

<style scoped>
.project-detail-container {
  padding: 20px;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.project-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.project-title h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.project-info-card,
.project-tasks-card,
.project-stats-card,
.project-documents-card {
  margin-bottom: 20px;
}

.project-progress,
.project-members {
  margin-top: 20px;
}

.project-progress h3,
.project-members h3 {
  margin-bottom: 10px;
  font-size: 16px;
  color: #303133;
}

.member-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  color: #606266;
}

.stat-value {
  font-weight: bold;
  color: #303133;
}
</style>
