<template>
  <AppLayout>
    <div class="project-detail-container" v-loading="loading">
      <div class="project-header">
        <div class="project-title">
          <h1>{{ project.name }}</h1>
          <el-tag :type="getStatusType(project.status)" size="large">
            {{ getStatusLabel(project.status) }}
          </el-tag>
        </div>

        <div class="project-actions">
          <el-button-group>
            <el-button type="primary" @click="editMode = !editMode">
              {{ editMode ? '取消編輯' : '編輯專案' }}
            </el-button>
            <el-button @click="goBack">返回列表</el-button>
          </el-button-group>
        </div>
      </div>

      <el-tabs v-model="activeTab">
        <el-tab-pane label="專案概覽" name="overview">
          <el-row :gutter="20" v-if="!editMode">
            <el-col :xs="24" :md="16">
              <el-card class="project-info-card">
                <template #header>
                  <div class="card-header">
                    <span>專案資訊</span>
                  </div>
                </template>

                <el-descriptions :column="2" border>
                  <el-descriptions-item label="專案名稱">{{ project.name }}</el-descriptions-item>
                  <el-descriptions-item label="客戶">{{ project.customer?.name || '-' }}</el-descriptions-item>
                  <el-descriptions-item label="開始日期">{{ formatDate(project.startDate) }}</el-descriptions-item>
                  <el-descriptions-item label="結束日期">{{ formatDate(project.endDate) }}</el-descriptions-item>
                  <el-descriptions-item label="專案負責人">
                    {{ project.manager ? `${project.manager.firstName} ${project.manager.lastName}` : '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="預算">{{ formatCurrency(project.budget) }}</el-descriptions-item>
                  <el-descriptions-item label="專案描述" :span="2">{{ project.description || '-' }}</el-descriptions-item>
                </el-descriptions>

                <div class="project-progress">
                  <h3>專案進度</h3>
                  <el-progress :percentage="project.progress" :format="percentageFormat" />
                </div>

                <div class="project-members">
                  <div class="members-header">
                    <h3>專案成員</h3>
                    <el-button type="primary" size="small" @click="showAddMemberDialog = true">
                      新增成員
                    </el-button>
                  </div>
                  <div class="members-list">
                    <el-tag
                      v-for="member in project.members"
                      :key="member.id"
                      class="member-tag"
                      closable
                      @close="removeMember(member.id)"
                    >
                      {{ member.firstName }} {{ member.lastName }}
                    </el-tag>
                    <span v-if="!project.members || project.members.length === 0">暫無專案成員</span>
                  </div>
                </div>
              </el-card>

              <el-card class="project-tasks-card">
                <template #header>
                  <div class="card-header">
                    <span>任務列表</span>
                    <el-button type="primary" size="small" @click="showAddTaskDialog = true">
                      新增任務
                    </el-button>
                  </div>
                </template>

                <el-table :data="tasks" style="width: 100%">
                  <el-table-column prop="name" label="任務名稱" min-width="150" />
                  <el-table-column label="負責人" width="120">
                    <template #default="scope">
                      {{ scope.row.assignee ? `${scope.row.assignee.firstName} ${scope.row.assignee.lastName}` : '-' }}
                    </template>
                  </el-table-column>
                  <el-table-column label="截止日期" width="120">
                    <template #default="scope">
                      {{ formatDate(scope.row.dueDate) }}
                    </template>
                  </el-table-column>
                  <el-table-column label="狀態" width="100">
                    <template #default="scope">
                      <el-tag :type="getTaskStatusType(scope.row.status)">
                        {{ getTaskStatusLabel(scope.row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="進度" width="180">
                    <template #default="scope">
                      <el-progress :percentage="scope.row.progress" />
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="120" fixed="right">
                    <template #default="scope">
                      <el-button type="primary" size="small" @click="viewTask(scope.row.id)">
                        查看
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-card>
            </el-col>

            <el-col :xs="24" :md="8">
              <el-card class="project-stats-card">
                <template #header>
                  <div class="card-header">
                    <span>專案統計</span>
                  </div>
                </template>

                <div class="stat-item">
                  <div class="stat-label">總任務數</div>
                  <div class="stat-value">{{ tasks.length }}</div>
                </div>

                <div class="stat-item">
                  <div class="stat-label">已完成任務</div>
                  <div class="stat-value">{{ completedTasks.length }}</div>
                </div>

                <div class="stat-item">
                  <div class="stat-label">進行中任務</div>
                  <div class="stat-value">{{ inProgressTasks.length }}</div>
                </div>

                <div class="stat-item">
                  <div class="stat-label">延期任務</div>
                  <div class="stat-value">{{ delayedTasks.length }}</div>
                </div>

                <div class="stat-item">
                  <div class="stat-label">任務完成率</div>
                  <div class="stat-value">{{ taskCompletionRate }}%</div>
                </div>
              </el-card>

              <el-card class="project-documents-card">
                <template #header>
                  <div class="card-header">
                    <span>專案文件</span>
                    <el-button type="primary" size="small" @click="showUploadDialog = true">
                      上傳文件
                    </el-button>
                  </div>
                </template>

                <el-table :data="documents" style="width: 100%">
                  <el-table-column prop="name" label="文件名稱" min-width="150" />
                  <el-table-column prop="fileType" label="類型" width="80" />
                  <el-table-column prop="version" label="版本" width="80" />
                  <el-table-column label="上傳者" width="120">
                    <template #default="scope">
                      {{ scope.row.uploadedBy ? `${scope.row.uploadedBy.firstName} ${scope.row.uploadedBy.lastName}` : '-' }}
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="120">
                    <template #default="scope">
                      <el-button type="primary" size="small" @click="downloadDocument(scope.row)">
                        下載
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-card>
            </el-col>
          </el-row>

          <el-card v-else>
            <ProjectForm
              :project-id="projectId"
              @submit-success="handleUpdateSuccess"
              submit-button-text="更新專案"
            />
          </el-card>
        </el-tab-pane>

        <el-tab-pane label="甘特圖" name="gantt">
          <el-card>
            <GanttChart
              :tasks="tasks"
              :start-date="project.startDate"
              :end-date="project.endDate || getDefaultEndDate()"
              @task-click="viewTask"
            />
          </el-card>
        </el-tab-pane>

        <el-tab-pane label="進度自動化" name="progress">
          <ProjectProgressAutomation :project-id="projectId" />
        </el-tab-pane>

        <el-tab-pane label="資源分配" name="resources">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>專案資源分配</span>
                <el-button type="primary" @click="showResourceAllocationDialog = true">
                  分配資源
                </el-button>
              </div>
            </template>

            <el-table :data="resourceAllocations" v-loading="resourceLoading" style="width: 100%">
              <el-table-column prop="resource.name" label="資源名稱" min-width="150" />

              <el-table-column label="資源類型" width="120">
                <template #default="{ row }">
                  <el-tag :type="getResourceTypeTag(row.resource.type)">
                    {{ getResourceTypeLabel(row.resource.type) }}
                  </el-tag>
                </template>
              </el-table-column>

              <el-table-column prop="startDate" label="開始日期" width="120">
                <template #default="{ row }">
                  {{ formatDate(row.startDate) }}
                </template>
              </el-table-column>

              <el-table-column prop="endDate" label="結束日期" width="120">
                <template #default="{ row }">
                  {{ formatDate(row.endDate) }}
                </template>
              </el-table-column>

              <el-table-column prop="quantity" label="數量" width="100" />

              <el-table-column prop="cost" label="成本" width="120">
                <template #default="{ row }">
                  {{ formatCurrency(row.cost) }}
                </template>
              </el-table-column>

              <el-table-column label="關聯任務" min-width="150">
                <template #default="{ row }">
                  {{ row.task?.name || '整個專案' }}
                </template>
              </el-table-column>

              <el-table-column label="操作" width="150" fixed="right">
                <template #default="{ row }">
                  <el-button type="warning" size="small" @click="editResourceAllocation(row)">
                    編輯
                  </el-button>
                  <el-button type="danger" size="small" @click="deleteResourceAllocation(row.id)">
                    刪除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <div v-if="!resourceAllocations.length && !resourceLoading" class="empty-state">
              <el-empty description="尚未分配任何資源" />
            </div>
          </el-card>
        </el-tab-pane>


      </el-tabs>

      <!-- Add Task Dialog -->
      <el-dialog
        v-model="showAddTaskDialog"
        title="新增任務"
        width="50%"
      >
        <!-- Task form will be implemented separately -->
        <div>任務表單將在任務管理模組中實現</div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showAddTaskDialog = false">取消</el-button>
            <el-button type="primary" @click="showAddTaskDialog = false">確認</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- Upload Document Dialog -->
      <el-dialog
        v-model="showUploadDialog"
        title="上傳文件"
        width="50%"
      >
        <!-- Document upload form will be implemented separately -->
        <div>文件上傳表單將在文件管理模組中實現</div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="showUploadDialog = false">取消</el-button>
            <el-button type="primary" @click="showUploadDialog = false">確認</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- Add Member Dialog -->
      <el-dialog
        v-model="showAddMemberDialog"
        title="新增專案成員"
        width="40%"
      >
        <el-form
          ref="memberFormRef"
          :model="memberForm"
          :rules="memberRules"
          label-width="100px"
        >
          <el-form-item label="選擇成員" prop="userId">
            <el-select
              v-model="memberForm.userId"
              placeholder="請選擇要添加的成員"
              style="width: 100%"
              filterable
              remote
              :remote-method="searchUsers"
              :loading="usersLoading"
            >
              <el-option
                v-for="user in availableUsers"
                :key="user.id"
                :label="`${user.firstName} ${user.lastName} (${user.username})`"
                :value="user.id"
              />
            </el-select>
          </el-form-item>
        </el-form>

        <template #footer>
          <span class="dialog-footer">
            <el-button @click="closeAddMemberDialog">取消</el-button>
            <el-button type="primary" @click="addMember" :loading="addingMember">
              新增
            </el-button>
          </span>
        </template>
      </el-dialog>

      <!-- Resource Allocation Dialog -->
      <el-dialog
        v-model="showResourceAllocationDialog"
        :title="editingAllocation ? '編輯資源分配' : '分配資源'"
        width="60%"
      >
        <el-form
          ref="allocationFormRef"
          :model="allocationForm"
          :rules="allocationRules"
          label-width="120px"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="選擇資源" prop="resourceId">
                <el-select
                  v-model="allocationForm.resourceId"
                  placeholder="請選擇資源"
                  style="width: 100%"
                  filterable
                >
                  <el-option
                    v-for="resource in availableResources"
                    :key="resource.id"
                    :label="`${resource.name} (${getResourceTypeLabel(resource.type)})`"
                    :value="resource.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="關聯任務" prop="taskId">
                <el-select
                  v-model="allocationForm.taskId"
                  placeholder="選擇任務（可選）"
                  style="width: 100%"
                  clearable
                >
                  <el-option label="整個專案" :value="null" />
                  <el-option
                    v-for="task in tasks"
                    :key="task.id"
                    :label="task.name"
                    :value="task.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="開始日期" prop="startDate">
                <el-date-picker
                  v-model="allocationForm.startDate"
                  type="date"
                  placeholder="選擇開始日期"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="結束日期" prop="endDate">
                <el-date-picker
                  v-model="allocationForm.endDate"
                  type="date"
                  placeholder="選擇結束日期"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="數量" prop="quantity">
                <el-input-number
                  v-model="allocationForm.quantity"
                  :min="0"
                  :precision="2"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="成本" prop="cost">
                <el-input-number
                  v-model="allocationForm.cost"
                  :min="0"
                  :precision="2"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <template #footer>
          <span class="dialog-footer">
            <el-button @click="closeResourceAllocationDialog">取消</el-button>
            <el-button type="primary" @click="submitResourceAllocation" :loading="submittingAllocation">
              {{ editingAllocation ? '更新' : '分配' }}
            </el-button>
          </span>
        </template>
      </el-dialog>


    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import AppLayout from '../components/layout/AppLayout.vue'
import ProjectForm from '../components/project/ProjectForm.vue'
import ProjectProgressAutomation from '../components/project/ProjectProgressAutomation.vue'
import GanttChart from '../components/project/GanttChart.vue'
import api from '../services/api'

const route = useRoute()
const router = useRouter()
const projectId = computed(() => Number(route.params.id))
const loading = ref(false)
const project = ref<any>({})
const tasks = ref([])
const documents = ref([])
const resourceAllocations = ref([])
const availableResources = ref([])
const activeTab = ref('overview')
const editMode = ref(false)
const showAddTaskDialog = ref(false)
const showUploadDialog = ref(false)
const showResourceAllocationDialog = ref(false)
const showAddMemberDialog = ref(false)
const resourceLoading = ref(false)
const submittingAllocation = ref(false)
const editingAllocation = ref<any>(null)
const allocationFormRef = ref()
const memberFormRef = ref()
const availableUsers = ref([])
const usersLoading = ref(false)
const addingMember = ref(false)

// Member form
const memberForm = ref({
  userId: null
})

// Member form validation rules
const memberRules = {
  userId: [
    { required: true, message: '請選擇成員', trigger: 'change' }
  ]
}

// Resource allocation form
const allocationForm = ref({
  resourceId: null,
  taskId: null,
  startDate: '',
  endDate: '',
  quantity: 1,
  cost: 0
})

// Form validation rules
const allocationRules = {
  resourceId: [
    { required: true, message: '請選擇資源', trigger: 'change' }
  ],
  startDate: [
    { required: true, message: '請選擇開始日期', trigger: 'change' }
  ],
  endDate: [
    { required: true, message: '請選擇結束日期', trigger: 'change' }
  ],
  quantity: [
    { required: true, message: '請輸入數量', trigger: 'blur' }
  ],
  cost: [
    { required: true, message: '請輸入成本', trigger: 'blur' }
  ]
}

// Computed properties
const completedTasks = computed(() => {
  return tasks.value.filter((task: any) => task.status === 'COMPLETED')
})

const inProgressTasks = computed(() => {
  return tasks.value.filter((task: any) => task.status === 'IN_PROGRESS')
})

const delayedTasks = computed(() => {
  return tasks.value.filter((task: any) => task.status === 'DELAYED')
})

const taskCompletionRate = computed(() => {
  if (tasks.value.length === 0) return 0
  return Math.round((completedTasks.value.length / tasks.value.length) * 100)
})

// Fetch project data
const fetchProjectData = async () => {
  loading.value = true
  try {
    const response = await api.getProject(projectId.value)
    project.value = response.data

    // Fetch related data
    await Promise.all([
      fetchTasks(),
      fetchDocuments(),
      fetchResourceAllocations(),
      fetchAvailableResources()
    ])
  } catch (error) {
    console.error('Failed to fetch project data:', error)
    ElMessage.error('獲取專案資料失敗')
  } finally {
    loading.value = false
  }
}

// Fetch tasks
const fetchTasks = async () => {
  try {
    const response = await api.getProjectTasks(projectId.value)
    tasks.value = response.data
  } catch (error) {
    console.error('Failed to fetch tasks:', error)
    ElMessage.error('獲取任務資料失敗')
  }
}

// Fetch documents
const fetchDocuments = async () => {
  try {
    const response = await api.getProjectDocuments(projectId.value)
    documents.value = response.data
  } catch (error) {
    console.error('Failed to fetch documents:', error)
    ElMessage.error('獲取文件資料失敗')
  }
}

// Fetch resource allocations
const fetchResourceAllocations = async () => {
  resourceLoading.value = true
  try {
    const response = await api.getProjectResourceAllocations(projectId.value)
    resourceAllocations.value = response.data
  } catch (error) {
    console.error('Failed to fetch resource allocations:', error)
    ElMessage.error('獲取資源分配資料失敗')
  } finally {
    resourceLoading.value = false
  }
}

// Fetch available resources
const fetchAvailableResources = async () => {
  try {
    const response = await api.getResources()
    availableResources.value = response.data.filter((resource: any) => resource.available)
  } catch (error) {
    console.error('Failed to fetch available resources:', error)
    ElMessage.error('獲取可用資源失敗')
  }
}



// Handle update success
const handleUpdateSuccess = (updatedProject: any) => {
  project.value = updatedProject
  editMode.value = false
  ElMessage.success('專案已更新')
}

// View task
const viewTask = (id: number) => {
  router.push(`/tasks/${id}`)
}

// Download document
const downloadDocument = (document: any) => {
  // This will be implemented in the document management module
  ElMessage.info('文件下載功能將在文件管理模組中實現')
}

// Resource allocation methods
const resetAllocationForm = () => {
  allocationForm.value = {
    resourceId: null,
    taskId: null,
    startDate: '',
    endDate: '',
    quantity: 1,
    cost: 0
  }
  editingAllocation.value = null
}

const closeResourceAllocationDialog = () => {
  showResourceAllocationDialog.value = false
  resetAllocationForm()
  if (allocationFormRef.value) {
    allocationFormRef.value.resetFields()
  }
}

const editResourceAllocation = (allocation: any) => {
  editingAllocation.value = allocation
  allocationForm.value = {
    resourceId: allocation.resource.id,
    taskId: allocation.task?.id || null,
    startDate: allocation.startDate,
    endDate: allocation.endDate,
    quantity: allocation.quantity,
    cost: allocation.cost
  }
  showResourceAllocationDialog.value = true
}

const submitResourceAllocation = async () => {
  if (!allocationFormRef.value) return

  await allocationFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      submittingAllocation.value = true
      try {
        const allocationData = {
          resource: { id: allocationForm.value.resourceId },
          project: { id: projectId.value },
          task: allocationForm.value.taskId ? { id: allocationForm.value.taskId } : null,
          startDate: allocationForm.value.startDate,
          endDate: allocationForm.value.endDate,
          quantity: allocationForm.value.quantity,
          cost: allocationForm.value.cost
        }

        if (editingAllocation.value) {
          await api.updateResourceAllocation(editingAllocation.value.id, allocationData)
          ElMessage.success('資源分配已更新')
        } else {
          await api.createResourceAllocation(allocationData)
          ElMessage.success('資源分配成功')
        }

        closeResourceAllocationDialog()
        fetchResourceAllocations()
      } catch (error: any) {
        console.error('Resource allocation failed:', error)
        if (error.response?.data?.message) {
          ElMessage.error(error.response.data.message)
        } else {
          ElMessage.error('資源分配失敗')
        }
      } finally {
        submittingAllocation.value = false
      }
    }
  })
}

const deleteResourceAllocation = async (id: number) => {
  try {
    await ElMessageBox.confirm(
      '確定要刪除這個資源分配嗎？',
      '刪除確認',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await api.deleteResourceAllocation(id)
    ElMessage.success('資源分配已刪除')
    fetchResourceAllocations()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Delete resource allocation failed:', error)
      ElMessage.error('刪除資源分配失敗')
    }
  }
}



// Go back to project list
const goBack = () => {
  router.push('/projects')
}

// Helper functions
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-TW')
}

const formatCurrency = (value: number) => {
  if (value === undefined || value === null) return '-'
  return new Intl.NumberFormat('zh-TW', {
    style: 'currency',
    currency: 'TWD',
    minimumFractionDigits: 0
  }).format(value)
}

const percentageFormat = (percentage: number) => {
  return `${percentage}%`
}

const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    'PLANNING': '規劃中',
    'IN_PROGRESS': '執行中',
    'COMPLETED': '已完成',
    'DELAYED': '延期',
    'CLOSED': '結案'
  }
  return statusMap[status] || status
}

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'PLANNING': 'info',
    'IN_PROGRESS': 'primary',
    'COMPLETED': 'success',
    'DELAYED': 'danger',
    'CLOSED': 'info'
  }
  return typeMap[status] || 'info'
}

const getTaskStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    'NOT_STARTED': '未開始',
    'IN_PROGRESS': '進行中',
    'COMPLETED': '已完成',
    'DELAYED': '延期'
  }
  return statusMap[status] || status
}

const getTaskStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'NOT_STARTED': 'info',
    'IN_PROGRESS': 'primary',
    'COMPLETED': 'success',
    'DELAYED': 'danger'
  }
  return typeMap[status] || 'info'
}



const getDefaultEndDate = () => {
  const date = new Date()
  date.setMonth(date.getMonth() + 1)
  return date.toISOString().split('T')[0]
}

const getResourceTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    'HUMAN': '人力',
    'MATERIAL': '材料',
    'EQUIPMENT': '設備'
  }
  return typeMap[type] || type
}

const getResourceTypeTag = (type: string) => {
  const tagMap: Record<string, string> = {
    'HUMAN': 'success',
    'MATERIAL': 'warning',
    'EQUIPMENT': 'primary'
  }
  return tagMap[type] || 'info'
}

// Member management methods
const searchUsers = async (query: string) => {
  if (!query) {
    availableUsers.value = []
    return
  }

  usersLoading.value = true
  try {
    const response = await api.searchUsers(query)
    // Filter out users who are already members
    const currentMemberIds = project.value.members?.map((m: any) => m.id) || []
    availableUsers.value = response.data.filter((user: any) => !currentMemberIds.includes(user.id))
  } catch (error) {
    console.error('Failed to search users:', error)
    ElMessage.error('搜尋用戶失敗')
  } finally {
    usersLoading.value = false
  }
}

const closeAddMemberDialog = () => {
  showAddMemberDialog.value = false
  memberForm.value.userId = null
  availableUsers.value = []
  if (memberFormRef.value) {
    memberFormRef.value.resetFields()
  }
}

const addMember = async () => {
  if (!memberFormRef.value) return

  await memberFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      addingMember.value = true
      try {
        await api.addProjectMember(projectId.value, memberForm.value.userId)
        ElMessage.success('成員已添加')
        closeAddMemberDialog()
        fetchProjectData() // Refresh project data to get updated members
      } catch (error: any) {
        console.error('Add member failed:', error)
        if (error.response?.data?.message) {
          ElMessage.error(error.response.data.message)
        } else {
          ElMessage.error('添加成員失敗')
        }
      } finally {
        addingMember.value = false
      }
    }
  })
}

const removeMember = async (userId: number) => {
  try {
    await ElMessageBox.confirm(
      '確定要移除這個成員嗎？',
      '移除確認',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await api.removeProjectMember(projectId.value, userId)
    ElMessage.success('成員已移除')
    fetchProjectData() // Refresh project data to get updated members
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('Remove member failed:', error)
      ElMessage.error('移除成員失敗')
    }
  }
}

onMounted(() => {
  fetchProjectData()
})
</script>

<style scoped>
.project-detail-container {
  padding: 20px;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.project-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.project-title h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.project-info-card,
.project-tasks-card,
.project-stats-card,
.project-documents-card {
  margin-bottom: 20px;
}

.project-progress,
.project-members {
  margin-top: 20px;
}

.project-progress h3,
.project-members h3 {
  margin-bottom: 10px;
  font-size: 16px;
  color: #303133;
}

.members-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.members-header h3 {
  margin: 0;
}

.members-list {
  min-height: 40px;
}

.member-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  color: #606266;
}

.stat-value {
  font-weight: bold;
  color: #303133;
}

.empty-state {
  padding: 40px;
  text-align: center;
}
</style>
