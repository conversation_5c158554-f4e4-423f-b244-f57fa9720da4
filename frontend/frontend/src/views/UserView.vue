<template>
  <div class="user-view">
    <h1>使用者管理</h1>
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>使用者列表</span>
          <el-button type="primary" @click="openAddUserDialog">新增使用者</el-button>
        </div>
      </template>
      <el-table :data="users" style="width: 100%">
        <el-table-column prop="username" label="使用者名稱" />
        <el-table-column prop="email" label="電子郵件" />
        <el-table-column prop="department" label="部門" />
        <el-table-column prop="role" label="角色" />
        <el-table-column prop="status" label="狀態">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'ACTIVE' ? 'success' : 'danger'">
              {{ scope.row.status === 'ACTIVE' ? '啟用' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button size="small" @click="editUser(scope.row)">編輯</el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="toggleUserStatus(scope.row)"
            >
              {{ scope.row.status === 'ACTIVE' ? '停用' : '啟用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// Mock data for demonstration
const users = ref([
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    department: '管理部',
    role: '最高管理員',
    status: 'ACTIVE'
  },
  {
    id: 2,
    username: 'manager',
    email: '<EMAIL>',
    department: '業務部',
    role: '部門主管',
    status: 'ACTIVE'
  },
  {
    id: 3,
    username: 'employee',
    email: '<EMAIL>',
    department: '技術部',
    role: '員工',
    status: 'ACTIVE'
  }
])

const openAddUserDialog = () => {
  // Implement add user functionality
  console.log('Open add user dialog')
}

const editUser = (user: any) => {
  // Implement edit user functionality
  console.log('Edit user', user)
}

const toggleUserStatus = (user: any) => {
  // Implement toggle user status functionality
  user.status = user.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE'
  console.log('Toggle user status', user)
}

onMounted(() => {
  // In a real application, you would fetch users from the API
  console.log('UserView component mounted')
})
</script>

<style scoped>
.user-view {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
