<template>
  <AppLayout>
    <div class="user-management-view">
      <div class="page-header">
        <h1>用戶管理</h1>
        <p>管理系統用戶、權限和活動記錄</p>
      </div>

      <!-- 統計卡片 -->
      <div class="stats-grid">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.totalUsers }}</div>
              <div class="stat-label">總用戶數</div>
            </div>
          </div>
        </el-card>

        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon active">
              <el-icon><User /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.enabledUsers }}</div>
              <div class="stat-label">啟用用戶</div>
            </div>
          </div>
        </el-card>

        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon inactive">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.disabledUsers }}</div>
              <div class="stat-label">停用用戶</div>
            </div>
          </div>
        </el-card>

        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon online">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ onlineUsers }}</div>
              <div class="stat-label">在線用戶</div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 搜索和過濾 -->
      <el-card class="filter-card">
        <div class="filter-row">
          <div class="filter-left">
            <el-input
              v-model="searchQuery"
              placeholder="搜索用戶名、姓名或郵箱"
              style="width: 300px"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>

            <el-select
              v-model="selectedStatus"
              placeholder="狀態"
              style="width: 120px"
              clearable
            >
              <el-option label="啟用" :value="true" />
              <el-option label="停用" :value="false" />
            </el-select>

            <el-select
              v-model="selectedDepartment"
              placeholder="部門"
              style="width: 150px"
              clearable
            >
              <el-option
                v-for="dept in departments"
                :key="dept.id"
                :label="dept.name"
                :value="dept.id"
              />
            </el-select>

            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>

            <el-button @click="resetFilters">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </div>

          <div class="filter-right">
            <el-button
              type="success"
              :disabled="selectedUsers.length === 0"
              @click="batchEnableUsers"
            >
              批量啟用
            </el-button>
            <el-button
              type="danger"
              :disabled="selectedUsers.length === 0"
              @click="batchDisableUsers"
            >
              批量停用
            </el-button>
            <el-button type="primary" @click="goToCreateUser">
              <el-icon><Plus /></el-icon>
              新增用戶
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 用戶表格 -->
      <el-card class="table-card">
        <el-table
          v-loading="loading"
          :data="paginatedUsers"
          style="width: 100%"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
          row-class-name="clickable-row"
        >
          <el-table-column type="selection" width="55" />

          <el-table-column label="頭像" width="80">
            <template #default="scope">
              <el-avatar
                :size="40"
                :src="scope.row.avatar"
                :alt="scope.row.fullName"
              >
                {{ scope.row.firstName?.charAt(0) }}{{ scope.row.lastName?.charAt(0) }}
              </el-avatar>
            </template>
          </el-table-column>

          <el-table-column prop="username" label="用戶名" min-width="120" />

          <el-table-column label="姓名" min-width="120">
            <template #default="scope">
              {{ scope.row.firstName }} {{ scope.row.lastName }}
            </template>
          </el-table-column>

          <el-table-column prop="email" label="郵箱" min-width="200" />

          <el-table-column label="部門" min-width="120">
            <template #default="scope">
              {{ scope.row.department?.name || '-' }}
            </template>
          </el-table-column>

          <el-table-column label="角色" min-width="150">
            <template #default="scope">
              <el-tag
                v-for="role in scope.row.roles"
                :key="role.id"
                size="small"
                style="margin-right: 4px"
              >
                {{ getRoleLabel(role.name) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="狀態" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.enabled ? 'success' : 'danger'">
                {{ scope.row.enabled ? '啟用' : '停用' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="最後登入" min-width="150">
            <template #default="scope">
              {{ formatDate(scope.row.lastLoginAt) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
            <template #default="scope">
              <el-button
                size="small"
                @click.stop="viewUser(scope.row)"
              >
                查看
              </el-button>
              <el-button
                size="small"
                type="primary"
                @click.stop="editUser(scope.row)"
              >
                編輯
              </el-button>
              <el-button
                size="small"
                :type="scope.row.enabled ? 'danger' : 'success'"
                @click.stop="toggleUserStatus(scope.row)"
              >
                {{ scope.row.enabled ? '停用' : '啟用' }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分頁 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="filteredUsers.length"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  UserFilled,
  User,
  Connection
} from '@element-plus/icons-vue'
import AppLayout from '../components/layout/AppLayout.vue'
import api from '../services/api'

const router = useRouter()

// 響應式數據
const loading = ref(false)
const users = ref<any[]>([])
const departments = ref<any[]>([])
const selectedUsers = ref<any[]>([])
const searchQuery = ref('')
const selectedStatus = ref<boolean | null>(null)
const selectedDepartment = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const onlineUsers = ref(0)

// 統計數據
const statistics = reactive({
  totalUsers: 0,
  enabledUsers: 0,
  disabledUsers: 0
})

// 計算屬性
const filteredUsers = computed(() => {
  let filtered = users.value

  // 搜索過濾
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(user =>
      user.username.toLowerCase().includes(query) ||
      user.firstName.toLowerCase().includes(query) ||
      user.lastName.toLowerCase().includes(query) ||
      user.email.toLowerCase().includes(query)
    )
  }

  // 狀態過濾
  if (selectedStatus.value !== null) {
    filtered = filtered.filter(user => user.enabled === selectedStatus.value)
  }

  // 部門過濾
  if (selectedDepartment.value) {
    filtered = filtered.filter(user => user.department?.id === selectedDepartment.value)
  }

  return filtered
})

const paginatedUsers = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredUsers.value.slice(start, end)
})

// 方法
const fetchUsers = async () => {
  loading.value = true
  try {
    const response = await api.getUsers()
    users.value = response.data
    updateStatistics()
  } catch (error) {
    console.error('獲取用戶列表失敗:', error)
    ElMessage.error('獲取用戶列表失敗')
  } finally {
    loading.value = false
  }
}

const fetchDepartments = async () => {
  try {
    const response = await api.getDepartments()
    departments.value = response.data
  } catch (error) {
    console.error('獲取部門列表失敗:', error)
  }
}

const fetchStatistics = async () => {
  try {
    const response = await api.getUserStatistics()
    Object.assign(statistics, response.data)
  } catch (error) {
    console.error('獲取用戶統計失敗:', error)
  }
}

const updateStatistics = () => {
  statistics.totalUsers = users.value.length
  statistics.enabledUsers = users.value.filter(user => user.enabled).length
  statistics.disabledUsers = users.value.filter(user => !user.enabled).length
}

const handleSearch = () => {
  currentPage.value = 1
}

const resetFilters = () => {
  searchQuery.value = ''
  selectedStatus.value = null
  selectedDepartment.value = ''
  currentPage.value = 1
}

const handleSelectionChange = (selection: any[]) => {
  selectedUsers.value = selection
}

const handleRowClick = (row: any) => {
  viewUser(row)
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

const viewUser = (user: any) => {
  router.push({ name: 'user-detail', params: { id: user.id } })
}

const editUser = (user: any) => {
  router.push({ name: 'user-edit', params: { id: user.id } })
}

const goToCreateUser = () => {
  router.push({ name: 'user-create' })
}

const toggleUserStatus = async (user: any) => {
  const action = user.enabled ? '停用' : '啟用'

  try {
    await ElMessageBox.confirm(
      `確定要${action}用戶 "${user.firstName} ${user.lastName}" 嗎？`,
      `${action}用戶`,
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    if (user.enabled) {
      await api.disableUser(user.id)
    } else {
      await api.enableUser(user.id)
    }

    ElMessage.success(`用戶已${action}`)
    fetchUsers()
    fetchStatistics()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error(`${action}用戶失敗:`, error)
      ElMessage.error(`${action}用戶失敗`)
    }
  }
}

const batchEnableUsers = async () => {
  if (selectedUsers.value.length === 0) return

  try {
    await ElMessageBox.confirm(
      `確定要啟用選中的 ${selectedUsers.value.length} 個用戶嗎？`,
      '批量啟用用戶',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const userIds = selectedUsers.value.map(user => user.id)
    await api.batchEnableUsers(userIds)
    ElMessage.success('用戶已批量啟用')
    fetchUsers()
    fetchStatistics()
    selectedUsers.value = []
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('批量啟用用戶失敗:', error)
      ElMessage.error('批量啟用用戶失敗')
    }
  }
}

const batchDisableUsers = async () => {
  if (selectedUsers.value.length === 0) return

  try {
    await ElMessageBox.confirm(
      `確定要停用選中的 ${selectedUsers.value.length} 個用戶嗎？`,
      '批量停用用戶',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const userIds = selectedUsers.value.map(user => user.id)
    await api.batchDisableUsers(userIds)
    ElMessage.success('用戶已批量停用')
    fetchUsers()
    fetchStatistics()
    selectedUsers.value = []
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('批量停用用戶失敗:', error)
      ElMessage.error('批量停用用戶失敗')
    }
  }
}

const getRoleLabel = (roleName: string) => {
  const roleMap: Record<string, string> = {
    'SUPER_ADMIN': '超級管理員',
    'ADMIN': '管理員',
    'DEPARTMENT_HEAD': '部門主管',
    'PROJECT_MANAGER': '專案經理',
    'EMPLOYEE': '員工'
  }
  return roleMap[roleName] || roleName
}

const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 模擬在線用戶數（實際應用中應該從後端獲取）
const updateOnlineUsers = () => {
  onlineUsers.value = Math.floor(Math.random() * 20) + 5
}

onMounted(() => {
  fetchUsers()
  fetchDepartments()
  fetchStatistics()
  updateOnlineUsers()

  // 每30秒更新一次在線用戶數
  setInterval(updateOnlineUsers, 30000)
})
</script>

<style scoped>
.user-management-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

/* 統計卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 8px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.active {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.inactive {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.online {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

/* 過濾卡片 */
.filter-card {
  margin-bottom: 20px;
}

.filter-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.filter-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.filter-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 表格卡片 */
.table-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.clickable-row {
  cursor: pointer;
}

.clickable-row:hover {
  background-color: #f5f7fa;
}

/* 分頁 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .user-management-view {
    padding: 12px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-left,
  .filter-right {
    justify-content: center;
  }

  .stat-content {
    padding: 12px;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .stat-number {
    font-size: 24px;
  }
}

/* 表格樣式優化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f8f9fa;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #303133;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table__row:hover > td) {
  background-color: #f5f7fa;
}

/* 頭像樣式 */
:deep(.el-avatar) {
  background-color: #409eff;
  color: white;
  font-weight: 600;
}

/* 標籤樣式 */
:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 500;
}

/* 按鈕樣式 */
:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--small) {
  padding: 5px 11px;
  font-size: 12px;
}
</style>
