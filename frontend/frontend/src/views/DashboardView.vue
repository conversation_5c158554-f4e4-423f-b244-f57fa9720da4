<template>
  <AppLayout>
    <div class="dashboard-container">
      <h1 class="dashboard-title">儀表板</h1>
      
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="6" v-for="(card, index) in summaryCards" :key="index">
          <el-card class="summary-card" :body-style="{ padding: '20px' }">
            <div class="summary-card-content">
              <el-icon :size="32" :color="card.color"><component :is="card.icon" /></el-icon>
              <div class="summary-card-info">
                <div class="summary-card-value">{{ card.value }}</div>
                <div class="summary-card-label">{{ card.label }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" class="dashboard-charts">
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>專案狀態分佈</span>
              </div>
            </template>
            <div class="chart-container">
              <ProjectStatusChart :chart-data="projectStatusData" />
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :lg="12">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>任務狀態分佈</span>
              </div>
            </template>
            <div class="chart-container">
              <TaskStatusChart :chart-data="taskStatusData" />
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" class="dashboard-tables">
        <el-col :xs="24" :lg="12">
          <el-card class="table-card">
            <template #header>
              <div class="table-header">
                <span>即將到期的任務</span>
                <el-button type="primary" size="small" @click="goToTasks">查看全部</el-button>
              </div>
            </template>
            <el-table :data="dueSoonTasks" style="width: 100%" v-loading="loading">
              <el-table-column prop="name" label="任務名稱" min-width="120" />
              <el-table-column prop="dueDate" label="截止日期" width="100" />
              <el-table-column prop="priority" label="優先度" width="80">
                <template #default="scope">
                  <el-tag :type="getPriorityType(scope.row.priority)">
                    {{ getPriorityLabel(scope.row.priority) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80">
                <template #default="scope">
                  <el-button type="primary" size="small" @click="viewTask(scope.row.id)">查看</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :lg="12">
          <el-card class="table-card">
            <template #header>
              <div class="table-header">
                <span>最近的專案</span>
                <el-button type="primary" size="small" @click="goToProjects">查看全部</el-button>
              </div>
            </template>
            <el-table :data="recentProjects" style="width: 100%" v-loading="loading">
              <el-table-column prop="name" label="專案名稱" min-width="120" />
              <el-table-column prop="status" label="狀態" width="100">
                <template #default="scope">
                  <el-tag :type="getStatusType(scope.row.status)">
                    {{ getStatusLabel(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="progress" label="進度" width="100">
                <template #default="scope">
                  <el-progress :percentage="scope.row.progress" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80">
                <template #default="scope">
                  <el-button type="primary" size="small" @click="viewProject(scope.row.id)">查看</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Folder, Document, User, Collection } from '@element-plus/icons-vue'
import AppLayout from '../components/layout/AppLayout.vue'
import ProjectStatusChart from '../components/charts/ProjectStatusChart.vue'
import TaskStatusChart from '../components/charts/TaskStatusChart.vue'
import api from '../services/api'

const router = useRouter()
const loading = ref(true)

// Dashboard summary data
const summaryData = reactive({
  totalProjects: 0,
  userProjects: 0,
  totalTasks: 0,
  userTasks: 0,
  overdueTasks: 0,
  userOverdueTasks: 0,
  projectStatusCounts: {},
  taskStatusCounts: {},
  userTaskStatusCounts: {}
})

// Tasks and projects data
const dueSoonTasks = ref([])
const recentProjects = ref([])

// Summary cards
const summaryCards = computed(() => [
  {
    icon: Folder,
    label: '我的專案',
    value: summaryData.userProjects,
    color: '#409EFF'
  },
  {
    icon: Document,
    label: '我的任務',
    value: summaryData.userTasks,
    color: '#67C23A'
  },
  {
    icon: Collection,
    label: '逾期任務',
    value: summaryData.userOverdueTasks,
    color: '#F56C6C'
  },
  {
    icon: User,
    label: '總專案數',
    value: summaryData.totalProjects,
    color: '#E6A23C'
  }
])

// Chart data
const projectStatusData = computed(() => {
  const statusMap = {
    PLANNING: '規劃中',
    IN_PROGRESS: '執行中',
    COMPLETED: '已完成',
    DELAYED: '延期',
    CLOSED: '結案'
  }
  
  const labels = Object.keys(summaryData.projectStatusCounts).map(key => statusMap[key as keyof typeof statusMap] || key)
  const data = Object.values(summaryData.projectStatusCounts)
  
  return {
    labels,
    datasets: [
      {
        data,
        backgroundColor: ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399']
      }
    ]
  }
})

const taskStatusData = computed(() => {
  const statusMap = {
    NOT_STARTED: '未開始',
    IN_PROGRESS: '進行中',
    COMPLETED: '已完成',
    DELAYED: '延期'
  }
  
  const labels = Object.keys(summaryData.userTaskStatusCounts).map(key => statusMap[key as keyof typeof statusMap] || key)
  const data = Object.values(summaryData.userTaskStatusCounts)
  
  return {
    labels,
    datasets: [
      {
        data,
        backgroundColor: ['#909399', '#409EFF', '#67C23A', '#F56C6C']
      }
    ]
  }
})

// Fetch dashboard data
onMounted(async () => {
  try {
    // Fetch summary data
    const summaryResponse = await api.getDashboardSummary()
    Object.assign(summaryData, summaryResponse.data)
    
    // Fetch tasks dashboard
    const tasksResponse = await api.getTasksDashboard()
    dueSoonTasks.value = tasksResponse.data.dueSoonTasks
    
    // Fetch projects dashboard
    const projectsResponse = await api.getProjectsDashboard()
    recentProjects.value = projectsResponse.data.recentProjects
  } catch (error) {
    console.error('Failed to fetch dashboard data:', error)
  } finally {
    loading.value = false
  }
})

// Helper functions
const getPriorityType = (priority: string) => {
  const types = {
    LOW: 'info',
    MEDIUM: 'warning',
    HIGH: 'danger',
    URGENT: 'danger'
  }
  return types[priority as keyof typeof types] || 'info'
}

const getPriorityLabel = (priority: string) => {
  const labels = {
    LOW: '低',
    MEDIUM: '中',
    HIGH: '高',
    URGENT: '緊急'
  }
  return labels[priority as keyof typeof labels] || priority
}

const getStatusType = (status: string) => {
  const types = {
    PLANNING: 'info',
    IN_PROGRESS: 'primary',
    COMPLETED: 'success',
    DELAYED: 'danger',
    CLOSED: 'info'
  }
  return types[status as keyof typeof types] || 'info'
}

const getStatusLabel = (status: string) => {
  const labels = {
    PLANNING: '規劃中',
    IN_PROGRESS: '執行中',
    COMPLETED: '已完成',
    DELAYED: '延期',
    CLOSED: '結案'
  }
  return labels[status as keyof typeof labels] || status
}

// Navigation functions
const viewTask = (id: number) => {
  router.push(`/tasks/${id}`)
}

const viewProject = (id: number) => {
  router.push(`/projects/${id}`)
}

const goToTasks = () => {
  router.push('/tasks')
}

const goToProjects = () => {
  router.push('/projects')
}
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.dashboard-title {
  margin-bottom: 20px;
  font-size: 24px;
  color: #303133;
}

.summary-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.summary-card-content {
  display: flex;
  align-items: center;
}

.summary-card-info {
  margin-left: 16px;
}

.summary-card-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.summary-card-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.dashboard-charts {
  margin-top: 20px;
}

.chart-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
}

.dashboard-tables {
  margin-top: 20px;
}

.table-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
