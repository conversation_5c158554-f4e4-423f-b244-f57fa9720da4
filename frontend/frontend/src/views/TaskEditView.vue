<template>
  <AppLayout>
    <div class="task-edit-view">
      <div class="page-header">
        <div class="page-title">
          <h1>編輯任務</h1>
          <p>修改任務信息和設置</p>
        </div>
        
        <div class="page-actions">
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon> 返回任務詳情
          </el-button>
        </div>
      </div>

      <el-card v-loading="loading">
        <TaskForm
          :task-id="taskId"
          @submit-success="handleSubmitSuccess"
          @cancel="goBack"
          submit-button-text="更新任務"
        />
      </el-card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'
import AppLayout from '../components/layout/AppLayout.vue'
import TaskForm from '../components/task/TaskForm.vue'

const router = useRouter()
const route = useRoute()

const loading = ref(false)

const taskId = computed(() => Number(route.params.id))

const handleSubmitSuccess = (task: any) => {
  router.push({ name: 'task-detail', params: { id: task.id } })
}

const goBack = () => {
  router.push({ name: 'task-detail', params: { id: taskId.value } })
}
</script>

<style scoped>
.task-edit-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.page-title h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.page-title p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}
</style>
