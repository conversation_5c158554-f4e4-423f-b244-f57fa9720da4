<template>
  <AppLayout>
    <div class="project-create-container">
      <div class="page-header">
        <div class="page-title">
          <h1>新增專案</h1>
          <p>建立新的專案並設定基本資訊</p>
        </div>
        
        <div class="page-actions">
          <el-button @click="goBack">返回列表</el-button>
        </div>
      </div>
      
      <el-card class="form-card">
        <ProjectForm
          @submit-success="handleCreateSuccess"
          submit-button-text="建立專案"
        />
      </el-card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import AppLayout from '../components/layout/AppLayout.vue'
import ProjectForm from '../components/project/ProjectForm.vue'

const router = useRouter()

// Handle create success
const handleCreateSuccess = (project: any) => {
  ElMessage.success('專案建立成功！')
  // Navigate to the newly created project detail page
  router.push(`/projects/${project.id}`)
}

// Go back to project list
const goBack = () => {
  router.push('/projects')
}
</script>

<style scoped>
.project-create-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.page-title h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.page-title p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.form-card {
  max-width: 800px;
  margin: 0 auto;
}

.form-card :deep(.el-card__body) {
  padding: 30px;
}
</style>
