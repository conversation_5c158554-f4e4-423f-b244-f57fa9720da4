<template>
  <AppLayout>
    <div class="contact-record-view">
      <div class="page-header">
        <h1>聯繫記錄管理</h1>
        <p>管理客戶聯繫記錄和跟進情況</p>
      </div>

      <!-- 聯繫記錄列表 -->
      <ContactRecordList
        v-if="!showForm"
        @create="handleCreate"
        @edit="handleEdit"
      />

      <!-- 聯繫記錄表單 -->
      <div v-else class="form-container">
        <div class="form-header">
          <h2>{{ isEdit ? '編輯聯繫記錄' : '創建聯繫記錄' }}</h2>
        </div>
        
        <ContactRecordForm
          :contact-record="selectedRecord"
          :is-edit="isEdit"
          @success="handleFormSuccess"
          @cancel="handleFormCancel"
        />
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import AppLayout from '../components/layout/AppLayout.vue'
import ContactRecordList from '../components/customer/ContactRecordList.vue'
import ContactRecordForm from '../components/customer/ContactRecordForm.vue'
import type { ContactRecord } from '../stores/contactRecord'

const showForm = ref(false)
const isEdit = ref(false)
const selectedRecord = ref<ContactRecord | null>(null)

const handleCreate = () => {
  selectedRecord.value = null
  isEdit.value = false
  showForm.value = true
}

const handleEdit = (record: ContactRecord) => {
  selectedRecord.value = record
  isEdit.value = true
  showForm.value = true
}

const handleFormSuccess = () => {
  showForm.value = false
  selectedRecord.value = null
  isEdit.value = false
}

const handleFormCancel = () => {
  showForm.value = false
  selectedRecord.value = null
  isEdit.value = false
}
</script>

<style scoped>
.contact-record-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.form-container {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.form-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.form-header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}
</style>
