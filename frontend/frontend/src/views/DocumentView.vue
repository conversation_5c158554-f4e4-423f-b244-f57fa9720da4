<template>
  <div class="document-view">
    <h1>文件管理</h1>
    
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <div class="search-box">
            <el-input
              v-model="searchQuery"
              placeholder="搜尋文件..."
              prefix-icon="Search"
              clearable
              @clear="searchDocuments"
              @input="searchDocuments"
            />
            <el-select v-model="documentType" placeholder="文件類型" @change="searchDocuments">
              <el-option label="全部" value="" />
              <el-option label="合約" value="CONTRACT" />
              <el-option label="報告" value="REPORT" />
              <el-option label="提案" value="PROPOSAL" />
              <el-option label="其他" value="OTHER" />
            </el-select>
          </div>
          <el-button type="primary" @click="openUploadDialog">上傳文件</el-button>
        </div>
      </template>
      
      <el-table :data="documents" style="width: 100%">
        <el-table-column prop="name" label="文件名稱" />
        <el-table-column prop="type" label="類型" />
        <el-table-column prop="project" label="相關專案" />
        <el-table-column prop="uploadedBy" label="上傳者" />
        <el-table-column prop="uploadDate" label="上傳日期" />
        <el-table-column prop="version" label="版本" />
        <el-table-column label="操作">
          <template #default="scope">
            <el-button size="small" type="primary" @click="downloadDocument(scope.row)">下載</el-button>
            <el-button size="small" @click="viewDocumentHistory(scope.row)">歷史版本</el-button>
            <el-dropdown trigger="click">
              <el-button size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="uploadNewVersion(scope.row)">上傳新版本</el-dropdown-item>
                  <el-dropdown-item @click="shareDocument(scope.row)">分享</el-dropdown-item>
                  <el-dropdown-item @click="deleteDocument(scope.row)" divided>刪除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalDocuments"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ArrowDown } from '@element-plus/icons-vue'

const searchQuery = ref('')
const documentType = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const totalDocuments = ref(100)

// Mock data for demonstration
const documents = ref([
  {
    id: 1,
    name: '專案管理系統開發合約.pdf',
    type: 'CONTRACT',
    project: '專案管理系統開發',
    uploadedBy: '王大明',
    uploadDate: '2025-04-15',
    version: '1.0'
  },
  {
    id: 2,
    name: '需求分析報告.docx',
    type: 'REPORT',
    project: '專案管理系統開發',
    uploadedBy: '李小華',
    uploadDate: '2025-04-20',
    version: '2.1'
  },
  {
    id: 3,
    name: '系統架構設計.pptx',
    type: 'PROPOSAL',
    project: '專案管理系統開發',
    uploadedBy: '張小明',
    uploadDate: '2025-04-25',
    version: '1.2'
  },
  {
    id: 4,
    name: '前端開發規範.docx',
    type: 'OTHER',
    project: '專案管理系統開發',
    uploadedBy: '陳小美',
    uploadDate: '2025-05-01',
    version: '1.0'
  },
  {
    id: 5,
    name: '後端開發規範.docx',
    type: 'OTHER',
    project: '專案管理系統開發',
    uploadedBy: '陳小美',
    uploadDate: '2025-05-01',
    version: '1.0'
  }
])

const searchDocuments = () => {
  // Implement search functionality
  console.log('Search documents', searchQuery.value, documentType.value)
}

const openUploadDialog = () => {
  // Implement upload dialog functionality
  console.log('Open upload dialog')
}

const downloadDocument = (document: any) => {
  // Implement download functionality
  console.log('Download document', document)
}

const viewDocumentHistory = (document: any) => {
  // Implement view history functionality
  console.log('View document history', document)
}

const uploadNewVersion = (document: any) => {
  // Implement upload new version functionality
  console.log('Upload new version', document)
}

const shareDocument = (document: any) => {
  // Implement share functionality
  console.log('Share document', document)
}

const deleteDocument = (document: any) => {
  // Implement delete functionality
  console.log('Delete document', document)
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  // Implement pagination functionality
  console.log('Page size changed to', val)
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  // Implement pagination functionality
  console.log('Current page changed to', val)
}

onMounted(() => {
  // In a real application, you would fetch documents from the API
  console.log('DocumentView component mounted')
})
</script>

<style scoped>
.document-view {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-box {
  display: flex;
  gap: 10px;
  width: 60%;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
