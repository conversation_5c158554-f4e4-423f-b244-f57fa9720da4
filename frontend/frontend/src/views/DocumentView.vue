<template>
  <AppLayout>
    <div class="document-management-view">
      <div class="page-header">
        <h1>文件管理</h1>
        <p>管理專案文件、文檔版本和文件權限</p>
      </div>

      <!-- 統計卡片 -->
      <div class="stats-grid">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.totalFiles }}</div>
              <div class="stat-label">總文件數</div>
            </div>
          </div>
        </el-card>

        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon storage">
              <el-icon><FolderOpened /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ formatFileSize(statistics.totalSize) }}</div>
              <div class="stat-label">總存儲空間</div>
            </div>
          </div>
        </el-card>

        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon recent">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.recentFiles }}</div>
              <div class="stat-label">本週新增</div>
            </div>
          </div>
        </el-card>

        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon shared">
              <el-icon><Share /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.sharedFiles }}</div>
              <div class="stat-label">共享文件</div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 工具欄 -->
      <el-card class="toolbar-card">
        <div class="toolbar-row">
          <div class="toolbar-left">
            <el-input
              v-model="searchQuery"
              placeholder="搜索文件名稱或內容"
              style="width: 300px"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>

            <el-select
              v-model="selectedType"
              placeholder="文件類型"
              style="width: 150px"
              clearable
            >
              <el-option label="文檔" value="DOCUMENT" />
              <el-option label="圖片" value="IMAGE" />
              <el-option label="視頻" value="VIDEO" />
              <el-option label="音頻" value="AUDIO" />
              <el-option label="壓縮包" value="ARCHIVE" />
              <el-option label="其他" value="OTHER" />
            </el-select>

            <el-select
              v-model="selectedProject"
              placeholder="相關專案"
              style="width: 150px"
              clearable
              filterable
            >
              <el-option
                v-for="project in projects"
                :key="project.id"
                :label="project.name"
                :value="project.id"
              />
            </el-select>

            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>

            <el-button @click="resetFilters">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </div>

          <div class="toolbar-right">
            <el-button-group>
              <el-button
                :type="viewMode === 'grid' ? 'primary' : 'default'"
                @click="viewMode = 'grid'"
              >
                <el-icon><Grid /></el-icon>
                網格
              </el-button>
              <el-button
                :type="viewMode === 'list' ? 'primary' : 'default'"
                @click="viewMode = 'list'"
              >
                <el-icon><List /></el-icon>
                列表
              </el-button>
            </el-button-group>

            <el-dropdown @command="handleBatchAction">
              <el-button :disabled="selectedFiles.length === 0">
                批量操作
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="download">批量下載</el-dropdown-item>
                  <el-dropdown-item command="move">移動到文件夾</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>刪除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <el-button type="primary" @click="showUploadDialog = true">
              <el-icon><Upload /></el-icon>
              上傳文件
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 文件列表 -->
      <el-card class="files-card">
        <!-- 網格視圖 -->
        <div v-if="viewMode === 'grid'" class="grid-view">
          <div class="files-grid">
            <div
              v-for="file in filteredFiles"
              :key="file.id"
              class="file-card"
              :class="{ selected: selectedFiles.includes(file.id) }"
              @click="toggleFileSelection(file.id)"
              @dblclick="previewFile(file)"
            >
              <div class="file-checkbox">
                <el-checkbox
                  :model-value="selectedFiles.includes(file.id)"
                  @change="toggleFileSelection(file.id)"
                  @click.stop
                />
              </div>

              <div class="file-icon">
                <el-icon v-if="getFileIcon(file.type)" :size="48">
                  <component :is="getFileIcon(file.type)" />
                </el-icon>
                <img
                  v-else-if="file.type === 'IMAGE' && file.thumbnail"
                  :src="file.thumbnail"
                  :alt="file.name"
                  class="file-thumbnail"
                />
              </div>

              <div class="file-info">
                <div class="file-name" :title="file.name">{{ file.name }}</div>
                <div class="file-meta">
                  <span class="file-size">{{ formatFileSize(file.size) }}</span>
                  <span class="file-date">{{ formatDate(file.updatedAt) }}</span>
                </div>
              </div>

              <div class="file-actions">
                <el-button size="small" @click.stop="previewFile(file)">
                  <el-icon><View /></el-icon>
                </el-button>
                <el-button size="small" @click.stop="downloadFile(file)">
                  <el-icon><Download /></el-icon>
                </el-button>
                <el-dropdown @command="(command) => handleFileAction(command, file)" @click.stop>
                  <el-button size="small">
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="share">分享</el-dropdown-item>
                      <el-dropdown-item command="version">版本歷史</el-dropdown-item>
                      <el-dropdown-item command="edit">編輯信息</el-dropdown-item>
                      <el-dropdown-item command="move">移動</el-dropdown-item>
                      <el-dropdown-item command="delete" divided>刪除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
          </div>
        </div>

        <!-- 列表視圖 -->
        <div v-else class="list-view">
          <el-table
            :data="filteredFiles"
            v-loading="loading"
            @selection-change="handleSelectionChange"
            @row-dblclick="previewFile"
          >
            <el-table-column type="selection" width="55" />

            <el-table-column label="文件名" min-width="200">
              <template #default="scope">
                <div class="file-name-cell">
                  <el-icon class="file-type-icon">
                    <component :is="getFileIcon(scope.row.type)" />
                  </el-icon>
                  <span class="file-name">{{ scope.row.name }}</span>
                  <el-tag v-if="scope.row.isShared" size="small" type="success">共享</el-tag>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="type" label="類型" width="100">
              <template #default="scope">
                <el-tag size="small">{{ getFileTypeLabel(scope.row.type) }}</el-tag>
              </template>
            </el-table-column>

            <el-table-column label="大小" width="100">
              <template #default="scope">
                {{ formatFileSize(scope.row.size) }}
              </template>
            </el-table-column>

            <el-table-column label="相關專案" width="150">
              <template #default="scope">
                {{ scope.row.project?.name || '-' }}
              </template>
            </el-table-column>

            <el-table-column label="上傳者" width="120">
              <template #default="scope">
                {{ scope.row.uploadedBy?.firstName }} {{ scope.row.uploadedBy?.lastName }}
              </template>
            </el-table-column>

            <el-table-column label="上傳時間" width="180">
              <template #default="scope">
                {{ formatDateTime(scope.row.createdAt) }}
              </template>
            </el-table-column>

            <el-table-column label="版本" width="80" align="center">
              <template #default="scope">
                <el-tag size="small" type="info">v{{ scope.row.version || 1 }}</el-tag>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="200" fixed="right">
              <template #default="scope">
                <el-button size="small" @click="previewFile(scope.row)">
                  預覽
                </el-button>
                <el-button size="small" type="primary" @click="downloadFile(scope.row)">
                  下載
                </el-button>
                <el-dropdown @command="(command) => handleFileAction(command, scope.row)">
                  <el-button size="small">
                    更多
                    <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="share">分享文件</el-dropdown-item>
                      <el-dropdown-item command="version">版本歷史</el-dropdown-item>
                      <el-dropdown-item command="edit">編輯信息</el-dropdown-item>
                      <el-dropdown-item command="move">移動文件</el-dropdown-item>
                      <el-dropdown-item command="delete" divided>刪除文件</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分頁 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[20, 50, 100]"
            :total="filteredFiles.length"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>

      <!-- 文件上傳對話框 -->
      <el-dialog
        v-model="showUploadDialog"
        title="上傳文件"
        width="600px"
        @close="resetUploadForm"
      >
        <FileUpload
          @upload-success="handleUploadSuccess"
          @cancel="showUploadDialog = false"
        />
      </el-dialog>

      <!-- 文件預覽對話框 -->
      <el-dialog
        v-model="showPreviewDialog"
        :title="previewFile?.name"
        width="80%"
        top="5vh"
      >
        <FilePreview
          v-if="previewFile"
          :file="previewFile"
          @close="showPreviewDialog = false"
        />
      </el-dialog>

      <!-- 文件分享對話框 -->
      <el-dialog
        v-model="showShareDialog"
        title="分享文件"
        width="500px"
      >
        <FileShare
          v-if="shareFile"
          :file="shareFile"
          @share-success="handleShareSuccess"
          @cancel="showShareDialog = false"
        />
      </el-dialog>

      <!-- 版本歷史對話框 -->
      <el-dialog
        v-model="showVersionDialog"
        title="版本歷史"
        width="700px"
      >
        <FileVersionHistory
          v-if="versionFile"
          :file="versionFile"
          @close="showVersionDialog = false"
        />
      </el-dialog>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Upload,
  Download,
  View,
  Share,
  ArrowDown,
  Grid,
  List,
  Document,
  FolderOpened,
  Clock,
  MoreFilled,
  Picture,
  VideoPlay,
  Headphone,
  Files,
  DocumentCopy
} from '@element-plus/icons-vue'
import AppLayout from '../components/layout/AppLayout.vue'
import FileUpload from '../components/file/FileUpload.vue'
import FilePreview from '../components/file/FilePreview.vue'
import FileShare from '../components/file/FileShare.vue'
import FileVersionHistory from '../components/file/FileVersionHistory.vue'
import api from '../services/api'

const router = useRouter()

// 響應式數據
const loading = ref(false)
const files = ref<any[]>([])
const projects = ref<any[]>([])
const searchQuery = ref('')
const selectedType = ref('')
const selectedProject = ref('')
const viewMode = ref('list')
const selectedFiles = ref<number[]>([])
const currentPage = ref(1)
const pageSize = ref(20)

// 對話框狀態
const showUploadDialog = ref(false)
const showPreviewDialog = ref(false)
const showShareDialog = ref(false)
const showVersionDialog = ref(false)
const previewFile = ref<any>(null)
const shareFile = ref<any>(null)
const versionFile = ref<any>(null)

// 統計數據
const statistics = reactive({
  totalFiles: 0,
  totalSize: 0,
  recentFiles: 0,
  sharedFiles: 0
})

// 計算屬性
const filteredFiles = computed(() => {
  let filtered = files.value

  // 搜索過濾
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(file =>
      file.name.toLowerCase().includes(query) ||
      (file.description && file.description.toLowerCase().includes(query))
    )
  }

  // 類型過濾
  if (selectedType.value) {
    filtered = filtered.filter(file => file.type === selectedType.value)
  }

  // 專案過濾
  if (selectedProject.value) {
    filtered = filtered.filter(file => file.project?.id === selectedProject.value)
  }

  return filtered
})

// 方法
const fetchFiles = async () => {
  loading.value = true
  try {
    const response = await api.getFiles()
    files.value = response.data
    updateStatistics()
  } catch (error) {
    console.error('獲取文件列表失敗:', error)
    ElMessage.error('獲取文件列表失敗')
  } finally {
    loading.value = false
  }
}

const fetchProjects = async () => {
  try {
    const response = await api.getProjects()
    projects.value = response.data
  } catch (error) {
    console.error('獲取專案列表失敗:', error)
  }
}

const updateStatistics = () => {
  statistics.totalFiles = files.value.length
  statistics.totalSize = files.value.reduce((sum, file) => sum + (file.size || 0), 0)

  // 計算本週新增文件
  const weekAgo = new Date()
  weekAgo.setDate(weekAgo.getDate() - 7)
  statistics.recentFiles = files.value.filter(file =>
    new Date(file.createdAt) > weekAgo
  ).length

  statistics.sharedFiles = files.value.filter(file => file.isShared).length
}

const handleSearch = () => {
  currentPage.value = 1
}

const resetFilters = () => {
  searchQuery.value = ''
  selectedType.value = ''
  selectedProject.value = ''
  currentPage.value = 1
}

const toggleFileSelection = (fileId: number) => {
  const index = selectedFiles.value.indexOf(fileId)
  if (index > -1) {
    selectedFiles.value.splice(index, 1)
  } else {
    selectedFiles.value.push(fileId)
  }
}

const handleSelectionChange = (selection: any[]) => {
  selectedFiles.value = selection.map(file => file.id)
}

const previewFile = (file: any) => {
  previewFile.value = file
  showPreviewDialog.value = true
}

const downloadFile = async (file: any) => {
  try {
    const response = await api.downloadFile(file.id)
    // 創建下載鏈接
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', file.name)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)

    ElMessage.success('文件下載成功')
  } catch (error) {
    console.error('下載文件失敗:', error)
    ElMessage.error('下載文件失敗')
  }
}

const handleFileAction = async (command: string, file: any) => {
  switch (command) {
    case 'share':
      shareFile.value = file
      showShareDialog.value = true
      break
    case 'version':
      versionFile.value = file
      showVersionDialog.value = true
      break
    case 'edit':
      // 編輯文件信息
      console.log('編輯文件信息:', file)
      break
    case 'move':
      // 移動文件
      console.log('移動文件:', file)
      break
    case 'delete':
      await deleteFile(file)
      break
  }
}

const handleBatchAction = async (command: string) => {
  if (selectedFiles.value.length === 0) return

  switch (command) {
    case 'download':
      await batchDownload()
      break
    case 'move':
      // 批量移動
      console.log('批量移動文件')
      break
    case 'delete':
      await batchDelete()
      break
  }
}

const deleteFile = async (file: any) => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除文件 "${file.name}" 嗎？此操作不可恢復。`,
      '刪除文件',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await api.deleteFile(file.id)
    ElMessage.success('文件已刪除')
    fetchFiles()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('刪除文件失敗:', error)
      ElMessage.error('刪除文件失敗')
    }
  }
}

const batchDownload = async () => {
  try {
    await ElMessageBox.confirm(
      `確定要下載選中的 ${selectedFiles.value.length} 個文件嗎？`,
      '批量下載',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    // 實現批量下載邏輯
    for (const fileId of selectedFiles.value) {
      const file = files.value.find(f => f.id === fileId)
      if (file) {
        await downloadFile(file)
      }
    }

    selectedFiles.value = []
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('批量下載失敗:', error)
      ElMessage.error('批量下載失敗')
    }
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除選中的 ${selectedFiles.value.length} 個文件嗎？此操作不可恢復。`,
      '批量刪除',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await api.batchDeleteFiles(selectedFiles.value)
    ElMessage.success('文件已批量刪除')
    selectedFiles.value = []
    fetchFiles()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('批量刪除失敗:', error)
      ElMessage.error('批量刪除失敗')
    }
  }
}

const handleUploadSuccess = () => {
  showUploadDialog.value = false
  fetchFiles()
  ElMessage.success('文件上傳成功')
}

const handleShareSuccess = () => {
  showShareDialog.value = false
  fetchFiles()
  ElMessage.success('文件分享成功')
}

const resetUploadForm = () => {
  // 重置上傳表單
}

const getFileIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    'DOCUMENT': DocumentCopy,
    'IMAGE': Picture,
    'VIDEO': VideoPlay,
    'AUDIO': Headphone,
    'ARCHIVE': Files,
    'OTHER': Document
  }
  return iconMap[type] || Document
}

const getFileTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    'DOCUMENT': '文檔',
    'IMAGE': '圖片',
    'VIDEO': '視頻',
    'AUDIO': '音頻',
    'ARCHIVE': '壓縮包',
    'OTHER': '其他'
  }
  return labelMap[type] || type
}

const formatFileSize = (bytes: number) => {
  if (!bytes) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-TW', {
    month: '2-digit',
    day: '2-digit'
  })
}

const formatDateTime = (dateString: string) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

onMounted(() => {
  fetchFiles()
  fetchProjects()
})
</script>

<style scoped>
.document-management-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

/* 統計卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 8px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.storage {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.recent {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.shared {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

/* 工具欄 */
.toolbar-card {
  margin-bottom: 20px;
}

.toolbar-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 文件卡片 */
.files-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 網格視圖 */
.grid-view {
  padding: 20px;
}

.files-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.file-card {
  position: relative;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.file-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.file-card.selected {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.file-checkbox {
  position: absolute;
  top: 8px;
  left: 8px;
}

.file-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80px;
  margin-bottom: 12px;
  color: #409eff;
}

.file-thumbnail {
  max-width: 100%;
  max-height: 80px;
  border-radius: 4px;
}

.file-info {
  text-align: center;
  margin-bottom: 12px;
}

.file-name {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
}

.file-actions {
  display: flex;
  justify-content: center;
  gap: 4px;
}

/* 列表視圖 */
.list-view {
  padding: 16px 0;
}

.file-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-type-icon {
  color: #409eff;
  font-size: 16px;
}

.file-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 分頁 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .document-management-view {
    padding: 12px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .toolbar-row {
    flex-direction: column;
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }

  .files-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 12px;
  }

  .file-card {
    padding: 12px;
  }

  .file-icon {
    height: 60px;
  }

  .stat-content {
    padding: 12px;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .stat-number {
    font-size: 24px;
  }
}

/* Element Plus 樣式覆蓋 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f8f9fa;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #303133;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table__row:hover > td) {
  background-color: #f5f7fa;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--small) {
  padding: 5px 11px;
  font-size: 12px;
}

:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 500;
}

:deep(.el-dialog__body) {
  padding: 20px;
}
</style>
