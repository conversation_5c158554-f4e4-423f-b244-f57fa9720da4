<template>
  <AppLayout>
    <div class="resource-allocation-view">
      <div class="page-header">
        <h1>資源分配管理</h1>
        <p>管理和分配項目資源</p>
      </div>

      <!-- 資源分配列表 -->
      <ResourceAllocationList
        v-if="!showForm"
        @create="handleCreate"
        @edit="handleEdit"
      />

      <!-- 資源分配表單 -->
      <div v-else class="form-container">
        <div class="form-header">
          <h2>{{ isEdit ? '編輯資源分配' : '創建資源分配' }}</h2>
        </div>
        
        <ResourceAllocationForm
          :resource-allocation="selectedAllocation"
          :is-edit="isEdit"
          @success="handleFormSuccess"
          @cancel="handleFormCancel"
        />
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import AppLayout from '../components/layout/AppLayout.vue'
import ResourceAllocationList from '../components/resource/ResourceAllocationList.vue'
import ResourceAllocationForm from '../components/resource/ResourceAllocationForm.vue'
import type { ResourceAllocation } from '../stores/resourceAllocation'

const showForm = ref(false)
const isEdit = ref(false)
const selectedAllocation = ref<ResourceAllocation | null>(null)

const handleCreate = () => {
  selectedAllocation.value = null
  isEdit.value = false
  showForm.value = true
}

const handleEdit = (allocation: ResourceAllocation) => {
  selectedAllocation.value = allocation
  isEdit.value = true
  showForm.value = true
}

const handleFormSuccess = () => {
  showForm.value = false
  selectedAllocation.value = null
  isEdit.value = false
}

const handleFormCancel = () => {
  showForm.value = false
  selectedAllocation.value = null
  isEdit.value = false
}
</script>

<style scoped>
.resource-allocation-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.form-container {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.form-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.form-header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}
</style>
