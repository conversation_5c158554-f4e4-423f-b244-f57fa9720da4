<template>
  <AppLayout>
    <div class="customer-detail-view" v-loading="loading">
      <div class="page-header">
        <div class="page-title">
          <h1>{{ customer.name || '客戶詳情' }}</h1>
          <p>查看和管理客戶的詳細信息</p>
        </div>
        
        <div class="page-actions">
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon> 返回客戶列表
          </el-button>
          <el-button type="primary" @click="editCustomer">
            <el-icon><Edit /></el-icon> 編輯客戶
          </el-button>
        </div>
      </div>

      <div class="customer-content" v-if="customer.id">
        <el-row :gutter="20">
          <el-col :span="16">
            <!-- 客戶基本信息 -->
            <el-card class="info-card">
              <template #header>
                <div class="card-header">
                  <span>客戶信息</span>
                  <el-tag v-if="customer.customerType" type="info">
                    {{ customer.customerType.name }}
                  </el-tag>
                </div>
              </template>
              
              <el-descriptions :column="2" border>
                <el-descriptions-item label="客戶名稱" :span="2">{{ customer.name }}</el-descriptions-item>
                <el-descriptions-item label="聯絡人">{{ customer.contactPerson || '-' }}</el-descriptions-item>
                <el-descriptions-item label="電話">{{ customer.phone || '-' }}</el-descriptions-item>
                <el-descriptions-item label="電子郵件">{{ customer.email || '-' }}</el-descriptions-item>
                <el-descriptions-item label="客戶類型">
                  {{ customer.customerType?.name || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="地址" :span="2">{{ customer.address || '-' }}</el-descriptions-item>
                <el-descriptions-item label="標籤" :span="2">
                  <div v-if="customer.tags && customer.tags.length > 0" class="tags-container">
                    <el-tag
                      v-for="tag in customer.tags"
                      :key="tag.id"
                      :style="{ backgroundColor: tag.color + '20', borderColor: tag.color, color: tag.color }"
                      class="tag-item"
                    >
                      {{ tag.name }}
                    </el-tag>
                  </div>
                  <span v-else>-</span>
                </el-descriptions-item>
                <el-descriptions-item label="創建時間">{{ formatDate(customer.createdAt) }}</el-descriptions-item>
                <el-descriptions-item label="更新時間">{{ formatDate(customer.updatedAt) }}</el-descriptions-item>
              </el-descriptions>
            </el-card>

            <!-- 相關專案 -->
            <el-card class="projects-card">
              <template #header>
                <div class="card-header">
                  <span>相關專案 ({{ projects.length }})</span>
                  <el-button type="primary" size="small" @click="createProject">新增專案</el-button>
                </div>
              </template>
              
              <el-table :data="projects" style="width: 100%" v-loading="projectsLoading">
                <el-table-column prop="name" label="專案名稱" min-width="150" />
                <el-table-column label="狀態" width="100">
                  <template #default="scope">
                    <el-tag :type="getProjectStatusType(scope.row.status)">
                      {{ getProjectStatusLabel(scope.row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="進度" width="120">
                  <template #default="scope">
                    <el-progress :percentage="scope.row.progress || 0" :stroke-width="6" />
                  </template>
                </el-table-column>
                <el-table-column label="開始日期" width="120">
                  <template #default="scope">
                    {{ formatDate(scope.row.startDate) }}
                  </template>
                </el-table-column>
                <el-table-column label="結束日期" width="120">
                  <template #default="scope">
                    {{ formatDate(scope.row.endDate) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100">
                  <template #default="scope">
                    <el-button size="small" type="primary" @click="viewProject(scope.row)">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
              
              <div v-if="projects.length === 0" class="empty-state">
                <el-empty description="暫無相關專案" />
              </div>
            </el-card>
          </el-col>

          <el-col :span="8">
            <!-- 統計信息 -->
            <el-card class="stats-card">
              <template #header>
                <span>統計信息</span>
              </template>
              
              <div class="stats-grid">
                <div class="stat-item">
                  <div class="stat-value">{{ customer.projectCount || 0 }}</div>
                  <div class="stat-label">專案數量</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ customer.contactRecordCount || 0 }}</div>
                  <div class="stat-label">聯絡記錄</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ contactRecords.length }}</div>
                  <div class="stat-label">最近聯絡</div>
                </div>
              </div>
            </el-card>

            <!-- 聯絡記錄 -->
            <el-card class="contacts-card">
              <template #header>
                <div class="card-header">
                  <span>聯絡記錄 ({{ contactRecords.length }})</span>
                  <el-button type="primary" size="small" @click="addContactRecord">新增記錄</el-button>
                </div>
              </template>
              
              <div v-loading="contactsLoading">
                <div v-for="record in contactRecords.slice(0, 5)" :key="record.id" class="contact-item">
                  <div class="contact-header">
                    <el-tag size="small" :type="getContactTypeColor(record.contactType)">
                      {{ getContactTypeLabel(record.contactType) }}
                    </el-tag>
                    <span class="contact-date">{{ formatDate(record.contactDate) }}</span>
                  </div>
                  <div class="contact-content">{{ record.content }}</div>
                </div>
                
                <div v-if="contactRecords.length === 0" class="empty-state">
                  <el-empty description="暫無聯絡記錄" />
                </div>
                
                <div v-if="contactRecords.length > 5" class="view-more">
                  <el-button type="text" @click="viewAllContacts">查看全部 {{ contactRecords.length }} 條記錄</el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Edit } from '@element-plus/icons-vue'
import AppLayout from '../components/layout/AppLayout.vue'
import api from '../services/api'

const route = useRoute()
const router = useRouter()

// Data
const customer = ref({})
const projects = ref([])
const contactRecords = ref([])

// Loading states
const loading = ref(false)
const projectsLoading = ref(false)
const contactsLoading = ref(false)

// Computed
const customerId = computed(() => Number(route.params.id))

// Methods
const fetchCustomer = async () => {
  loading.value = true
  try {
    const response = await api.getCustomer(customerId.value)
    customer.value = response.data
  } catch (error) {
    console.error('Failed to fetch customer:', error)
    ElMessage.error('獲取客戶詳情失敗')
  } finally {
    loading.value = false
  }
}

const fetchProjects = async () => {
  projectsLoading.value = true
  try {
    const response = await api.getCustomerProjects(customerId.value)
    projects.value = response.data
  } catch (error) {
    console.error('Failed to fetch projects:', error)
  } finally {
    projectsLoading.value = false
  }
}

const fetchContactRecords = async () => {
  contactsLoading.value = true
  try {
    const response = await api.getCustomerContacts(customerId.value)
    contactRecords.value = response.data
  } catch (error) {
    console.error('Failed to fetch contact records:', error)
  } finally {
    contactsLoading.value = false
  }
}

const formatDate = (date: string) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-TW')
}

const getProjectStatusType = (status: string) => {
  const types = {
    'PLANNING': 'info',
    'IN_PROGRESS': 'warning',
    'COMPLETED': 'success',
    'ON_HOLD': 'warning',
    'CANCELLED': 'danger'
  }
  return types[status] || ''
}

const getProjectStatusLabel = (status: string) => {
  const labels = {
    'PLANNING': '規劃中',
    'IN_PROGRESS': '進行中',
    'COMPLETED': '已完成',
    'ON_HOLD': '暫停',
    'CANCELLED': '已取消'
  }
  return labels[status] || status
}

const getContactTypeColor = (type: string) => {
  const colors = {
    'PHONE': 'primary',
    'EMAIL': 'success',
    'MEETING': 'warning',
    'OTHER': 'info'
  }
  return colors[type] || 'info'
}

const getContactTypeLabel = (type: string) => {
  const labels = {
    'PHONE': '電話',
    'EMAIL': '郵件',
    'MEETING': '會議',
    'OTHER': '其他'
  }
  return labels[type] || type
}

const goBack = () => {
  router.push({ name: 'customers' })
}

const editCustomer = () => {
  router.push({ name: 'customer-edit', params: { id: customerId.value } })
}

const createProject = () => {
  router.push({ 
    name: 'project-create', 
    query: { customerId: customerId.value } 
  })
}

const viewProject = (project: any) => {
  router.push({ name: 'project-detail', params: { id: project.id } })
}

const addContactRecord = () => {
  router.push({ name: 'contact-records' })
}

const viewAllContacts = () => {
  router.push({ name: 'contact-records' })
}

onMounted(() => {
  fetchCustomer()
  fetchProjects()
  fetchContactRecords()
})
</script>

<style scoped>
.customer-detail-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.page-title h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.page-title p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.customer-content {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-card {
  margin-bottom: 20px;
}

.projects-card {
  margin-bottom: 20px;
}

.stats-card {
  margin-bottom: 20px;
}

.contacts-card {
  margin-bottom: 20px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.contact-item {
  margin-bottom: 12px;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #409eff;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.contact-date {
  font-size: 12px;
  color: #909399;
}

.contact-content {
  font-size: 14px;
  line-height: 1.5;
  color: #303133;
}

.empty-state {
  padding: 20px;
  text-align: center;
}

.view-more {
  text-align: center;
  margin-top: 12px;
}
</style>
