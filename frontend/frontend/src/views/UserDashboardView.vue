<template>
  <AppLayout>
    <div class="user-dashboard-view">
      <div class="page-header">
        <h1>用戶管理儀表板</h1>
        <p>用戶統計和管理概覽</p>
      </div>

      <!-- 統計卡片 -->
      <div class="stats-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon total">
                  <el-icon><User /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ statistics.totalUsers || 0 }}</div>
                  <div class="stat-label">總用戶數</div>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon enabled">
                  <el-icon><Check /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ statistics.enabledUsers || 0 }}</div>
                  <div class="stat-label">啟用用戶</div>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon disabled">
                  <el-icon><Close /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ statistics.disabledUsers || 0 }}</div>
                  <div class="stat-label">停用用戶</div>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon rate">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ enabledRate }}%</div>
                  <div class="stat-label">啟用率</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 用戶列表和批量操作 -->
      <el-card class="users-section">
        <template #header>
          <div class="card-header">
            <span>用戶管理</span>
            <div class="header-actions">
              <el-button
                type="success"
                :disabled="selectedUsers.length === 0"
                @click="batchEnableUsers"
              >
                批量啟用
              </el-button>
              <el-button
                type="danger"
                :disabled="selectedUsers.length === 0"
                @click="batchDisableUsers"
              >
                批量停用
              </el-button>
              <el-button type="primary" @click="goToCreateUser">
                新增用戶
              </el-button>
            </div>
          </div>
        </template>

        <!-- 搜索和篩選 -->
        <div class="filter-section">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-input
                v-model="searchQuery"
                placeholder="搜索用戶名或姓名..."
                clearable
                @input="handleSearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-select
                v-model="selectedStatus"
                placeholder="用戶狀態"
                clearable
                @change="fetchUsers"
              >
                <el-option label="啟用" :value="true" />
                <el-option label="停用" :value="false" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-select
                v-model="selectedDepartment"
                placeholder="部門"
                clearable
                @change="fetchUsers"
              >
                <el-option
                  v-for="dept in departments"
                  :key="dept.id"
                  :label="dept.name"
                  :value="dept.id"
                />
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-button @click="resetFilters">重置篩選</el-button>
            </el-col>
          </el-row>
        </div>

        <!-- 用戶表格 -->
        <el-table
          :data="filteredUsers"
          v-loading="loading"
          stripe
          style="width: 100%"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
        >
          <el-table-column type="selection" width="55" />

          <el-table-column prop="id" label="ID" width="80" />

          <el-table-column prop="username" label="用戶名" min-width="120" />

          <el-table-column label="姓名" min-width="120">
            <template #default="{ row }">
              {{ row.firstName }} {{ row.lastName }}
            </template>
          </el-table-column>

          <el-table-column prop="email" label="電子郵件" min-width="200" />

          <el-table-column label="部門" min-width="120">
            <template #default="{ row }">
              {{ row.department?.name || '無部門' }}
            </template>
          </el-table-column>

          <el-table-column label="角色" min-width="150">
            <template #default="{ row }">
              <el-tag
                v-for="role in row.roles"
                :key="role.id"
                :type="getRoleTagType(role.name)"
                size="small"
                style="margin-right: 4px;"
              >
                {{ role.displayName }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="enabled" label="狀態" width="100">
            <template #default="{ row }">
              <el-tag :type="row.enabled ? 'success' : 'danger'">
                {{ row.enabled ? '啟用' : '停用' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="createdAt" label="創建時間" width="180">
            <template #default="{ row }">
              {{ formatDate(row.createdAt) }}
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click.stop="viewUser(row)"
              >
                查看
              </el-button>
              <el-button
                type="warning"
                size="small"
                @click.stop="editUser(row)"
              >
                編輯
              </el-button>
              <el-button
                :type="row.enabled ? 'danger' : 'success'"
                size="small"
                @click.stop="toggleUserStatus(row)"
              >
                {{ row.enabled ? '停用' : '啟用' }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分頁 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, Check, Close, TrendCharts, Search } from '@element-plus/icons-vue'
import AppLayout from '../components/layout/AppLayout.vue'
import api from '../services/api'

const router = useRouter()

// Data
const users = ref<any[]>([])
const departments = ref<any[]>([])
const statistics = ref<any>({})
const loading = ref(false)
const searchQuery = ref('')
const selectedStatus = ref<boolean | null>(null)
const selectedDepartment = ref('')
const selectedUsers = ref<any[]>([])

// Pagination
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// Computed
const filteredUsers = computed(() => {
  let filtered = users.value

  if (searchQuery.value) {
    filtered = filtered.filter(user =>
      user.username.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      `${user.firstName} ${user.lastName}`.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  if (selectedStatus.value !== null) {
    filtered = filtered.filter(user => user.enabled === selectedStatus.value)
  }

  if (selectedDepartment.value) {
    filtered = filtered.filter(user => user.department?.id === selectedDepartment.value)
  }

  total.value = filtered.length
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filtered.slice(start, end)
})

const enabledRate = computed(() => {
  if (statistics.value.totalUsers === 0) return 0
  return ((statistics.value.enabledUsers / statistics.value.totalUsers) * 100).toFixed(1)
})

// Methods
const fetchUsers = async () => {
  loading.value = true
  try {
    const response = await api.getUsers()
    users.value = response.data
  } catch (error) {
    console.error('獲取用戶列表失敗:', error)
    ElMessage.error('獲取用戶列表失敗')
  } finally {
    loading.value = false
  }
}

const fetchStatistics = async () => {
  try {
    const response = await api.getUserStatistics()
    statistics.value = response.data
  } catch (error) {
    console.error('獲取用戶統計失敗:', error)
    ElMessage.error('獲取用戶統計失敗')
  }
}

const fetchDepartments = async () => {
  try {
    const response = await api.getDepartments()
    departments.value = response.data
  } catch (error) {
    console.error('獲取部門列表失敗:', error)
  }
}

const handleSearch = () => {
  currentPage.value = 1
}

const resetFilters = () => {
  searchQuery.value = ''
  selectedStatus.value = null
  selectedDepartment.value = ''
  currentPage.value = 1
  fetchUsers()
}

const handleSelectionChange = (selection: any[]) => {
  selectedUsers.value = selection
}

const handleRowClick = (row: any) => {
  viewUser(row)
}

const viewUser = (user: any) => {
  router.push({ name: 'user-detail', params: { id: user.id } })
}

const editUser = (user: any) => {
  router.push({ name: 'user-edit', params: { id: user.id } })
}

const goToCreateUser = () => {
  router.push({ name: 'user-create' })
}

const toggleUserStatus = async (user: any) => {
  const action = user.enabled ? '停用' : '啟用'

  try {
    await ElMessageBox.confirm(
      `確定要${action}用戶 "${user.firstName} ${user.lastName}" 嗎？`,
      `${action}用戶`,
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    if (user.enabled) {
      await api.disableUser(user.id)
    } else {
      await api.enableUser(user.id)
    }

    ElMessage.success(`用戶已${action}`)
    fetchUsers()
    fetchStatistics()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error(`${action}用戶失敗:`, error)
      ElMessage.error(`${action}用戶失敗`)
    }
  }
}

const batchEnableUsers = async () => {
  if (selectedUsers.value.length === 0) return

  try {
    await ElMessageBox.confirm(
      `確定要啟用選中的 ${selectedUsers.value.length} 個用戶嗎？`,
      '批量啟用用戶',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const userIds = selectedUsers.value.map(user => user.id)
    await api.batchEnableUsers(userIds)

    ElMessage.success(`已啟用 ${selectedUsers.value.length} 個用戶`)
    fetchUsers()
    fetchStatistics()
    selectedUsers.value = []
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('批量啟用用戶失敗:', error)
      ElMessage.error('批量啟用用戶失敗')
    }
  }
}

const batchDisableUsers = async () => {
  if (selectedUsers.value.length === 0) return

  try {
    await ElMessageBox.confirm(
      `確定要停用選中的 ${selectedUsers.value.length} 個用戶嗎？`,
      '批量停用用戶',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const userIds = selectedUsers.value.map(user => user.id)
    await api.batchDisableUsers(userIds)

    ElMessage.success(`已停用 ${selectedUsers.value.length} 個用戶`)
    fetchUsers()
    fetchStatistics()
    selectedUsers.value = []
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('批量停用用戶失敗:', error)
      ElMessage.error('批量停用用戶失敗')
    }
  }
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

const getRoleTagType = (roleName: string) => {
  const types: Record<string, string> = {
    'ROLE_SUPER_ADMIN': 'danger',
    'ROLE_ADMIN': 'warning',
    'ROLE_DEPARTMENT_HEAD': 'success',
    'ROLE_EMPLOYEE': 'info',
    'ROLE_GUEST': ''
  }
  return types[roleName] || ''
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('zh-TW')
}

onMounted(() => {
  fetchUsers()
  fetchStatistics()
  fetchDepartments()
})
</script>

<style scoped>
.user-dashboard-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #303133;
}

.page-header p {
  margin: 5px 0 0 0;
  color: #606266;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.enabled {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.disabled {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.rate {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.users-section {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .stats-section .el-col {
    margin-bottom: 20px;
  }

  .header-actions {
    flex-direction: column;
    gap: 5px;
  }

  .filter-section .el-col {
    margin-bottom: 10px;
  }
}
</style>
