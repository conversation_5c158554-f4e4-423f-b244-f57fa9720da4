<template>
  <AppLayout>
    <div class="progress-dashboard">
      <div class="page-header">
        <div class="page-title">
          <h1>進度儀表板</h1>
          <p>全面掌握項目和任務的執行進度</p>
        </div>
        <div class="page-actions">
          <el-button type="primary" @click="refreshAllData" :loading="refreshing">
            <el-icon><Refresh /></el-icon>
            刷新數據
          </el-button>
        </div>
      </div>

      <div class="dashboard-content">
        <!-- 總覽統計 -->
        <el-row :gutter="20" class="overview-stats">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon projects">
                  <el-icon><FolderOpened /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ totalProjects }}</div>
                  <div class="stat-label">總項目數</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon tasks">
                  <el-icon><List /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ totalTasks }}</div>
                  <div class="stat-label">總任務數</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon completed">
                  <el-icon><CircleCheck /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ completedTasks }}</div>
                  <div class="stat-label">已完成任務</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon overdue">
                  <el-icon><Warning /></el-icon>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ overdueTasks }}</div>
                  <div class="stat-label">逾期任務</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 項目進度列表 -->
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="16">
            <el-card class="projects-card">
              <template #header>
                <div class="card-header">
                  <span>項目進度概覽</span>
                  <el-select 
                    v-model="selectedProjectFilter" 
                    placeholder="篩選項目狀態"
                    style="width: 150px;"
                    @change="filterProjects"
                  >
                    <el-option label="全部項目" value="all" />
                    <el-option label="進行中" value="IN_PROGRESS" />
                    <el-option label="已完成" value="COMPLETED" />
                    <el-option label="延期" value="DELAYED" />
                  </el-select>
                </div>
              </template>

              <div v-loading="projectsLoading">
                <div v-for="project in filteredProjects" :key="project.id" class="project-item">
                  <div class="project-header">
                    <div class="project-info">
                      <h4 class="project-name" @click="viewProject(project.id)">{{ project.name }}</h4>
                      <div class="project-meta">
                        <el-tag :type="getStatusType(project.status)" size="small">
                          {{ getStatusLabel(project.status) }}
                        </el-tag>
                        <span class="project-dates">
                          {{ formatDate(project.startDate) }} - {{ formatDate(project.endDate) }}
                        </span>
                      </div>
                    </div>
                    <div class="project-progress">
                      <span class="progress-text">{{ project.progress || 0 }}%</span>
                    </div>
                  </div>
                  <el-progress 
                    :percentage="project.progress || 0" 
                    :color="getProgressColor(project.progress || 0)"
                    :stroke-width="8"
                    :show-text="false"
                  />
                </div>
                <div v-if="filteredProjects.length === 0" class="empty-state">
                  <el-empty description="暫無項目數據" />
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="8">
            <el-card class="alerts-card">
              <template #header>
                <span>進度警報</span>
              </template>

              <div class="alerts-content">
                <div v-for="alert in progressAlerts" :key="alert.id" class="alert-item">
                  <div class="alert-icon" :class="alert.type">
                    <el-icon><Warning /></el-icon>
                  </div>
                  <div class="alert-content">
                    <div class="alert-title">{{ alert.title }}</div>
                    <div class="alert-desc">{{ alert.description }}</div>
                    <div class="alert-time">{{ formatTime(alert.time) }}</div>
                  </div>
                </div>
                <div v-if="progressAlerts.length === 0" class="no-alerts">
                  <el-icon><CircleCheck /></el-icon>
                  <span>暫無進度警報</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 任務進度分析 -->
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="24">
            <el-card class="analysis-card">
              <template #header>
                <span>任務進度分析</span>
              </template>

              <div class="analysis-content">
                <div class="analysis-grid">
                  <div class="analysis-item">
                    <div class="analysis-title">平均完成率</div>
                    <div class="analysis-value">{{ averageCompletion }}%</div>
                    <el-progress 
                      :percentage="averageCompletion" 
                      :stroke-width="6"
                      :show-text="false"
                    />
                  </div>
                  <div class="analysis-item">
                    <div class="analysis-title">按時完成率</div>
                    <div class="analysis-value">{{ onTimeCompletion }}%</div>
                    <el-progress 
                      :percentage="onTimeCompletion" 
                      :stroke-width="6"
                      :show-text="false"
                      color="#67c23a"
                    />
                  </div>
                  <div class="analysis-item">
                    <div class="analysis-title">資源利用率</div>
                    <div class="analysis-value">{{ resourceUtilization }}%</div>
                    <el-progress 
                      :percentage="resourceUtilization" 
                      :stroke-width="6"
                      :show-text="false"
                      color="#e6a23c"
                    />
                  </div>
                  <div class="analysis-item">
                    <div class="analysis-title">團隊效率</div>
                    <div class="analysis-value">{{ teamEfficiency }}%</div>
                    <el-progress 
                      :percentage="teamEfficiency" 
                      :stroke-width="6"
                      :show-text="false"
                      color="#409eff"
                    />
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Refresh, 
  FolderOpened, 
  List, 
  CircleCheck, 
  Warning 
} from '@element-plus/icons-vue'
import AppLayout from '../components/layout/AppLayout.vue'
import api from '../services/api'

const router = useRouter()

// 響應式數據
const refreshing = ref(false)
const projectsLoading = ref(false)
const projects = ref<any[]>([])
const tasks = ref<any[]>([])
const selectedProjectFilter = ref('all')

// 計算屬性
const totalProjects = computed(() => projects.value.length)
const totalTasks = computed(() => tasks.value.length)
const completedTasks = computed(() => 
  tasks.value.filter(task => task.status === 'COMPLETED').length
)
const overdueTasks = computed(() => {
  const now = new Date()
  return tasks.value.filter(task => 
    new Date(task.dueDate) < now && task.status !== 'COMPLETED'
  ).length
})

const filteredProjects = computed(() => {
  if (selectedProjectFilter.value === 'all') {
    return projects.value
  }
  return projects.value.filter(project => project.status === selectedProjectFilter.value)
})

const averageCompletion = computed(() => {
  if (tasks.value.length === 0) return 0
  const total = tasks.value.reduce((sum, task) => sum + (task.progress || 0), 0)
  return Math.round(total / tasks.value.length)
})

const onTimeCompletion = computed(() => {
  const completedOnTime = tasks.value.filter(task => {
    if (task.status !== 'COMPLETED') return false
    // 簡化邏輯：假設按時完成
    return true
  }).length
  return totalTasks.value > 0 ? Math.round((completedOnTime / totalTasks.value) * 100) : 0
})

const resourceUtilization = computed(() => {
  // 模擬資源利用率計算
  return Math.round(Math.random() * 30 + 70) // 70-100%
})

const teamEfficiency = computed(() => {
  // 模擬團隊效率計算
  return Math.round(Math.random() * 20 + 80) // 80-100%
})

const progressAlerts = computed(() => {
  const alerts = []
  const now = new Date()
  
  // 檢查逾期任務
  tasks.value.forEach(task => {
    if (new Date(task.dueDate) < now && task.status !== 'COMPLETED') {
      alerts.push({
        id: `overdue-${task.id}`,
        type: 'danger',
        title: '任務逾期',
        description: `任務「${task.name}」已逾期`,
        time: new Date()
      })
    }
  })
  
  // 檢查即將到期的任務
  tasks.value.forEach(task => {
    const daysToDeadline = (new Date(task.dueDate).getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
    if (daysToDeadline <= 3 && daysToDeadline > 0 && task.status !== 'COMPLETED') {
      alerts.push({
        id: `due-soon-${task.id}`,
        type: 'warning',
        title: '即將到期',
        description: `任務「${task.name}」將在${Math.ceil(daysToDeadline)}天後到期`,
        time: new Date()
      })
    }
  })
  
  return alerts.slice(0, 5) // 最多顯示5個警報
})

// 方法
const fetchProjects = async () => {
  projectsLoading.value = true
  try {
    const response = await api.getProjects()
    projects.value = response.data
  } catch (error) {
    console.error('Failed to fetch projects:', error)
    ElMessage.error('獲取項目數據失敗')
  } finally {
    projectsLoading.value = false
  }
}

const fetchTasks = async () => {
  try {
    const response = await api.getTasks()
    tasks.value = response.data
  } catch (error) {
    console.error('Failed to fetch tasks:', error)
    ElMessage.error('獲取任務數據失敗')
  }
}

const refreshAllData = async () => {
  refreshing.value = true
  try {
    await Promise.all([fetchProjects(), fetchTasks()])
    ElMessage.success('數據刷新成功')
  } finally {
    refreshing.value = false
  }
}

const filterProjects = () => {
  // 篩選邏輯已在計算屬性中處理
}

const viewProject = (projectId: number) => {
  router.push({ name: 'project-detail', params: { id: projectId } })
}

const getStatusType = (status: string) => {
  const typeMap = {
    'NOT_STARTED': 'info',
    'IN_PROGRESS': 'warning',
    'COMPLETED': 'success',
    'DELAYED': 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusLabel = (status: string) => {
  const labelMap = {
    'NOT_STARTED': '未開始',
    'IN_PROGRESS': '進行中',
    'COMPLETED': '已完成',
    'DELAYED': '延期'
  }
  return labelMap[status] || status
}

const getProgressColor = (percentage: number) => {
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('zh-TW')
}

const formatTime = (date: Date) => {
  return date.toLocaleString('zh-TW')
}

onMounted(() => {
  refreshAllData()
})
</script>

<style scoped>
.progress-dashboard {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.page-title h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.page-title p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.overview-stats {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 20px;
  color: white;
}

.stat-icon.projects { background-color: #409eff; }
.stat-icon.tasks { background-color: #67c23a; }
.stat-icon.completed { background-color: #e6a23c; }
.stat-icon.overdue { background-color: #f56c6c; }

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #606266;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.project-item {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.project-item:last-child {
  margin-bottom: 0;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.project-name {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #303133;
  cursor: pointer;
  transition: color 0.3s;
}

.project-name:hover {
  color: #409eff;
}

.project-meta {
  display: flex;
  align-items: center;
  gap: 10px;
}

.project-dates {
  font-size: 12px;
  color: #909399;
}

.progress-text {
  font-size: 16px;
  font-weight: bold;
  color: #409eff;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

.alerts-content {
  max-height: 400px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
  flex-shrink: 0;
}

.alert-icon.danger { background-color: #f56c6c; }
.alert-icon.warning { background-color: #e6a23c; }

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.alert-desc {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.alert-time {
  font-size: 11px;
  color: #909399;
}

.no-alerts {
  text-align: center;
  padding: 40px;
  color: #67c23a;
}

.no-alerts .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.analysis-item {
  text-align: center;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.analysis-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.analysis-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}
</style>
