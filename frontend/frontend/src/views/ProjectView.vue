<template>
  <AppLayout>
    <div class="project-container">
      <h1 class="page-title">專案管理</h1>
      
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="專案列表" name="list">
          <ProjectList
            @view-project="viewProject"
            @add-project="activeTab = 'add'"
            @refresh="refreshData"
          />
        </el-tab-pane>
        
        <el-tab-pane label="新增專案" name="add">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>新增專案</span>
              </div>
            </template>
            <ProjectForm
              @submit-success="handleFormSuccess"
              submit-button-text="新增專案"
            />
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import AppLayout from '../components/layout/AppLayout.vue'
import ProjectList from '../components/project/ProjectList.vue'
import ProjectForm from '../components/project/ProjectForm.vue'

const router = useRouter()
const activeTab = ref('list')

// Handle tab click
const handleTabClick = () => {
  // Reset any state if needed
}

// View project
const viewProject = (id: number) => {
  router.push(`/projects/${id}`)
}

// Handle form success
const handleFormSuccess = (project: any) => {
  activeTab.value = 'list'
  refreshData()
}

// Refresh data
const refreshData = () => {
  // This will be handled by the ProjectList component
}
</script>

<style scoped>
.project-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
  font-size: 24px;
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
