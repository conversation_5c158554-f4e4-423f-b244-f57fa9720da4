<template>
  <AppLayout>
    <div class="activity-log-view">
      <div class="page-header">
        <h1>操作記錄管理</h1>
        <p>查看系統中所有用戶的操作記錄和活動統計</p>
      </div>

      <!-- 統計卡片 -->
      <div class="stats-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-number">{{ activityLogs.length }}</div>
                <div class="stat-label">總記錄數</div>
              </div>
              <el-icon class="stat-icon" color="#409EFF"><Document /></el-icon>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-number">{{ loginLogs.length }}</div>
                <div class="stat-label">登錄記錄</div>
              </div>
              <el-icon class="stat-icon" color="#67C23A"><User /></el-icon>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-number">{{ uniqueUsersCount }}</div>
                <div class="stat-label">活躍用戶</div>
              </div>
              <el-icon class="stat-icon" color="#E6A23C"><UserFilled /></el-icon>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-number">{{ todayLogsCount }}</div>
                <div class="stat-label">今日記錄</div>
              </div>
              <el-icon class="stat-icon" color="#F56C6C"><Calendar /></el-icon>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 篩選和搜索 -->
      <div class="filter-section">
        <el-card>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-select
                v-model="filters.activityType"
                placeholder="篩選活動類型"
                clearable
                style="width: 100%"
              >
                <el-option label="登錄" value="LOGIN" />
                <el-option label="登出" value="LOGOUT" />
                <el-option label="創建" value="CREATE" />
                <el-option label="更新" value="UPDATE" />
                <el-option label="刪除" value="DELETE" />
                <el-option label="查看" value="VIEW" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-date-picker
                v-model="filters.dateRange"
                type="datetimerange"
                range-separator="至"
                start-placeholder="開始時間"
                end-placeholder="結束時間"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </el-col>
            <el-col :span="6">
              <el-input
                v-model="filters.searchTerm"
                placeholder="搜索用戶或描述"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" @click="handleSearch" :loading="loading">
                <el-icon><Search /></el-icon>
                搜索
              </el-button>
              <el-button @click="handleReset">
                <el-icon><Refresh /></el-icon>
                重置
              </el-button>
            </el-col>
          </el-row>
        </el-card>
      </div>

      <!-- 活動記錄表格 -->
      <div class="table-section">
        <el-card>
          <el-table
            :data="filteredLogs"
            v-loading="loading"
            stripe
            style="width: 100%"
            :default-sort="{ prop: 'createdAt', order: 'descending' }"
          >
            <el-table-column prop="id" label="ID" width="80" />

            <el-table-column label="用戶" min-width="150">
              <template #default="{ row }">
                <div class="user-info">
                  <div class="user-name">{{ row.user.firstName }} {{ row.user.lastName }}</div>
                  <div class="user-username">@{{ row.user.username }}</div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="活動類型" width="120">
              <template #default="{ row }">
                <el-tag :type="getActivityTypeColor(row.activityType)">
                  <el-icon>
                    <component :is="getActivityTypeIcon(row.activityType)" />
                  </el-icon>
                  {{ getActivityTypeText(row.activityType) }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column label="描述" min-width="300">
              <template #default="{ row }">
                <el-tooltip :content="row.description" placement="top">
                  <span class="description-text">{{ truncateText(row.description, 80) }}</span>
                </el-tooltip>
              </template>
            </el-table-column>

            <el-table-column label="IP地址" width="140">
              <template #default="{ row }">
                <span v-if="row.ipAddress">{{ row.ipAddress }}</span>
                <span v-else class="text-muted">未記錄</span>
              </template>
            </el-table-column>

            <el-table-column label="時間" width="180" sortable prop="createdAt">
              <template #default="{ row }">
                {{ formatDateTime(row.createdAt) }}
              </template>
            </el-table-column>

            <el-table-column label="操作" width="100" fixed="right">
              <template #default="{ row }">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleView(row)"
                >
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分頁 -->
          <div class="pagination-section">
            <el-pagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :page-sizes="[20, 50, 100, 200]"
              :total="filteredLogs.length"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </el-card>
      </div>

      <!-- 查看詳情對話框 -->
      <el-dialog
        v-model="viewDialogVisible"
        title="操作記錄詳情"
        width="600px"
      >
        <div v-if="selectedLog" class="log-detail">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="記錄ID">
              {{ selectedLog.id }}
            </el-descriptions-item>
            <el-descriptions-item label="用戶">
              {{ selectedLog.user.firstName }} {{ selectedLog.user.lastName }}
            </el-descriptions-item>
            <el-descriptions-item label="用戶名">
              @{{ selectedLog.user.username }}
            </el-descriptions-item>
            <el-descriptions-item label="郵箱">
              {{ selectedLog.user.email }}
            </el-descriptions-item>
            <el-descriptions-item label="活動類型">
              <el-tag :type="getActivityTypeColor(selectedLog.activityType)">
                {{ getActivityTypeText(selectedLog.activityType) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="IP地址">
              {{ selectedLog.ipAddress || '未記錄' }}
            </el-descriptions-item>
            <el-descriptions-item label="時間" :span="2">
              {{ formatDateTime(selectedLog.createdAt) }}
            </el-descriptions-item>
          </el-descriptions>

          <div class="description-section">
            <h4>操作描述</h4>
            <p>{{ selectedLog.description }}</p>
          </div>
        </div>
      </el-dialog>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import {
  Search,
  Refresh,
  Document,
  User,
  UserFilled,
  Calendar,
  Plus,
  Edit,
  Delete
} from '@element-plus/icons-vue'
import AppLayout from '../components/layout/AppLayout.vue'
import { useActivityLogStore, type ActivityLog } from '../stores/activityLog'

const activityLogStore = useActivityLogStore()
const loading = ref(false)
const viewDialogVisible = ref(false)
const selectedLog = ref<ActivityLog | null>(null)

const filters = reactive({
  activityType: '',
  dateRange: [] as string[],
  searchTerm: ''
})

const pagination = reactive({
  currentPage: 1,
  pageSize: 50
})

const activityLogs = computed(() => activityLogStore.sortedActivityLogs)
const loginLogs = computed(() => activityLogStore.loginLogs)

const uniqueUsersCount = computed(() => {
  const userIds = new Set(activityLogs.value.map(log => log.user.id))
  return userIds.size
})

const todayLogsCount = computed(() => {
  const today = new Date().toISOString().split('T')[0]
  return activityLogs.value.filter(log =>
    log.createdAt.startsWith(today)
  ).length
})

const filteredLogs = computed(() => {
  let logs = activityLogs.value

  // 按活動類型篩選
  if (filters.activityType) {
    logs = logs.filter(log => log.activityType === filters.activityType)
  }

  // 按日期範圍篩選
  if (filters.dateRange && filters.dateRange.length === 2) {
    const [startDate, endDate] = filters.dateRange
    logs = logs.filter(log => {
      const logDate = new Date(log.createdAt)
      return logDate >= new Date(startDate) && logDate <= new Date(endDate)
    })
  }

  // 按搜索詞篩選
  if (filters.searchTerm) {
    logs = activityLogStore.searchActivityLogs(filters.searchTerm)
  }

  // 分頁
  const start = (pagination.currentPage - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return logs.slice(start, end)
})

const getActivityTypeColor = (type: string) => {
  const colors = {
    LOGIN: 'success',
    LOGOUT: 'info',
    CREATE: 'primary',
    UPDATE: 'warning',
    DELETE: 'danger',
    VIEW: ''
  }
  return colors[type as keyof typeof colors] || ''
}

const getActivityTypeText = (type: string) => {
  const texts = {
    LOGIN: '登錄',
    LOGOUT: '登出',
    CREATE: '創建',
    UPDATE: '更新',
    DELETE: '刪除',
    VIEW: '查看'
  }
  return texts[type as keyof typeof texts] || type
}

const getActivityTypeIcon = (type: string) => {
  const icons = {
    LOGIN: User,
    LOGOUT: User,
    CREATE: Plus,
    UPDATE: Edit,
    DELETE: Delete,
    VIEW: Document
  }
  return icons[type as keyof typeof icons] || Document
}

const truncateText = (text: string, maxLength: number) => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-TW')
}

const fetchActivityLogs = async () => {
  loading.value = true
  try {
    await activityLogStore.fetchAllActivityLogs()
  } catch (error) {
    console.error('獲取活動記錄失敗:', error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.currentPage = 1
  // 篩選邏輯已在computed中處理
}

const handleReset = () => {
  filters.activityType = ''
  filters.dateRange = []
  filters.searchTerm = ''
  pagination.currentPage = 1
}

const handleView = (log: ActivityLog) => {
  selectedLog.value = log
  viewDialogVisible.value = true
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
}

onMounted(() => {
  fetchActivityLogs()
})
</script>

<style scoped>
.activity-log-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.stat-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 40px;
  opacity: 0.3;
}

.filter-section {
  margin-bottom: 20px;
}

.table-section {
  margin-bottom: 20px;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  margin-bottom: 2px;
}

.user-username {
  font-size: 12px;
  color: #999;
}

.description-text {
  cursor: pointer;
}

.text-muted {
  color: #999;
}

.pagination-section {
  margin-top: 20px;
  text-align: center;
}

.log-detail {
  padding: 20px 0;
}

.description-section {
  margin-top: 20px;
}

.description-section h4 {
  margin-bottom: 10px;
  color: #333;
}

.description-section p {
  line-height: 1.6;
  color: #666;
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
}
</style>
