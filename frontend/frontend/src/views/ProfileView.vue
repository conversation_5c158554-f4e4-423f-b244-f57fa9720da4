<template>
  <AppLayout>
    <div class="profile-container">
      <h1 class="profile-title">個人資料</h1>
      
      <el-card class="profile-card">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本資料" name="info">
            <el-form
              ref="profileFormRef"
              :model="profileForm"
              :rules="profileRules"
              label-width="100px"
              v-loading="loading"
            >
              <el-form-item label="使用者名稱" prop="username">
                <el-input v-model="profileForm.username" disabled />
              </el-form-item>
              
              <el-form-item label="名字" prop="firstName">
                <el-input v-model="profileForm.firstName" />
              </el-form-item>
              
              <el-form-item label="姓氏" prop="lastName">
                <el-input v-model="profileForm.lastName" />
              </el-form-item>
              
              <el-form-item label="電子郵件" prop="email">
                <el-input v-model="profileForm.email" />
              </el-form-item>
              
              <el-form-item label="部門">
                <el-input v-model="departmentName" disabled />
              </el-form-item>
              
              <el-form-item label="角色">
                <el-tag 
                  v-for="role in roles" 
                  :key="role" 
                  class="role-tag"
                  :type="getRoleType(role)"
                >
                  {{ getRoleLabel(role) }}
                </el-tag>
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" @click="updateProfile">更新資料</el-button>
              </el-form-item>
            </el-form>
          </el-tab-pane>
          
          <el-tab-pane label="修改密碼" name="password">
            <el-form
              ref="passwordFormRef"
              :model="passwordForm"
              :rules="passwordRules"
              label-width="100px"
              v-loading="loading"
            >
              <el-form-item label="目前密碼" prop="currentPassword">
                <el-input 
                  v-model="passwordForm.currentPassword" 
                  type="password" 
                  show-password 
                />
              </el-form-item>
              
              <el-form-item label="新密碼" prop="newPassword">
                <el-input 
                  v-model="passwordForm.newPassword" 
                  type="password" 
                  show-password 
                />
              </el-form-item>
              
              <el-form-item label="確認密碼" prop="confirmPassword">
                <el-input 
                  v-model="passwordForm.confirmPassword" 
                  type="password" 
                  show-password 
                />
              </el-form-item>
              
              <el-form-item>
                <el-button type="primary" @click="updatePassword">更新密碼</el-button>
              </el-form-item>
            </el-form>
          </el-tab-pane>
          
          <el-tab-pane label="活動記錄" name="activity">
            <el-timeline v-loading="loading">
              <el-timeline-item
                v-for="(activity, index) in activities"
                :key="index"
                :timestamp="formatDate(activity.createdAt)"
                :type="getActivityType(activity.activityType)"
              >
                {{ activity.description }}
              </el-timeline-item>
            </el-timeline>
            
            <div v-if="activities.length === 0" class="no-data">
              <el-empty description="暫無活動記錄" />
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import AppLayout from '../components/layout/AppLayout.vue'
import { useAuthStore } from '../stores/auth'
import api from '../services/api'

const authStore = useAuthStore()
const loading = ref(false)
const activeTab = ref('info')

// User data
const userId = ref(authStore.userId)
const roles = ref(authStore.roles)
const departmentName = ref('')
const activities = ref([])

// Forms
const profileFormRef = ref<FormInstance>()
const passwordFormRef = ref<FormInstance>()

const profileForm = reactive({
  username: '',
  firstName: '',
  lastName: '',
  email: ''
})

const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// Validation rules
const profileRules = reactive<FormRules>({
  firstName: [
    { required: true, message: '請輸入名字', trigger: 'blur' },
    { max: 50, message: '長度不能超過 50 個字元', trigger: 'blur' }
  ],
  lastName: [
    { required: true, message: '請輸入姓氏', trigger: 'blur' },
    { max: 50, message: '長度不能超過 50 個字元', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '請輸入電子郵件', trigger: 'blur' },
    { type: 'email', message: '請輸入有效的電子郵件地址', trigger: 'blur' }
  ]
})

const validateConfirmPassword = (rule: any, value: string, callback: any) => {
  if (value === '') {
    callback(new Error('請再次輸入密碼'))
  } else if (value !== passwordForm.newPassword) {
    callback(new Error('兩次輸入的密碼不一致'))
  } else {
    callback()
  }
}

const passwordRules = reactive<FormRules>({
  currentPassword: [
    { required: true, message: '請輸入目前密碼', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '請輸入新密碼', trigger: 'blur' },
    { min: 6, max: 40, message: '長度必須在 6 到 40 個字元之間', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '請再次輸入密碼', trigger: 'blur' },
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
})

// Fetch user data
onMounted(async () => {
  loading.value = true
  try {
    const response = await api.getUser(userId.value)
    const userData = response.data
    
    profileForm.username = userData.username
    profileForm.firstName = userData.firstName
    profileForm.lastName = userData.lastName
    profileForm.email = userData.email
    
    if (userData.department) {
      departmentName.value = userData.department.name
    }
    
    // Fetch activity logs
    const activityResponse = await api.getUserActivityLogs(userId.value)
    activities.value = activityResponse.data
  } catch (error) {
    console.error('Failed to fetch user data:', error)
    ElMessage.error('獲取用戶資料失敗')
  } finally {
    loading.value = false
  }
})

// Update profile
const updateProfile = async () => {
  if (!profileFormRef.value) return
  
  await profileFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        await api.updateUser(userId.value, {
          firstName: profileForm.firstName,
          lastName: profileForm.lastName,
          email: profileForm.email
        })
        
        ElMessage.success('個人資料更新成功')
      } catch (error) {
        console.error('Failed to update profile:', error)
        ElMessage.error('更新個人資料失敗')
      } finally {
        loading.value = false
      }
    }
  })
}

// Update password
const updatePassword = async () => {
  if (!passwordFormRef.value) return
  
  await passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        await api.updatePassword(userId.value, {
          currentPassword: passwordForm.currentPassword,
          newPassword: passwordForm.newPassword
        })
        
        ElMessage.success('密碼更新成功')
        
        // Clear password form
        passwordForm.currentPassword = ''
        passwordForm.newPassword = ''
        passwordForm.confirmPassword = ''
        
        // Reset form validation
        passwordFormRef.value.resetFields()
      } catch (error) {
        console.error('Failed to update password:', error)
        ElMessage.error('更新密碼失敗，請確認目前密碼是否正確')
      } finally {
        loading.value = false
      }
    }
  })
}

// Helper functions
const getRoleLabel = (role: string) => {
  const roleMap: Record<string, string> = {
    'ROLE_SUPER_ADMIN': '最高管理員',
    'ROLE_ADMIN': '管理員',
    'ROLE_DEPARTMENT_HEAD': '部門主管',
    'ROLE_EMPLOYEE': '員工',
    'ROLE_GUEST': '訪客'
  }
  return roleMap[role] || role
}

const getRoleType = (role: string) => {
  const typeMap: Record<string, string> = {
    'ROLE_SUPER_ADMIN': 'danger',
    'ROLE_ADMIN': 'warning',
    'ROLE_DEPARTMENT_HEAD': 'success',
    'ROLE_EMPLOYEE': 'info',
    'ROLE_GUEST': ''
  }
  return typeMap[role] || ''
}

const getActivityType = (type: string) => {
  const typeMap: Record<string, string> = {
    'LOGIN': 'primary',
    'LOGOUT': 'info',
    'CREATE': 'success',
    'UPDATE': 'warning',
    'DELETE': 'danger',
    'VIEW': 'info'
  }
  return typeMap[type] || 'info'
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-TW')
}
</script>

<style scoped>
.profile-container {
  padding: 20px;
}

.profile-title {
  margin-bottom: 20px;
  font-size: 24px;
  color: #303133;
}

.profile-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.role-tag {
  margin-right: 8px;
}

.no-data {
  margin-top: 20px;
  text-align: center;
}
</style>
