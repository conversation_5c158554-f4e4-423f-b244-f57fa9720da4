<template>
  <AppLayout>
    <div class="task-create-view">
      <div class="page-header">
        <div class="page-title">
          <h1>創建任務</h1>
          <p>創建新的任務並分配給團隊成員</p>
        </div>
        
        <div class="page-actions">
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon> 返回任務列表
          </el-button>
        </div>
      </div>

      <el-card>
        <TaskForm
          @submit-success="handleSubmitSuccess"
          @cancel="goBack"
          submit-button-text="創建任務"
        />
      </el-card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'
import AppLayout from '../components/layout/AppLayout.vue'
import TaskForm from '../components/task/TaskForm.vue'

const router = useRouter()

const handleSubmitSuccess = (task: any) => {
  router.push({ name: 'task-detail', params: { id: task.id } })
}

const goBack = () => {
  router.push({ name: 'tasks' })
}
</script>

<style scoped>
.task-create-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.page-title h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.page-title p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}
</style>
