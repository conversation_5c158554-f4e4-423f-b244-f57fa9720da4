<template>
  <div class="home-container">
    <div class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">專案管理系統</h1>
        <p class="hero-subtitle">高效管理您的專案、任務和團隊</p>
        <div class="hero-buttons">
          <el-button type="primary" size="large" @click="goToDashboard" v-if="isAuthenticated">
            進入系統
          </el-button>
          <el-button type="primary" size="large" @click="goToLogin" v-else>
            立即登入
          </el-button>
        </div>
      </div>
    </div>

    <div class="features-section">
      <h2 class="section-title">系統功能</h2>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :md="8" v-for="(feature, index) in features" :key="index">
          <el-card class="feature-card">
            <el-icon :size="40" :color="feature.color" class="feature-icon">
              <component :is="feature.icon" />
            </el-icon>
            <h3 class="feature-title">{{ feature.title }}</h3>
            <p class="feature-description">{{ feature.description }}</p>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="footer-section">
      <p>© 2024 專案管理系統 - 企業級應用</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { computed } from 'vue'
import {
  Monitor,
  Folder,
  Document,
  User,
  Collection,
  OfficeBuilding,
  Setting,
  DataAnalysis
} from '@element-plus/icons-vue'

const router = useRouter()
const authStore = useAuthStore()

const isAuthenticated = computed(() => authStore.isAuthenticated)

const goToDashboard = () => {
  router.push('/dashboard')
}

const goToLogin = () => {
  router.push('/login')
}

const features = [
  {
    icon: User,
    title: '使用者與權限管理',
    description: '完整的使用者角色與權限管理，確保系統安全性',
    color: '#409EFF'
  },
  {
    icon: OfficeBuilding,
    title: '公司組織管理',
    description: '管理部門結構與員工資訊，建立完整組織架構',
    color: '#67C23A'
  },
  {
    icon: Collection,
    title: '客戶關係管理',
    description: '追蹤客戶資訊、聯絡記錄與商機',
    color: '#E6A23C'
  },
  {
    icon: Folder,
    title: '專案管理',
    description: '全方位專案追蹤，包含進度、預算與資源分配',
    color: '#F56C6C'
  },
  {
    icon: Document,
    title: '任務管理',
    description: '分配任務、追蹤進度與設定優先順序',
    color: '#909399'
  },
  {
    icon: DataAnalysis,
    title: '資源管理',
    description: '有效分配與追蹤人力、材料與設備資源',
    color: '#9254DE'
  }
]
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.hero-section {
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  color: white;
  padding: 80px 20px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-content {
  max-width: 800px;
}

.hero-title {
  font-size: 3rem;
  margin-bottom: 20px;
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 40px;
  opacity: 0.9;
}

.hero-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.features-section {
  padding: 60px 20px;
  background-color: #f5f7fa;
}

.section-title {
  text-align: center;
  margin-bottom: 40px;
  font-size: 2rem;
  color: #303133;
}

.feature-card {
  height: 100%;
  text-align: center;
  padding: 20px;
  transition: transform 0.3s;
  margin-bottom: 20px;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon {
  margin-bottom: 20px;
}

.feature-title {
  font-size: 1.25rem;
  margin-bottom: 10px;
  color: #303133;
}

.feature-description {
  color: #606266;
}

.footer-section {
  background-color: #303133;
  color: white;
  padding: 20px;
  text-align: center;
  margin-top: auto;
}
</style>
