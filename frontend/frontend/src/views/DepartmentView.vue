<template>
  <div class="department-view">
    <h1>部門管理</h1>
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>部門列表</span>
          <el-button type="primary" @click="openAddDepartmentDialog">新增部門</el-button>
        </div>
      </template>
      <el-table :data="departments" style="width: 100%">
        <el-table-column prop="name" label="部門名稱" />
        <el-table-column prop="manager" label="部門主管" />
        <el-table-column prop="employeeCount" label="員工數量" />
        <el-table-column prop="status" label="狀態">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'ACTIVE' ? 'success' : 'danger'">
              {{ scope.row.status === 'ACTIVE' ? '啟用' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button size="small" @click="editDepartment(scope.row)">編輯</el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="toggleDepartmentStatus(scope.row)"
            >
              {{ scope.row.status === 'ACTIVE' ? '停用' : '啟用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// Mock data for demonstration
const departments = ref([
  {
    id: 1,
    name: '管理部',
    manager: '王大明',
    employeeCount: 5,
    status: 'ACTIVE'
  },
  {
    id: 2,
    name: '業務部',
    manager: '李小華',
    employeeCount: 10,
    status: 'ACTIVE'
  },
  {
    id: 3,
    name: '技術部',
    manager: '張小明',
    employeeCount: 15,
    status: 'ACTIVE'
  },
  {
    id: 4,
    name: '人力資源部',
    manager: '陳小美',
    employeeCount: 3,
    status: 'ACTIVE'
  }
])

const openAddDepartmentDialog = () => {
  // Implement add department functionality
  console.log('Open add department dialog')
}

const editDepartment = (department: any) => {
  // Implement edit department functionality
  console.log('Edit department', department)
}

const toggleDepartmentStatus = (department: any) => {
  // Implement toggle department status functionality
  department.status = department.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE'
  console.log('Toggle department status', department)
}

onMounted(() => {
  // In a real application, you would fetch departments from the API
  console.log('DepartmentView component mounted')
})
</script>

<style scoped>
.department-view {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
