<template>
  <AppLayout>
    <div class="department-management-view">
      <div class="page-header">
        <h1>部門管理</h1>
        <p>管理組織架構、部門信息和成員分配</p>
      </div>

      <!-- 統計卡片 -->
      <div class="stats-grid">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><OfficeBuilding /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.totalDepartments }}</div>
              <div class="stat-label">總部門數</div>
            </div>
          </div>
        </el-card>

        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon active">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.activeDepartments }}</div>
              <div class="stat-label">啟用部門</div>
            </div>
          </div>
        </el-card>

        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon employees">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.totalEmployees }}</div>
              <div class="stat-label">總員工數</div>
            </div>
          </div>
        </el-card>

        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon levels">
              <el-icon><Rank /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ statistics.maxLevel }}</div>
              <div class="stat-label">組織層級</div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 操作工具欄 -->
      <el-card class="toolbar-card">
        <div class="toolbar-row">
          <div class="toolbar-left">
            <el-input
              v-model="searchQuery"
              placeholder="搜索部門名稱或描述"
              style="width: 300px"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>

            <el-select
              v-model="selectedStatus"
              placeholder="狀態"
              style="width: 120px"
              clearable
            >
              <el-option label="啟用" :value="true" />
              <el-option label="停用" :value="false" />
            </el-select>

            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>

            <el-button @click="resetFilters">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </div>

          <div class="toolbar-right">
            <el-button type="success" @click="expandAll">
              <el-icon><Plus /></el-icon>
              展開全部
            </el-button>
            <el-button @click="collapseAll">
              <el-icon><Minus /></el-icon>
              收起全部
            </el-button>
            <el-button type="primary" @click="openAddDepartmentDialog">
              <el-icon><Plus /></el-icon>
              新增部門
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 部門樹形表格 -->
      <el-card class="table-card">
        <template #header>
          <div class="table-header">
            <span>組織架構</span>
            <div class="view-toggle">
              <el-radio-group v-model="viewMode" @change="handleViewModeChange">
                <el-radio-button label="tree">樹形視圖</el-radio-button>
                <el-radio-button label="table">表格視圖</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </template>

        <!-- 樹形視圖 -->
        <div v-if="viewMode === 'tree'" class="tree-view">
          <el-tree
            ref="departmentTree"
            :data="departmentTreeData"
            :props="treeProps"
            :expand-on-click-node="false"
            :default-expand-all="false"
            node-key="id"
            class="department-tree"
          >
            <template #default="{ node, data }">
              <div class="tree-node">
                <div class="node-content">
                  <div class="node-info">
                    <el-icon class="node-icon">
                      <OfficeBuilding v-if="data.level === 1" />
                      <Collection v-else />
                    </el-icon>
                    <span class="node-name">{{ data.name }}</span>
                    <el-tag
                      :type="data.active ? 'success' : 'danger'"
                      size="small"
                      class="node-status"
                    >
                      {{ data.active ? '啟用' : '停用' }}
                    </el-tag>
                  </div>
                  <div class="node-meta">
                    <span class="employee-count">
                      <el-icon><User /></el-icon>
                      {{ data.employeeCount || 0 }}人
                    </span>
                    <span v-if="data.manager" class="manager">
                      主管: {{ data.manager.firstName }} {{ data.manager.lastName }}
                    </span>
                  </div>
                </div>
                <div class="node-actions">
                  <el-button size="small" @click="viewDepartment(data)">
                    查看
                  </el-button>
                  <el-button size="small" type="primary" @click="editDepartment(data)">
                    編輯
                  </el-button>
                  <el-button size="small" @click="addSubDepartment(data)">
                    新增子部門
                  </el-button>
                  <el-button
                    size="small"
                    :type="data.active ? 'danger' : 'success'"
                    @click="toggleDepartmentStatus(data)"
                  >
                    {{ data.active ? '停用' : '啟用' }}
                  </el-button>
                </div>
              </div>
            </template>
          </el-tree>
        </div>

        <!-- 表格視圖 -->
        <div v-else class="table-view">
          <el-table
            v-loading="loading"
            :data="filteredDepartments"
            style="width: 100%"
            row-key="id"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            :default-expand-all="false"
          >
            <el-table-column label="部門名稱" min-width="200">
              <template #default="scope">
                <div class="department-name">
                  <el-icon class="dept-icon">
                    <OfficeBuilding v-if="scope.row.level === 1" />
                    <Collection v-else />
                  </el-icon>
                  <span>{{ scope.row.name }}</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="部門主管" width="150">
              <template #default="scope">
                <span v-if="scope.row.manager">
                  {{ scope.row.manager.firstName }} {{ scope.row.manager.lastName }}
                </span>
                <span v-else class="no-manager">未指定</span>
              </template>
            </el-table-column>

            <el-table-column label="員工數量" width="100" align="center">
              <template #default="scope">
                <el-tag type="info" size="small">
                  {{ scope.row.employeeCount || 0 }}人
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column label="層級" width="80" align="center">
              <template #default="scope">
                <el-tag size="small">
                  L{{ scope.row.level || 1 }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column label="狀態" width="100" align="center">
              <template #default="scope">
                <el-tag :type="scope.row.active ? 'success' : 'danger'" size="small">
                  {{ scope.row.active ? '啟用' : '停用' }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column label="創建時間" width="180">
              <template #default="scope">
                {{ formatDate(scope.row.createdAt) }}
              </template>
            </el-table-column>

            <el-table-column label="操作" width="300" fixed="right">
              <template #default="scope">
                <el-button size="small" @click="viewDepartment(scope.row)">
                  查看
                </el-button>
                <el-button size="small" type="primary" @click="editDepartment(scope.row)">
                  編輯
                </el-button>
                <el-button size="small" @click="addSubDepartment(scope.row)">
                  新增子部門
                </el-button>
                <el-button
                  size="small"
                  :type="scope.row.active ? 'danger' : 'success'"
                  @click="toggleDepartmentStatus(scope.row)"
                >
                  {{ scope.row.active ? '停用' : '啟用' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>

      <!-- 新增/編輯部門對話框 -->
      <el-dialog
        v-model="showDepartmentDialog"
        :title="isEditing ? '編輯部門' : '新增部門'"
        width="600px"
        @close="resetDepartmentForm"
      >
        <DepartmentForm
          :department="currentDepartment"
          :is-editing="isEditing"
          :parent-department="parentDepartment"
          @submit="handleDepartmentSubmit"
          @cancel="showDepartmentDialog = false"
        />
      </el-dialog>

      <!-- 部門詳情對話框 -->
      <el-dialog
        v-model="showDetailDialog"
        title="部門詳情"
        width="800px"
      >
        <DepartmentDetail
          v-if="selectedDepartment"
          :department="selectedDepartment"
          @edit="editDepartment"
          @close="showDetailDialog = false"
        />
      </el-dialog>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Minus,
  OfficeBuilding,
  Collection,
  User,
  UserFilled,
  Check,
  Rank
} from '@element-plus/icons-vue'
import AppLayout from '../components/layout/AppLayout.vue'
import DepartmentForm from '../components/department/DepartmentForm.vue'
import DepartmentDetail from '../components/department/DepartmentDetail.vue'
import api from '../services/api'

const router = useRouter()

// 響應式數據
const loading = ref(false)
const departments = ref<any[]>([])
const searchQuery = ref('')
const selectedStatus = ref<boolean | null>(null)
const viewMode = ref('tree')
const showDepartmentDialog = ref(false)
const showDetailDialog = ref(false)
const isEditing = ref(false)
const currentDepartment = ref<any>(null)
const parentDepartment = ref<any>(null)
const selectedDepartment = ref<any>(null)
const departmentTree = ref()

// 統計數據
const statistics = reactive({
  totalDepartments: 0,
  activeDepartments: 0,
  totalEmployees: 0,
  maxLevel: 0
})

// 樹形配置
const treeProps = {
  children: 'children',
  label: 'name'
}

// 計算屬性
const filteredDepartments = computed(() => {
  let filtered = departments.value

  // 搜索過濾
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filterDepartmentsBySearch(filtered, query)
  }

  // 狀態過濾
  if (selectedStatus.value !== null) {
    filtered = filterDepartmentsByStatus(filtered, selectedStatus.value)
  }

  return filtered
})

const departmentTreeData = computed(() => {
  return buildDepartmentTree(filteredDepartments.value)
})

// 方法
const fetchDepartments = async () => {
  loading.value = true
  try {
    const response = await api.getDepartments()
    departments.value = response.data
    updateStatistics()
  } catch (error) {
    console.error('獲取部門列表失敗:', error)
    ElMessage.error('獲取部門列表失敗')
  } finally {
    loading.value = false
  }
}

const updateStatistics = () => {
  statistics.totalDepartments = departments.value.length
  statistics.activeDepartments = departments.value.filter(dept => dept.active).length
  statistics.totalEmployees = departments.value.reduce((sum, dept) => sum + (dept.employeeCount || 0), 0)
  statistics.maxLevel = Math.max(...departments.value.map(dept => dept.level || 1), 1)
}

const buildDepartmentTree = (depts: any[]) => {
  const map = new Map()
  const roots: any[] = []

  // 創建映射
  depts.forEach(dept => {
    map.set(dept.id, { ...dept, children: [] })
  })

  // 構建樹形結構
  depts.forEach(dept => {
    const node = map.get(dept.id)
    if (dept.parentId && map.has(dept.parentId)) {
      map.get(dept.parentId).children.push(node)
    } else {
      roots.push(node)
    }
  })

  return roots
}

const filterDepartmentsBySearch = (depts: any[], query: string): any[] => {
  const result: any[] = []

  const searchInDepartment = (dept: any): boolean => {
    const matches = dept.name.toLowerCase().includes(query) ||
                   (dept.description && dept.description.toLowerCase().includes(query))

    if (matches) {
      result.push(dept)
      return true
    }

    // 搜索子部門
    if (dept.children) {
      const hasMatchingChild = dept.children.some((child: any) => searchInDepartment(child))
      if (hasMatchingChild) {
        result.push(dept)
        return true
      }
    }

    return false
  }

  depts.forEach(dept => searchInDepartment(dept))
  return result
}

const filterDepartmentsByStatus = (depts: any[], status: boolean): any[] => {
  return depts.filter(dept => dept.active === status)
}

const handleSearch = () => {
  // 搜索邏輯已在計算屬性中處理
}

const resetFilters = () => {
  searchQuery.value = ''
  selectedStatus.value = null
}

const handleViewModeChange = () => {
  // 視圖模式切換邏輯
}

const expandAll = () => {
  if (departmentTree.value) {
    const allNodes = departments.value.map(dept => dept.id)
    allNodes.forEach(nodeId => {
      departmentTree.value.setExpanded(nodeId, true)
    })
  }
}

const collapseAll = () => {
  if (departmentTree.value) {
    const allNodes = departments.value.map(dept => dept.id)
    allNodes.forEach(nodeId => {
      departmentTree.value.setExpanded(nodeId, false)
    })
  }
}

const openAddDepartmentDialog = () => {
  isEditing.value = false
  currentDepartment.value = null
  parentDepartment.value = null
  showDepartmentDialog.value = true
}

const addSubDepartment = (parent: any) => {
  isEditing.value = false
  currentDepartment.value = null
  parentDepartment.value = parent
  showDepartmentDialog.value = true
}

const editDepartment = (department: any) => {
  isEditing.value = true
  currentDepartment.value = { ...department }
  parentDepartment.value = null
  showDepartmentDialog.value = true
}

const viewDepartment = (department: any) => {
  selectedDepartment.value = department
  showDetailDialog.value = true
}

const toggleDepartmentStatus = async (department: any) => {
  const action = department.active ? '停用' : '啟用'

  try {
    await ElMessageBox.confirm(
      `確定要${action}部門 "${department.name}" 嗎？`,
      `${action}部門`,
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    if (department.active) {
      await api.deactivateDepartment(department.id)
    } else {
      await api.activateDepartment(department.id)
    }

    ElMessage.success(`部門已${action}`)
    fetchDepartments()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error(`${action}部門失敗:`, error)
      ElMessage.error(`${action}部門失敗`)
    }
  }
}

const handleDepartmentSubmit = async (departmentData: any) => {
  try {
    if (isEditing.value) {
      await api.updateDepartment(currentDepartment.value.id, departmentData)
      ElMessage.success('部門更新成功')
    } else {
      await api.createDepartment(departmentData)
      ElMessage.success('部門創建成功')
    }

    showDepartmentDialog.value = false
    fetchDepartments()
  } catch (error) {
    console.error('保存部門失敗:', error)
    ElMessage.error('保存部門失敗')
  }
}

const resetDepartmentForm = () => {
  currentDepartment.value = null
  parentDepartment.value = null
  isEditing.value = false
}

const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-TW', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

onMounted(() => {
  fetchDepartments()
})
</script>

<style scoped>
.department-management-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

/* 統計卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 8px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.active {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.employees {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.levels {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  font-weight: 500;
}

/* 工具欄 */
.toolbar-card {
  margin-bottom: 20px;
}

.toolbar-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 表格卡片 */
.table-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.view-toggle {
  display: flex;
  align-items: center;
}

/* 樹形視圖 */
.tree-view {
  padding: 16px 0;
}

.department-tree {
  background-color: transparent;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 8px 12px;
  border-radius: 6px;
  margin: 4px 0;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.tree-node:hover {
  background-color: #e9ecef;
}

.node-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.node-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.node-icon {
  color: #409eff;
  font-size: 16px;
}

.node-name {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.node-status {
  margin-left: 8px;
}

.node-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 12px;
  color: #909399;
}

.employee-count {
  display: flex;
  align-items: center;
  gap: 4px;
}

.manager {
  font-style: italic;
}

.node-actions {
  display: flex;
  gap: 4px;
}

/* 表格視圖 */
.table-view {
  padding: 16px 0;
}

.department-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dept-icon {
  color: #409eff;
  font-size: 16px;
}

.no-manager {
  color: #c0c4cc;
  font-style: italic;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .department-management-view {
    padding: 12px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .toolbar-row {
    flex-direction: column;
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }

  .tree-node {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .node-actions {
    justify-content: center;
  }

  .stat-content {
    padding: 12px;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .stat-number {
    font-size: 24px;
  }
}

/* Element Plus 樣式覆蓋 */
:deep(.el-tree) {
  background-color: transparent;
}

:deep(.el-tree-node__content) {
  padding: 0;
  background-color: transparent;
}

:deep(.el-tree-node__content:hover) {
  background-color: transparent;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f8f9fa;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #303133;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table__row:hover > td) {
  background-color: #f5f7fa;
}

:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--small) {
  padding: 5px 11px;
  font-size: 12px;
}

:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 500;
}
</style>
