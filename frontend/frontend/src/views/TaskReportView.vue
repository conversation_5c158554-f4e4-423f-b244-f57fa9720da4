<template>
  <AppLayout>
    <div class="task-report-view">
      <div class="page-header">
        <h1>任務報告管理</h1>
        <p>管理和查看所有任務報告</p>
      </div>

      <!-- 任務報告列表 -->
      <TaskReportList
        v-if="!showForm"
        @create="handleCreate"
        @edit="handleEdit"
      />

      <!-- 任務報告表單 -->
      <div v-else class="form-container">
        <div class="form-header">
          <h2>{{ isEdit ? '編輯任務報告' : '創建任務報告' }}</h2>
        </div>
        
        <TaskReportForm
          :task-report="selectedTaskReport"
          :is-edit="isEdit"
          @success="handleFormSuccess"
          @cancel="handleFormCancel"
        />
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import AppLayout from '../components/layout/AppLayout.vue'
import TaskReportList from '../components/task/TaskReportList.vue'
import TaskReportForm from '../components/task/TaskReportForm.vue'
import type { TaskReport } from '../stores/taskReport'

const showForm = ref(false)
const isEdit = ref(false)
const selectedTaskReport = ref<TaskReport | null>(null)

const handleCreate = () => {
  selectedTaskReport.value = null
  isEdit.value = false
  showForm.value = true
}

const handleEdit = (taskReport: TaskReport) => {
  selectedTaskReport.value = taskReport
  isEdit.value = true
  showForm.value = true
}

const handleFormSuccess = () => {
  showForm.value = false
  selectedTaskReport.value = null
  isEdit.value = false
}

const handleFormCancel = () => {
  showForm.value = false
  selectedTaskReport.value = null
  isEdit.value = false
}
</script>

<style scoped>
.task-report-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.form-container {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.form-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.form-header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}
</style>
