<template>
  <AppLayout>
    <div class="customer-container">
      <h1 class="page-title">客戶管理</h1>
      
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="客戶列表" name="list">
          <CustomerList
            @view-customer="viewCustomer"
            @add-customer="activeTab = 'add'"
            @refresh="refreshData"
          />
        </el-tab-pane>
        
        <el-tab-pane label="新增客戶" name="add">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>新增客戶</span>
              </div>
            </template>
            <CustomerForm
              @submit-success="handleFormSuccess"
              submit-button-text="新增客戶"
            />
          </el-card>
        </el-tab-pane>
        
        <el-tab-pane v-if="selectedCustomerId" :label="customerDetailLabel" name="detail">
          <el-card v-loading="detailLoading">
            <template #header>
              <div class="card-header">
                <span>客戶詳情</span>
                <div class="card-actions">
                  <el-button type="primary" @click="editMode = !editMode">
                    {{ editMode ? '取消編輯' : '編輯' }}
                  </el-button>
                </div>
              </div>
            </template>
            
            <div v-if="!editMode">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="客戶名稱">{{ customerDetail.name }}</el-descriptions-item>
                <el-descriptions-item label="聯絡人">{{ customerDetail.contactPerson || '-' }}</el-descriptions-item>
                <el-descriptions-item label="電話">{{ customerDetail.phone || '-' }}</el-descriptions-item>
                <el-descriptions-item label="電子郵件">{{ customerDetail.email || '-' }}</el-descriptions-item>
                <el-descriptions-item label="地址" :span="2">{{ customerDetail.address || '-' }}</el-descriptions-item>
                <el-descriptions-item label="客戶類型">{{ customerDetail.customerType?.name || '-' }}</el-descriptions-item>
                <el-descriptions-item label="標籤">
                  <el-tag
                    v-for="tag in customerDetail.tags"
                    :key="tag.id"
                    :style="{ backgroundColor: tag.color + '20', marginRight: '5px' }"
                    size="small"
                  >
                    {{ tag.name }}
                  </el-tag>
                  <span v-if="!customerDetail.tags || customerDetail.tags.length === 0">-</span>
                </el-descriptions-item>
              </el-descriptions>
              
              <div class="detail-section">
                <h3>聯絡記錄</h3>
                <el-table :data="contactRecords" style="width: 100%" v-loading="contactsLoading">
                  <el-table-column prop="createdAt" label="日期" width="180">
                    <template #default="scope">
                      {{ formatDate(scope.row.createdAt) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="contactType" label="類型" width="100">
                    <template #default="scope">
                      {{ getContactTypeLabel(scope.row.contactType) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="user.username" label="聯絡人員" width="120" />
                  <el-table-column prop="content" label="內容" min-width="200" />
                </el-table>
                
                <div class="add-contact-form">
                  <h4>新增聯絡記錄</h4>
                  <el-form :model="contactForm" :rules="contactRules" ref="contactFormRef" label-width="80px">
                    <el-form-item label="類型" prop="contactType">
                      <el-select v-model="contactForm.contactType" placeholder="請選擇聯絡類型">
                        <el-option label="電話" value="PHONE" />
                        <el-option label="電子郵件" value="EMAIL" />
                        <el-option label="會議" value="MEETING" />
                        <el-option label="其他" value="OTHER" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="內容" prop="content">
                      <el-input v-model="contactForm.content" type="textarea" :rows="3" placeholder="請輸入聯絡內容" />
                    </el-form-item>
                    <el-form-item>
                      <el-button type="primary" @click="addContactRecord">新增記錄</el-button>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
              
              <div class="detail-section">
                <h3>相關專案</h3>
                <el-table :data="relatedProjects" style="width: 100%" v-loading="projectsLoading">
                  <el-table-column prop="name" label="專案名稱" min-width="150" />
                  <el-table-column prop="status" label="狀態" width="120">
                    <template #default="scope">
                      <el-tag :type="getProjectStatusType(scope.row.status)">
                        {{ getProjectStatusLabel(scope.row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="progress" label="進度" width="180">
                    <template #default="scope">
                      <el-progress :percentage="scope.row.progress" />
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="100" fixed="right">
                    <template #default="scope">
                      <el-button type="primary" size="small" @click="viewProject(scope.row.id)">
                        查看
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
            
            <CustomerForm
              v-else
              :customer-id="selectedCustomerId"
              @submit-success="handleUpdateSuccess"
              submit-button-text="更新客戶"
            />
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import AppLayout from '../components/layout/AppLayout.vue'
import CustomerList from '../components/customer/CustomerList.vue'
import CustomerForm from '../components/customer/CustomerForm.vue'
import api from '../services/api'

const router = useRouter()
const activeTab = ref('list')
const selectedCustomerId = ref<number | null>(null)
const customerDetail = ref<any>({})
const detailLoading = ref(false)
const editMode = ref(false)
const contactRecords = ref([])
const contactsLoading = ref(false)
const relatedProjects = ref([])
const projectsLoading = ref(false)

// Contact form
const contactFormRef = ref<FormInstance>()
const contactForm = ref({
  contactType: 'PHONE',
  content: ''
})
const contactRules = {
  contactType: [
    { required: true, message: '請選擇聯絡類型', trigger: 'change' }
  ],
  content: [
    { required: true, message: '請輸入聯絡內容', trigger: 'blur' },
    { max: 500, message: '長度不能超過 500 個字元', trigger: 'blur' }
  ]
}

// Computed properties
const customerDetailLabel = computed(() => {
  return customerDetail.value.name ? `客戶: ${customerDetail.value.name}` : '客戶詳情'
})

// Handle tab click
const handleTabClick = () => {
  if (activeTab.value === 'list') {
    editMode.value = false
  }
}

// View customer
const viewCustomer = async (id: number) => {
  selectedCustomerId.value = id
  activeTab.value = 'detail'
  await fetchCustomerDetail()
  await Promise.all([
    fetchContactRecords(),
    fetchRelatedProjects()
  ])
}

// Fetch customer detail
const fetchCustomerDetail = async () => {
  if (!selectedCustomerId.value) return
  
  detailLoading.value = true
  try {
    const response = await api.getCustomer(selectedCustomerId.value)
    customerDetail.value = response.data
  } catch (error) {
    console.error('Failed to fetch customer detail:', error)
    ElMessage.error('獲取客戶詳情失敗')
  } finally {
    detailLoading.value = false
  }
}

// Fetch contact records
const fetchContactRecords = async () => {
  if (!selectedCustomerId.value) return
  
  contactsLoading.value = true
  try {
    const response = await api.getCustomerContacts(selectedCustomerId.value)
    contactRecords.value = response.data
  } catch (error) {
    console.error('Failed to fetch contact records:', error)
    ElMessage.error('獲取聯絡記錄失敗')
  } finally {
    contactsLoading.value = false
  }
}

// Fetch related projects
const fetchRelatedProjects = async () => {
  if (!selectedCustomerId.value) return
  
  projectsLoading.value = true
  try {
    const response = await api.getCustomerProjects(selectedCustomerId.value)
    relatedProjects.value = response.data
  } catch (error) {
    console.error('Failed to fetch related projects:', error)
    ElMessage.error('獲取相關專案失敗')
  } finally {
    projectsLoading.value = false
  }
}

// Add contact record
const addContactRecord = async () => {
  if (!contactFormRef.value || !selectedCustomerId.value) return
  
  await contactFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        await api.createContactRecord({
          customerId: selectedCustomerId.value,
          contactType: contactForm.value.contactType,
          content: contactForm.value.content
        })
        
        ElMessage.success('聯絡記錄已新增')
        contactForm.value.content = ''
        await fetchContactRecords()
      } catch (error) {
        console.error('Failed to add contact record:', error)
        ElMessage.error('新增聯絡記錄失敗')
      }
    }
  })
}

// Handle form success
const handleFormSuccess = (customer: any) => {
  activeTab.value = 'list'
  refreshData()
}

// Handle update success
const handleUpdateSuccess = (customer: any) => {
  editMode.value = false
  customerDetail.value = customer
  refreshData()
}

// Refresh data
const refreshData = () => {
  if (selectedCustomerId.value) {
    fetchCustomerDetail()
  }
}

// View project
const viewProject = (id: number) => {
  router.push(`/projects/${id}`)
}

// Helper functions
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-TW')
}

const getContactTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    'PHONE': '電話',
    'EMAIL': '電子郵件',
    'MEETING': '會議',
    'OTHER': '其他'
  }
  return typeMap[type] || type
}

const getProjectStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    'PLANNING': '規劃中',
    'IN_PROGRESS': '執行中',
    'COMPLETED': '已完成',
    'DELAYED': '延期',
    'CLOSED': '結案'
  }
  return statusMap[status] || status
}

const getProjectStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'PLANNING': 'info',
    'IN_PROGRESS': 'primary',
    'COMPLETED': 'success',
    'DELAYED': 'danger',
    'CLOSED': 'info'
  }
  return typeMap[status] || 'info'
}

onMounted(() => {
  // Check if there's an ID in the route query
  const id = router.currentRoute.value.query.id
  if (id) {
    viewCustomer(Number(id))
  }
})
</script>

<style scoped>
.customer-container {
  padding: 20px;
}

.page-title {
  margin-bottom: 20px;
  font-size: 24px;
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-section {
  margin-top: 30px;
}

.add-contact-form {
  margin-top: 20px;
  max-width: 600px;
}
</style>
