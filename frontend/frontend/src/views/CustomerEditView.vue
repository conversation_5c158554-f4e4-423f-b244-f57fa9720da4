<template>
  <AppLayout>
    <div class="customer-edit-view">
      <div class="page-header">
        <div class="page-title">
          <h1>編輯客戶</h1>
          <p>修改客戶信息和設置</p>
        </div>
        
        <div class="page-actions">
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon> 返回客戶詳情
          </el-button>
        </div>
      </div>

      <el-card v-loading="loading">
        <CustomerForm
          :customer-id="customerId"
          @submit-success="handleSubmitSuccess"
          @cancel="goBack"
          submit-button-text="更新客戶"
        />
      </el-card>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'
import AppLayout from '../components/layout/AppLayout.vue'
import CustomerForm from '../components/customer/CustomerForm.vue'

const router = useRouter()
const route = useRoute()

const loading = ref(false)

const customerId = computed(() => Number(route.params.id))

const handleSubmitSuccess = (customer: any) => {
  router.push({ name: 'customer-detail', params: { id: customer.id } })
}

const goBack = () => {
  router.push({ name: 'customer-detail', params: { id: customerId.value } })
}
</script>

<style scoped>
.customer-edit-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.page-title h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.page-title p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}
</style>
