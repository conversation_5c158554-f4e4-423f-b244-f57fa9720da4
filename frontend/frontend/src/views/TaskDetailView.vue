<template>
  <AppLayout>
    <div class="task-detail-view" v-loading="loading">
      <div class="page-header">
        <div class="page-title">
          <h1>{{ task.name || '任務詳情' }}</h1>
          <p>查看和管理任務的詳細信息</p>
        </div>

        <div class="page-actions">
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon> 返回任務列表
          </el-button>
          <el-button type="primary" @click="editTask">
            <el-icon><Edit /></el-icon> 編輯任務
          </el-button>
        </div>
      </div>

      <div class="task-content" v-if="task.id">
        <el-row :gutter="20">
          <el-col :span="16">
            <el-card class="box-card">
              <template #header>
                <div class="card-header">
                  <span>任務詳情</span>
                  <el-tag :type="getStatusType(task.status)">{{ getStatusLabel(task.status) }}</el-tag>
                </div>
              </template>
              <div class="task-info">
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="任務名稱" :span="2">{{ task.name }}</el-descriptions-item>
                  <el-descriptions-item label="描述" :span="2">{{ task.description || '-' }}</el-descriptions-item>
                  <el-descriptions-item label="所屬專案">{{ task.project?.name || '-' }}</el-descriptions-item>
                  <el-descriptions-item label="負責人">
                    {{ task.assignee ? `${task.assignee.firstName} ${task.assignee.lastName}` : '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="開始日期">{{ formatDate(task.startDate) }}</el-descriptions-item>
                  <el-descriptions-item label="截止日期">{{ formatDate(task.dueDate) }}</el-descriptions-item>
                  <el-descriptions-item label="優先度">
                    <el-tag :type="getPriorityType(task.priority)">{{ getPriorityLabel(task.priority) }}</el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="狀態">
                    <el-tag :type="getStatusType(task.status)">{{ getStatusLabel(task.status) }}</el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="進度" :span="2">
                    <el-progress :percentage="task.progress || 0" :stroke-width="12" />
                  </el-descriptions-item>
                </el-descriptions>

                <div class="task-actions">
                  <el-button-group>
                    <el-button
                      type="primary"
                      @click="updateTaskStatus('IN_PROGRESS')"
                      :disabled="task.status === 'IN_PROGRESS'"
                      :loading="statusLoading"
                    >
                      開始任務
                    </el-button>
                    <el-button
                      type="success"
                      @click="updateTaskStatus('COMPLETED')"
                      :disabled="task.status === 'COMPLETED'"
                      :loading="statusLoading"
                    >
                      完成任務
                    </el-button>
                    <el-button
                      type="warning"
                      @click="updateTaskStatus('NOT_STARTED')"
                      :disabled="task.status === 'NOT_STARTED'"
                      :loading="statusLoading"
                    >
                      重置為未開始
                    </el-button>
                    <el-button
                      type="danger"
                      @click="updateTaskStatus('DELAYED')"
                      :disabled="task.status === 'DELAYED'"
                      :loading="statusLoading"
                    >
                      標記為延期
                    </el-button>
                  </el-button-group>
                </div>
              </div>
            </el-card>

            <el-card class="box-card subtasks-card">
              <template #header>
                <div class="card-header">
                  <span>子任務 ({{ subtasks.length }})</span>
                  <el-button type="primary" size="small" @click="addSubtask">新增子任務</el-button>
                </div>
              </template>
              <el-table :data="subtasks" style="width: 100%" v-loading="subtasksLoading">
                <el-table-column prop="name" label="名稱" min-width="150" />
                <el-table-column label="負責人" width="120">
                  <template #default="scope">
                    {{ scope.row.assignee ? `${scope.row.assignee.firstName} ${scope.row.assignee.lastName}` : '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="狀態" width="100">
                  <template #default="scope">
                    <el-tag :type="getStatusType(scope.row.status)">{{ getStatusLabel(scope.row.status) }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="progress" label="進度" width="120">
                  <template #default="scope">
                    <el-progress :percentage="scope.row.progress || 0" :stroke-width="6" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100">
                  <template #default="scope">
                    <el-button size="small" type="primary" @click="viewSubtask(scope.row)">查看</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div v-if="subtasks.length === 0" class="empty-state">
                <el-empty description="暫無子任務" />
              </div>
            </el-card>
          </el-col>

          <el-col :span="8">
            <el-card class="box-card">
              <template #header>
                <div class="card-header">
                  <span>任務文檔 ({{ documents.length }})</span>
                  <el-button type="primary" size="small" @click="uploadDocument">上傳文檔</el-button>
                </div>
              </template>
              <div v-loading="documentsLoading">
                <div v-for="document in documents" :key="document.id" class="document-item">
                  <div class="document-info">
                    <el-icon><Document /></el-icon>
                    <span class="document-name">{{ document.fileName }}</span>
                  </div>
                  <div class="document-actions">
                    <el-button size="small" type="primary" @click="downloadDocument(document)">下載</el-button>
                  </div>
                </div>
                <div v-if="documents.length === 0" class="empty-state">
                  <el-empty description="暫無文檔" />
                </div>
              </div>
            </el-card>

            <el-card class="box-card reports-card">
              <template #header>
                <div class="card-header">
                  <span>任務報告 ({{ reports.length }})</span>
                  <el-button type="primary" size="small" @click="addReport">新增報告</el-button>
                </div>
              </template>
              <div v-loading="reportsLoading">
                <div v-for="report in reports" :key="report.id" class="report-item">
                  <div class="report-header">
                    <span class="report-date">{{ formatDate(report.reportDate) }}</span>
                    <span class="report-progress">進度: {{ report.progressUpdate }}%</span>
                  </div>
                  <div class="report-content">{{ report.content }}</div>
                  <div v-if="report.issues" class="report-issues">
                    <strong>問題:</strong> {{ report.issues }}
                  </div>
                </div>
                <div v-if="reports.length === 0" class="empty-state">
                  <el-empty description="暫無報告" />
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Edit, Document } from '@element-plus/icons-vue'
import AppLayout from '../components/layout/AppLayout.vue'
import api from '../services/api'

const route = useRoute()
const router = useRouter()

// Data
const task = ref({})
const subtasks = ref([])
const documents = ref([])
const reports = ref([])

// Loading states
const loading = ref(false)
const statusLoading = ref(false)
const subtasksLoading = ref(false)
const documentsLoading = ref(false)
const reportsLoading = ref(false)

// Computed
const taskId = computed(() => Number(route.params.id))

// Methods
const fetchTask = async () => {
  loading.value = true
  try {
    const response = await api.getTask(taskId.value)
    task.value = response.data
  } catch (error) {
    console.error('Failed to fetch task:', error)
    ElMessage.error('獲取任務詳情失敗')
  } finally {
    loading.value = false
  }
}

const fetchSubtasks = async () => {
  subtasksLoading.value = true
  try {
    const response = await api.getSubtasks(taskId.value)
    subtasks.value = response.data
  } catch (error) {
    console.error('Failed to fetch subtasks:', error)
  } finally {
    subtasksLoading.value = false
  }
}

const fetchDocuments = async () => {
  documentsLoading.value = true
  try {
    const response = await api.getTaskDocuments(taskId.value)
    documents.value = response.data
  } catch (error) {
    console.error('Failed to fetch documents:', error)
  } finally {
    documentsLoading.value = false
  }
}

const fetchReports = async () => {
  reportsLoading.value = true
  try {
    const response = await api.getTaskReportsByTaskId(taskId.value)
    reports.value = response.data
  } catch (error) {
    console.error('Failed to fetch reports:', error)
  } finally {
    reportsLoading.value = false
  }
}

const getPriorityType = (priority: string) => {
  switch (priority) {
    case 'HIGH':
    case 'URGENT':
      return 'danger'
    case 'MEDIUM':
      return 'warning'
    case 'LOW':
      return 'info'
    default:
      return ''
  }
}

const getPriorityLabel = (priority: string) => {
  const labels = {
    'LOW': '低',
    'MEDIUM': '中',
    'HIGH': '高',
    'URGENT': '緊急'
  }
  return labels[priority] || priority
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'NOT_STARTED':
      return 'info'
    case 'IN_PROGRESS':
      return 'warning'
    case 'COMPLETED':
      return 'success'
    case 'DELAYED':
      return 'danger'
    default:
      return ''
  }
}

const getStatusLabel = (status: string) => {
  const labels = {
    'NOT_STARTED': '未開始',
    'IN_PROGRESS': '進行中',
    'COMPLETED': '已完成',
    'DELAYED': '延期'
  }
  return labels[status] || status
}

const formatDate = (date: string) => {
  if (!date) return '-'
  return new Date(date).toLocaleDateString('zh-TW')
}

const goBack = () => {
  router.push({ name: 'tasks' })
}

const editTask = () => {
  router.push({ name: 'task-edit', params: { id: taskId.value } })
}

const updateTaskStatus = async (status: string) => {
  statusLoading.value = true
  try {
    await api.updateTaskStatus(taskId.value, status)
    task.value.status = status
    if (status === 'COMPLETED') {
      task.value.progress = 100
    }
    ElMessage.success('任務狀態更新成功')
  } catch (error) {
    console.error('Failed to update task status:', error)
    ElMessage.error('更新任務狀態失敗')
  } finally {
    statusLoading.value = false
  }
}

const addSubtask = () => {
  router.push({
    name: 'task-create',
    query: { parentId: taskId.value }
  })
}

const viewSubtask = (subtask: any) => {
  router.push({ name: 'task-detail', params: { id: subtask.id } })
}

const uploadDocument = () => {
  ElMessage.info('文檔上傳功能將在後續版本中實現')
}

const downloadDocument = (document: any) => {
  ElMessage.info('文檔下載功能將在後續版本中實現')
}

const addReport = () => {
  router.push({ name: 'task-reports' })
}

onMounted(() => {
  fetchTask()
  fetchSubtasks()
  fetchDocuments()
  fetchReports()
})
</script>

<style scoped>
.task-detail-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.page-title h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.page-title p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.task-content {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-actions {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.subtasks-card {
  margin-top: 20px;
}

.reports-card {
  margin-top: 20px;
}

.empty-state {
  padding: 20px;
  text-align: center;
}

.document-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
}

.document-item:last-child {
  border-bottom: none;
}

.document-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.document-name {
  font-size: 14px;
  color: #303133;
}

.report-item {
  margin-bottom: 15px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #409eff;
}

.report-item:last-child {
  margin-bottom: 0;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.report-date {
  font-size: 12px;
  color: #909399;
}

.report-progress {
  font-size: 12px;
  color: #409eff;
  font-weight: bold;
}

.report-content {
  margin-bottom: 8px;
  line-height: 1.5;
  color: #303133;
}

.report-issues {
  font-size: 12px;
  color: #e6a23c;
}
</style>
