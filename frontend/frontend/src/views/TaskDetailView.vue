<template>
  <div class="task-detail-view">
    <el-page-header @back="goBack" :title="task.name">
      <template #extra>
        <el-button type="primary" @click="editTask">編輯任務</el-button>
      </template>
    </el-page-header>

    <div class="task-content">
      <el-row :gutter="20">
        <el-col :span="16">
          <el-card class="box-card">
            <template #header>
              <div class="card-header">
                <span>任務詳情</span>
                <el-tag :type="getStatusType(task.status)">{{ task.status }}</el-tag>
              </div>
            </template>
            <div class="task-info">
              <p><strong>描述：</strong>{{ task.description }}</p>
              <p><strong>所屬專案：</strong>{{ task.project }}</p>
              <p><strong>負責人：</strong>{{ task.assignee }}</p>
              <p><strong>開始日期：</strong>{{ task.startDate }}</p>
              <p><strong>截止日期：</strong>{{ task.dueDate }}</p>
              <p><strong>優先度：</strong>
                <el-tag :type="getPriorityType(task.priority)">{{ task.priority }}</el-tag>
              </p>
              <p><strong>進度：</strong></p>
              <el-progress :percentage="task.progress" />
              
              <div class="task-actions">
                <el-button-group>
                  <el-button type="primary" @click="updateTaskStatus('進行中')" :disabled="task.status === '進行中'">開始任務</el-button>
                  <el-button type="success" @click="updateTaskStatus('已完成')" :disabled="task.status === '已完成'">完成任務</el-button>
                  <el-button type="danger" @click="updateTaskStatus('已延期')" :disabled="task.status === '已延期'">標記延期</el-button>
                </el-button-group>
              </div>
            </div>
          </el-card>

          <el-card class="box-card subtasks-card">
            <template #header>
              <div class="card-header">
                <span>子任務</span>
                <el-button type="primary" size="small" @click="addSubtask">新增子任務</el-button>
              </div>
            </template>
            <el-table :data="subtasks" style="width: 100%">
              <el-table-column prop="name" label="名稱" />
              <el-table-column prop="assignee" label="負責人" />
              <el-table-column prop="status" label="狀態">
                <template #default="scope">
                  <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="progress" label="進度">
                <template #default="scope">
                  <el-progress :percentage="scope.row.progress" />
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="scope">
                  <el-button size="small" type="primary" @click="editSubtask(scope.row)">編輯</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>

        <el-col :span="8">
          <el-card class="box-card">
            <template #header>
              <div class="card-header">
                <span>任務回報</span>
                <el-button type="primary" size="small" @click="addComment">新增回報</el-button>
              </div>
            </template>
            <div v-for="comment in comments" :key="comment.id" class="comment">
              <div class="comment-header">
                <span class="comment-author">{{ comment.author }}</span>
                <span class="comment-date">{{ comment.date }}</span>
              </div>
              <div class="comment-content">{{ comment.content }}</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

// Mock data for demonstration
const task = ref({
  id: route.params.id,
  name: '前端開發',
  description: '開發專案管理系統的前端界面，包括使用者介面設計和功能實現。',
  project: '專案管理系統開發',
  assignee: '張小明',
  startDate: '2025-05-01',
  dueDate: '2025-06-15',
  priority: '高',
  status: '進行中',
  progress: 50
})

const subtasks = ref([
  {
    id: 1,
    name: '登入頁面開發',
    assignee: '張小明',
    status: '已完成',
    progress: 100
  },
  {
    id: 2,
    name: '儀表板開發',
    assignee: '張小明',
    status: '進行中',
    progress: 70
  },
  {
    id: 3,
    name: '任務管理頁面開發',
    assignee: '張小明',
    status: '待處理',
    progress: 0
  }
])

const comments = ref([
  {
    id: 1,
    author: '張小明',
    date: '2025-05-10 14:30',
    content: '已完成登入頁面的開發，正在進行儀表板頁面的開發。'
  },
  {
    id: 2,
    author: '王大明',
    date: '2025-05-11 09:15',
    content: '儀表板的設計看起來不錯，繼續加油！'
  },
  {
    id: 3,
    author: '張小明',
    date: '2025-05-15 16:45',
    content: '儀表板頁面已完成70%，預計明天可以完成。'
  }
])

const getPriorityType = (priority: string) => {
  switch (priority) {
    case '高':
      return 'danger'
    case '中':
      return 'warning'
    case '低':
      return 'info'
    default:
      return ''
  }
}

const getStatusType = (status: string) => {
  switch (status) {
    case '待處理':
      return 'info'
    case '進行中':
      return 'warning'
    case '已完成':
      return 'success'
    case '已延期':
      return 'danger'
    default:
      return ''
  }
}

const goBack = () => {
  router.push({ name: 'tasks' })
}

const editTask = () => {
  // Implement edit task functionality
  console.log('Edit task', task.value)
}

const updateTaskStatus = (status: string) => {
  task.value.status = status
  if (status === '已完成') {
    task.value.progress = 100
  }
  console.log('Update task status', status)
}

const addSubtask = () => {
  // Implement add subtask functionality
  console.log('Add subtask')
}

const editSubtask = (subtask: any) => {
  // Implement edit subtask functionality
  console.log('Edit subtask', subtask)
}

const addComment = () => {
  // Implement add comment functionality
  console.log('Add comment')
}

onMounted(() => {
  // In a real application, you would fetch task details from the API
  console.log('TaskDetailView component mounted, task ID:', route.params.id)
})
</script>

<style scoped>
.task-detail-view {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.task-content {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-info {
  margin-bottom: 20px;
}

.task-actions {
  margin-top: 20px;
}

.subtasks-card {
  margin-top: 20px;
}

.comment {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.comment:last-child {
  border-bottom: none;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.comment-author {
  font-weight: bold;
}

.comment-date {
  color: #909399;
  font-size: 0.9em;
}

.comment-content {
  white-space: pre-line;
}
</style>
