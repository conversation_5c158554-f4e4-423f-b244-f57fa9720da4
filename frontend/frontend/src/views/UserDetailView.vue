<template>
  <AppLayout>
    <div class="user-detail-view">
      <div class="page-header">
        <div class="header-left">
          <el-button @click="goBack" type="text">
            <el-icon><ArrowLeft /></el-icon>
            返回用戶列表
          </el-button>
          <h1>{{ user?.fullName || '用戶詳情' }}</h1>
        </div>
        <div class="header-actions">
          <el-button type="warning" @click="editUser">
            <el-icon><Edit /></el-icon>
            編輯用戶
          </el-button>
          <el-button
            :type="user?.enabled ? 'danger' : 'success'"
            @click="toggleUserStatus"
          >
            {{ user?.enabled ? '停用用戶' : '啟用用戶' }}
          </el-button>
        </div>
      </div>

      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <div v-else-if="user" class="user-content">
        <!-- 基本信息 -->
        <el-card class="info-card">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
            </div>
          </template>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>用戶名：</label>
                <span>{{ user.username }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>姓名：</label>
                <span>{{ user.fullName }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>電子郵件：</label>
                <span>{{ user.email }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>狀態：</label>
                <el-tag :type="user.enabled ? 'success' : 'danger'">
                  {{ user.enabled ? '啟用' : '停用' }}
                </el-tag>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <div class="info-item">
                <label>部門：</label>
                <span>{{ user.department?.name || '無部門' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>創建時間：</label>
                <span>{{ formatDate(user.createdAt) }}</span>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <div class="info-item">
                <label>角色：</label>
                <el-tag
                  v-for="role in user.roles"
                  :key="role.id"
                  :type="getRoleTagType(role.name)"
                  style="margin-right: 8px;"
                >
                  {{ role.displayName }}
                </el-tag>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 統計信息 -->
        <el-card class="stats-card">
          <template #header>
            <div class="card-header">
              <span>統計信息</span>
            </div>
          </template>

          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ user.managedProjectsCount || 0 }}</div>
                <div class="stat-label">管理專案</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ user.memberProjectsCount || 0 }}</div>
                <div class="stat-label">參與專案</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ user.assignedTasksCount || 0 }}</div>
                <div class="stat-label">指派任務</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ user.activityLogsCount || 0 }}</div>
                <div class="stat-label">活動記錄</div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20" v-if="user.lastLoginAt">
            <el-col :span="24">
              <div class="info-item">
                <label>最後活動時間：</label>
                <span>{{ formatDate(user.lastLoginAt) }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 活動日誌 -->
        <el-card class="activity-card">
          <template #header>
            <div class="card-header">
              <span>最近活動</span>
              <el-button type="primary" size="small" @click="viewAllActivityLogs">
                查看全部
              </el-button>
            </div>
          </template>

          <el-table
            :data="activityLogs"
            v-loading="activityLoading"
            stripe
            style="width: 100%"
          >
            <el-table-column prop="activityType" label="活動類型" width="120">
              <template #default="{ row }">
                <el-tag :type="getActivityTypeTag(row.activityType)">
                  {{ getActivityTypeLabel(row.activityType) }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="description" label="描述" min-width="200" />

            <el-table-column prop="ipAddress" label="IP地址" width="120" />

            <el-table-column prop="createdAt" label="時間" width="180">
              <template #default="{ row }">
                {{ formatDate(row.createdAt) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>

      <div v-else class="error-container">
        <el-result
          icon="error"
          title="用戶不存在"
          sub-title="請檢查用戶ID是否正確"
        >
          <template #extra>
            <el-button type="primary" @click="goBack">返回用戶列表</el-button>
          </template>
        </el-result>
      </div>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Edit } from '@element-plus/icons-vue'
import AppLayout from '../components/layout/AppLayout.vue'
import api from '../services/api'

const router = useRouter()
const route = useRoute()

// Data
const user = ref<any>(null)
const activityLogs = ref<any[]>([])
const loading = ref(false)
const activityLoading = ref(false)

// Methods
const fetchUser = async () => {
  const id = Number(route.params.id)
  if (!id) return

  loading.value = true
  try {
    const response = await api.getUser(id)
    user.value = response.data
  } catch (error) {
    console.error('獲取用戶詳情失敗:', error)
    ElMessage.error('獲取用戶詳情失敗')
  } finally {
    loading.value = false
  }
}

const fetchActivityLogs = async () => {
  const id = Number(route.params.id)
  if (!id) return

  activityLoading.value = true
  try {
    const response = await api.getUserActivityLogs(id)
    // 只顯示最近10條記錄
    activityLogs.value = response.data.slice(0, 10)
  } catch (error) {
    console.error('獲取活動日誌失敗:', error)
    ElMessage.error('獲取活動日誌失敗')
  } finally {
    activityLoading.value = false
  }
}

const goBack = () => {
  router.push({ name: 'users' })
}

const editUser = () => {
  router.push({ name: 'user-edit', params: { id: route.params.id } })
}

const toggleUserStatus = async () => {
  if (!user.value) return

  const action = user.value.enabled ? '停用' : '啟用'

  try {
    await ElMessageBox.confirm(
      `確定要${action}用戶 "${user.value.fullName}" 嗎？`,
      `${action}用戶`,
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const id = Number(route.params.id)
    if (user.value.enabled) {
      await api.disableUser(id)
    } else {
      await api.enableUser(id)
    }

    ElMessage.success(`用戶已${action}`)
    fetchUser()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error(`${action}用戶失敗:`, error)
      ElMessage.error(`${action}用戶失敗`)
    }
  }
}

const viewAllActivityLogs = () => {
  router.push({ name: 'activity-logs', query: { userId: route.params.id } })
}

const getRoleTagType = (roleName: string) => {
  const types: Record<string, string> = {
    'ROLE_SUPER_ADMIN': 'danger',
    'ROLE_ADMIN': 'warning',
    'ROLE_DEPARTMENT_HEAD': 'success',
    'ROLE_EMPLOYEE': 'info',
    'ROLE_GUEST': ''
  }
  return types[roleName] || ''
}

const getActivityTypeTag = (type: string) => {
  const types: Record<string, string> = {
    'LOGIN': 'success',
    'LOGOUT': 'info',
    'CREATE': 'primary',
    'UPDATE': 'warning',
    'DELETE': 'danger'
  }
  return types[type] || ''
}

const getActivityTypeLabel = (type: string) => {
  const labels: Record<string, string> = {
    'LOGIN': '登入',
    'LOGOUT': '登出',
    'CREATE': '創建',
    'UPDATE': '更新',
    'DELETE': '刪除'
  }
  return labels[type] || type
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-TW')
}

onMounted(() => {
  fetchUser()
  fetchActivityLogs()
})
</script>

<style scoped>
.user-detail-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.header-left h1 {
  margin: 10px 0 0 0;
  color: #303133;
}

.loading-container,
.error-container {
  padding: 40px;
  text-align: center;
}

.user-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-card,
.stats-card,
.activity-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item {
  margin-bottom: 15px;
}

.info-item label {
  font-weight: 600;
  color: #606266;
  margin-right: 10px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-actions {
    margin-top: 10px;
  }
}
</style>
