<template>
  <div class="resource-availability-view">
    <div class="page-header">
      <h1>資源可用性管理</h1>
      <p>檢查資源可用性，監控資源使用情況，管理資源短缺通知</p>
    </div>

    <el-tabs v-model="activeTab" type="border-card">
      <!-- 資源可用性檢查 -->
      <el-tab-pane label="可用性檢查" name="availability">
        <ResourceAvailabilityChecker />
      </el-tab-pane>

      <!-- 專案資源檢查 -->
      <el-tab-pane label="專案資源檢查" name="project-check">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>專案資源需求檢查</span>
              <el-button type="primary" @click="showProjectSelector = true">
                檢查專案
              </el-button>
            </div>
          </template>

          <div v-if="projectCheckResults.length > 0">
            <el-alert
              v-for="result in projectCheckResults"
              :key="result.projectId"
              :title="`專案: ${result.projectName}`"
              :type="result.hasResourceShortages ? 'error' : 'success'"
              :description="result.hasResourceShortages ? `發現 ${result.unavailableResources.length} 個資源短缺` : '所有資源充足'"
              show-icon
              style="margin-bottom: 15px;"
            >
              <template #default>
                <div v-if="result.hasResourceShortages">
                  <el-divider content-position="left">短缺資源詳情</el-divider>
                  <el-table :data="result.unavailableResources" size="small">
                    <el-table-column label="資源名稱" prop="resource.name" />
                    <el-table-column label="資源類型">
                      <template #default="{ row }">
                        <el-tag :type="getResourceTypeTag(row.resource.type)">
                          {{ getResourceTypeLabel(row.resource.type) }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column label="可用數量" prop="availabilityResult.availableQuantity" />
                    <el-table-column label="短缺原因" prop="availabilityResult.message" />
                    <el-table-column label="操作">
                      <template #default="{ row }">
                        <el-button size="small" type="warning" @click="sendShortageNotification(result, row)">
                          發送通知
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </template>
            </el-alert>
          </div>

          <el-empty v-else description="請選擇專案進行資源檢查" />
        </el-card>
      </el-tab-pane>

      <!-- 任務資源檢查 -->
      <el-tab-pane label="任務資源檢查" name="task-check">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>任務資源需求檢查</span>
              <el-button type="primary" @click="showTaskSelector = true">
                檢查任務
              </el-button>
            </div>
          </template>

          <div v-if="taskCheckResults.length > 0">
            <el-alert
              v-for="result in taskCheckResults"
              :key="result.taskId"
              :title="`任務: ${result.taskName}`"
              :type="result.hasResourceShortages ? 'error' : 'success'"
              :description="result.hasResourceShortages ? `發現 ${result.unavailableResources.length} 個資源短缺` : '所有資源充足'"
              show-icon
              style="margin-bottom: 15px;"
            />
          </div>

          <el-empty v-else description="請選擇任務進行資源檢查" />
        </el-card>
      </el-tab-pane>

      <!-- 資源使用統計 -->
      <el-tab-pane label="使用統計" name="statistics">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>資源使用統計</span>
              <el-button type="primary" @click="generateStatistics">
                生成統計
              </el-button>
            </div>
          </template>

          <el-form :model="statisticsForm" :inline="true" style="margin-bottom: 20px;">
            <el-form-item label="資源">
              <el-select v-model="statisticsForm.resourceId" placeholder="選擇資源" style="width: 200px;">
                <el-option
                  v-for="resource in resources"
                  :key="resource.id"
                  :label="`${resource.name} (${getResourceTypeLabel(resource.type)})`"
                  :value="resource.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="開始日期">
              <el-date-picker
                v-model="statisticsForm.startDate"
                type="date"
                placeholder="選擇開始日期"
              />
            </el-form-item>
            <el-form-item label="結束日期">
              <el-date-picker
                v-model="statisticsForm.endDate"
                type="date"
                placeholder="選擇結束日期"
              />
            </el-form-item>
          </el-form>

          <div v-if="usageStatistics">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-statistic title="資源名稱" :value="usageStatistics.resourceName" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="總分配次數" :value="usageStatistics.totalAllocations" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="總使用數量" :value="usageStatistics.totalQuantityUsed" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="使用率" :value="usageStatistics.utilizationRate" suffix="%" />
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <!-- 專案選擇對話框 -->
    <el-dialog v-model="showProjectSelector" title="選擇專案" width="600px">
      <el-table
        :data="projects"
        @selection-change="handleProjectSelection"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="專案名稱" prop="name" />
        <el-table-column label="狀態" prop="status" />
        <el-table-column label="負責人" prop="manager.name" />
      </el-table>
      <template #footer>
        <el-button @click="showProjectSelector = false">取消</el-button>
        <el-button type="primary" @click="checkSelectedProjects" :loading="checkingProjects">
          檢查選中專案
        </el-button>
      </template>
    </el-dialog>

    <!-- 任務選擇對話框 -->
    <el-dialog v-model="showTaskSelector" title="選擇任務" width="600px">
      <el-table
        :data="tasks"
        @selection-change="handleTaskSelection"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="任務名稱" prop="name" />
        <el-table-column label="狀態" prop="status" />
        <el-table-column label="負責人" prop="assignee.name" />
      </el-table>
      <template #footer>
        <el-button @click="showTaskSelector = false">取消</el-button>
        <el-button type="primary" @click="checkSelectedTasks" :loading="checkingTasks">
          檢查選中任務
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import ResourceAvailabilityChecker from '@/components/resource/ResourceAvailabilityChecker.vue'
import api from '@/services/api'

const activeTab = ref('availability')
const showProjectSelector = ref(false)
const showTaskSelector = ref(false)
const checkingProjects = ref(false)
const checkingTasks = ref(false)

const resources = ref([])
const projects = ref([])
const tasks = ref([])
const selectedProjects = ref([])
const selectedTasks = ref([])
const projectCheckResults = ref([])
const taskCheckResults = ref([])
const usageStatistics = ref(null)

const statisticsForm = reactive({
  resourceId: '',
  startDate: '',
  endDate: ''
})

const getResourceTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    'HUMAN': '人力',
    'MATERIAL': '材料',
    'EQUIPMENT': '設備'
  }
  return typeMap[type] || type
}

const getResourceTypeTag = (type: string) => {
  const tagMap: Record<string, string> = {
    'HUMAN': 'success',
    'MATERIAL': 'warning',
    'EQUIPMENT': 'primary'
  }
  return tagMap[type] || 'info'
}

const fetchData = async () => {
  try {
    const [resourcesRes, projectsRes, tasksRes] = await Promise.all([
      api.getResources(),
      api.getProjects(),
      api.getTasks()
    ])
    
    resources.value = resourcesRes.data
    projects.value = projectsRes.data
    tasks.value = tasksRes.data
  } catch (error) {
    ElMessage.error('獲取數據失敗')
  }
}

const handleProjectSelection = (selection: any[]) => {
  selectedProjects.value = selection
}

const handleTaskSelection = (selection: any[]) => {
  selectedTasks.value = selection
}

const checkSelectedProjects = async () => {
  if (selectedProjects.value.length === 0) {
    ElMessage.warning('請選擇至少一個專案')
    return
  }

  checkingProjects.value = true
  try {
    const projectIds = selectedProjects.value.map(p => p.id)
    const response = await api.batchCheckProjectsResourceRequirements(projectIds)
    projectCheckResults.value = response.data
    showProjectSelector.value = false
    ElMessage.success('專案資源檢查完成')
  } catch (error) {
    ElMessage.error('檢查失敗')
  } finally {
    checkingProjects.value = false
  }
}

const checkSelectedTasks = async () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('請選擇至少一個任務')
    return
  }

  checkingTasks.value = true
  try {
    const taskIds = selectedTasks.value.map(t => t.id)
    const response = await api.batchCheckTasksResourceRequirements(taskIds)
    taskCheckResults.value = response.data
    showTaskSelector.value = false
    ElMessage.success('任務資源檢查完成')
  } catch (error) {
    ElMessage.error('檢查失敗')
  } finally {
    checkingTasks.value = false
  }
}

const generateStatistics = async () => {
  if (!statisticsForm.resourceId || !statisticsForm.startDate || !statisticsForm.endDate) {
    ElMessage.warning('請填寫完整的統計條件')
    return
  }

  try {
    const response = await api.getResourceUsageStatistics(
      statisticsForm.resourceId,
      statisticsForm.startDate,
      statisticsForm.endDate
    )
    usageStatistics.value = response.data
    ElMessage.success('統計生成完成')
  } catch (error) {
    ElMessage.error('生成統計失敗')
  }
}

const sendShortageNotification = async (projectResult: any, resourceShortage: any) => {
  try {
    await ElMessageBox.confirm(
      `確定要發送資源短缺通知嗎？\n專案：${projectResult.projectName}\n資源：${resourceShortage.resource.name}`,
      '發送通知',
      {
        confirmButtonText: '確定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 這裡應該調用發送通知的API
    ElMessage.success('通知已發送給專案負責人和採購人員')
  } catch {
    // 用戶取消
  }
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.resource-availability-view {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #666;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
