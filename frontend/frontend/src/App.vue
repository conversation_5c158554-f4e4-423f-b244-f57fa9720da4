<script setup lang="ts">
import { RouterView } from 'vue-router'
import { ElConfigProvider } from 'element-plus'
import zhTW from 'element-plus/dist/locale/zh-tw.mjs'
</script>

<template>
  <el-config-provider :locale="zhTW">
    <RouterView />
  </el-config-provider>
</template>

<style>
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang TC', 'Microsoft JhengHei', 'Microsoft YaHei', Arial, sans-serif;
}

#app {
  height: 100vh;
}
</style>
