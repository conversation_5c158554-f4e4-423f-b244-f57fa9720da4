#!/bin/bash

# 批量更新 API 端點，為所有非認證端點添加 /api/ 前綴

API_FILE="/Users/<USER>/jsbvmx/frontend/frontend/src/services/api.ts"

# 備份原文件
cp "$API_FILE" "$API_FILE.backup"

# 更新所有端點（除了已經更新的 users, files, departments）
sed -i '' "s|return apiClient\.get('customers|return apiClient.get('api/customers|g" "$API_FILE"
sed -i '' "s|return apiClient\.post('customers|return apiClient.post('api/customers|g" "$API_FILE"
sed -i '' "s|return apiClient\.put('customers|return apiClient.put('api/customers|g" "$API_FILE"
sed -i '' "s|return apiClient\.delete('customers|return apiClient.delete('api/customers|g" "$API_FILE"

sed -i '' "s|return apiClient\.get('projects|return apiClient.get('api/projects|g" "$API_FILE"
sed -i '' "s|return apiClient\.post('projects|return apiClient.post('api/projects|g" "$API_FILE"
sed -i '' "s|return apiClient\.put('projects|return apiClient.put('api/projects|g" "$API_FILE"
sed -i '' "s|return apiClient\.delete('projects|return apiClient.delete('api/projects|g" "$API_FILE"

sed -i '' "s|return apiClient\.get('tasks|return apiClient.get('api/tasks|g" "$API_FILE"
sed -i '' "s|return apiClient\.post('tasks|return apiClient.post('api/tasks|g" "$API_FILE"
sed -i '' "s|return apiClient\.put('tasks|return apiClient.put('api/tasks|g" "$API_FILE"
sed -i '' "s|return apiClient\.delete('tasks|return apiClient.delete('api/tasks|g" "$API_FILE"

sed -i '' "s|return apiClient\.get('resources|return apiClient.get('api/resources|g" "$API_FILE"
sed -i '' "s|return apiClient\.post('resources|return apiClient.post('api/resources|g" "$API_FILE"
sed -i '' "s|return apiClient\.put('resources|return apiClient.put('api/resources|g" "$API_FILE"
sed -i '' "s|return apiClient\.delete('resources|return apiClient.delete('api/resources|g" "$API_FILE"

sed -i '' "s|return apiClient\.get('contracts|return apiClient.get('api/contracts|g" "$API_FILE"
sed -i '' "s|return apiClient\.post('contracts|return apiClient.post('api/contracts|g" "$API_FILE"
sed -i '' "s|return apiClient\.put('contracts|return apiClient.put('api/contracts|g" "$API_FILE"
sed -i '' "s|return apiClient\.delete('contracts|return apiClient.delete('api/contracts|g" "$API_FILE"

sed -i '' "s|return apiClient\.get('notifications|return apiClient.get('api/notifications|g" "$API_FILE"
sed -i '' "s|return apiClient\.post('notifications|return apiClient.post('api/notifications|g" "$API_FILE"
sed -i '' "s|return apiClient\.put('notifications|return apiClient.put('api/notifications|g" "$API_FILE"
sed -i '' "s|return apiClient\.delete('notifications|return apiClient.delete('api/notifications|g" "$API_FILE"

sed -i '' "s|return apiClient\.get('settings|return apiClient.get('api/settings|g" "$API_FILE"
sed -i '' "s|return apiClient\.post('settings|return apiClient.post('api/settings|g" "$API_FILE"
sed -i '' "s|return apiClient\.put('settings|return apiClient.put('api/settings|g" "$API_FILE"
sed -i '' "s|return apiClient\.delete('settings|return apiClient.delete('api/settings|g" "$API_FILE"

echo "API 端點更新完成！"
