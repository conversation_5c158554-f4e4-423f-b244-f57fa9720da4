-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- 主機： localhost:8889
-- 產生時間： 2025 年 05 月 24 日 09:47
-- 伺服器版本： 5.7.39
-- PHP 版本： 8.2.0

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 資料庫： `project_management`
--

-- --------------------------------------------------------

--
-- 資料表結構 `activity_logs`
--

CREATE TABLE `activity_logs` (
  `id` bigint(20) NOT NULL,
  `activity_type` enum('LOGIN','LOGOUT','CREATE','UPDATE','DELETE','VIEW') DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `ip_address` varchar(255) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- 資料表結構 `contact_records`
--

CREATE TABLE `contact_records` (
  `id` bigint(20) NOT NULL,
  `contact_type` enum('PHONE','EMAIL','MEETING','OTHER') DEFAULT NULL,
  `content` varchar(255) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `customer_id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- 資料表結構 `customers`
--

CREATE TABLE `customers` (
  `id` bigint(20) NOT NULL,
  `address` varchar(255) DEFAULT NULL,
  `contact_person` varchar(255) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `customer_type_id` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- 傾印資料表的資料 `customers`
--

INSERT INTO `customers` (`id`, `address`, `contact_person`, `created_at`, `email`, `name`, `phone`, `updated_at`, `customer_type_id`) VALUES
(2, '測試地址', '測試聯繫人', '2025-05-19 05:06:17.599750', '<EMAIL>', '測試客戶', '1234567890', '2025-05-19 05:06:17.599750', 1);

-- --------------------------------------------------------

--
-- 資料表結構 `customer_tags`
--

CREATE TABLE `customer_tags` (
  `customer_id` bigint(20) NOT NULL,
  `tag_id` bigint(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- 資料表結構 `customer_types`
--

CREATE TABLE `customer_types` (
  `id` bigint(20) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- 傾印資料表的資料 `customer_types`
--

INSERT INTO `customer_types` (`id`, `description`, `name`) VALUES
(1, '企業客戶類型', '企業客戶');

-- --------------------------------------------------------

--
-- 資料表結構 `departments`
--

CREATE TABLE `departments` (
  `id` bigint(20) NOT NULL,
  `active` bit(1) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `parent_id` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- 資料表結構 `documents`
--

CREATE TABLE `documents` (
  `id` bigint(20) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `file_path` varchar(255) DEFAULT NULL,
  `file_type` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `version` varchar(255) DEFAULT NULL,
  `project_id` bigint(20) DEFAULT NULL,
  `task_id` bigint(20) DEFAULT NULL,
  `uploaded_by` bigint(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- 資料表結構 `notifications`
--

CREATE TABLE `notifications` (
  `id` bigint(20) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `link` varchar(255) DEFAULT NULL,
  `message` varchar(255) DEFAULT NULL,
  `is_read` bit(1) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `type` enum('TASK_ASSIGNED','TASK_DUE_SOON','TASK_OVERDUE','PROJECT_UPDATE','SYSTEM') DEFAULT NULL,
  `user_id` bigint(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- 傾印資料表的資料 `notifications`
--

INSERT INTO `notifications` (`id`, `created_at`, `link`, `message`, `is_read`, `title`, `type`, `user_id`) VALUES
(1, '2025-05-19 04:59:07.071387', NULL, '這是一個測試通知', b'1', '測試通知', 'SYSTEM', 1),
(2, '2025-05-20 08:09:59.363146', '/tasks/2', '任務 測試任務 將於 2025-05-20 到期', b'0', '任務即將到期', 'TASK_DUE_SOON', 1),
(3, '2025-05-20 22:33:11.087292', '/tasks/2', '任務 測試任務 已逾期', b'0', '任務已逾期', 'TASK_OVERDUE', 1),
(4, '2025-05-20 22:33:11.093255', '/tasks/2', '任務 測試任務 已逾期，負責人: Super Admin', b'0', '任務逾期通知', 'TASK_OVERDUE', 1);

-- --------------------------------------------------------

--
-- 資料表結構 `projects`
--

CREATE TABLE `projects` (
  `id` bigint(20) NOT NULL,
  `budget` decimal(38,2) DEFAULT NULL,
  `cost` decimal(38,2) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `progress` int(11) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `status` enum('PLANNING','IN_PROGRESS','COMPLETED','DELAYED','CLOSED') DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `customer_id` bigint(20) DEFAULT NULL,
  `manager_id` bigint(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- 傾印資料表的資料 `projects`
--

INSERT INTO `projects` (`id`, `budget`, `cost`, `created_at`, `description`, `end_date`, `name`, `progress`, `start_date`, `status`, `updated_at`, `customer_id`, `manager_id`) VALUES
(1, NULL, '0.00', '2025-05-19 04:29:42.886657', '這是一個測試項目', '2025-06-19', '測試項目', 0, '2025-05-19', 'PLANNING', '2025-05-19 04:29:42.886657', NULL, 1),
(2, NULL, '0.00', '2025-05-19 04:31:07.353653', '這是一個測試項目', '2025-06-19', '測試項目', 0, '2025-05-19', 'PLANNING', '2025-05-19 04:31:07.353653', NULL, 1),
(3, NULL, '0.00', '2025-05-19 04:52:24.534459', '這是一個新的測試項目', '2025-07-01', '新測試項目', 0, '2025-06-01', 'PLANNING', '2025-05-19 04:52:24.534459', NULL, 1),
(4, NULL, '0.00', '2025-05-19 05:23:18.645181', '測試項目描述', '2025-06-19', '測試項目', 0, '2025-05-19', 'PLANNING', '2025-05-19 05:23:18.645181', NULL, 1);

-- --------------------------------------------------------

--
-- 資料表結構 `project_members`
--

CREATE TABLE `project_members` (
  `project_id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- 資料表結構 `resources`
--

CREATE TABLE `resources` (
  `id` bigint(20) NOT NULL,
  `available` bit(1) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `type` enum('HUMAN','MATERIAL','EQUIPMENT') DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- 傾印資料表的資料 `resources`
--

INSERT INTO `resources` (`id`, `available`, `created_at`, `description`, `name`, `type`, `updated_at`) VALUES
(1, b'1', '2025-05-19 05:23:03.050389', '大型會議室', '會議室A', 'EQUIPMENT', '2025-05-19 05:23:03.050389');

-- --------------------------------------------------------

--
-- 資料表結構 `resource_allocations`
--

CREATE TABLE `resource_allocations` (
  `id` bigint(20) NOT NULL,
  `cost` decimal(38,2) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `end_date` date DEFAULT NULL,
  `quantity` decimal(38,2) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `project_id` bigint(20) NOT NULL,
  `resource_id` bigint(20) NOT NULL,
  `task_id` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- 傾印資料表的資料 `resource_allocations`
--

INSERT INTO `resource_allocations` (`id`, `cost`, `created_at`, `end_date`, `quantity`, `start_date`, `project_id`, `resource_id`, `task_id`) VALUES
(1, '1000.00', '2025-05-19 05:24:46.686237', '2025-05-20', '1.00', '2025-05-19', 4, 1, NULL);

-- --------------------------------------------------------

--
-- 資料表結構 `roles`
--

CREATE TABLE `roles` (
  `id` int(11) NOT NULL,
  `name` enum('ROLE_SUPER_ADMIN','ROLE_ADMIN','ROLE_DEPARTMENT_HEAD','ROLE_EMPLOYEE','ROLE_GUEST') DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- 傾印資料表的資料 `roles`
--

INSERT INTO `roles` (`id`, `name`) VALUES
(1, 'ROLE_SUPER_ADMIN'),
(2, 'ROLE_ADMIN'),
(3, 'ROLE_DEPARTMENT_HEAD'),
(4, 'ROLE_EMPLOYEE'),
(5, 'ROLE_GUEST');

-- --------------------------------------------------------

--
-- 資料表結構 `settings`
--

CREATE TABLE `settings` (
  `id` bigint(20) NOT NULL,
  `category` varchar(255) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `is_system` bit(1) NOT NULL,
  `setting_key` varchar(255) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `value` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- 傾印資料表的資料 `settings`
--

INSERT INTO `settings` (`id`, `category`, `created_at`, `description`, `is_system`, `setting_key`, `updated_at`, `value`) VALUES
(1, 'system', '2025-05-19 05:20:40.651572', '系統名稱', b'0', 'system.name', '2025-05-19 05:20:40.651572', '項目管理系統');

-- --------------------------------------------------------

--
-- 資料表結構 `tags`
--

CREATE TABLE `tags` (
  `id` bigint(20) NOT NULL,
  `color` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- 傾印資料表的資料 `tags`
--

INSERT INTO `tags` (`id`, `color`, `name`) VALUES
(1, '#FF0000', '重要');

-- --------------------------------------------------------

--
-- 資料表結構 `tasks`
--

CREATE TABLE `tasks` (
  `id` bigint(20) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `due_date` date DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `priority` enum('LOW','MEDIUM','HIGH','URGENT') DEFAULT NULL,
  `progress` int(11) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `status` enum('NOT_STARTED','IN_PROGRESS','COMPLETED','DELAYED') DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `assignee_id` bigint(20) DEFAULT NULL,
  `parent_id` bigint(20) DEFAULT NULL,
  `project_id` bigint(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- 傾印資料表的資料 `tasks`
--

INSERT INTO `tasks` (`id`, `created_at`, `description`, `due_date`, `name`, `priority`, `progress`, `start_date`, `status`, `updated_at`, `assignee_id`, `parent_id`, `project_id`) VALUES
(1, '2025-05-19 04:54:30.350265', '這是一個測試任務', '2025-06-15', '測試任務', 'MEDIUM', 0, '2025-06-01', 'NOT_STARTED', '2025-05-19 04:54:30.350265', 1, NULL, 3),
(2, '2025-05-19 05:25:30.158775', '測試任務描述', '2025-05-20', '測試任務', 'HIGH', 50, '2025-05-19', 'DELAYED', '2025-05-23 02:18:38.393844', 1, NULL, 4);

-- --------------------------------------------------------

--
-- 資料表結構 `task_dependencies`
--

CREATE TABLE `task_dependencies` (
  `task_id` bigint(20) NOT NULL,
  `dependency_id` bigint(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- 資料表結構 `task_reports`
--

CREATE TABLE `task_reports` (
  `id` bigint(20) NOT NULL,
  `content` varchar(255) DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `issues` varchar(255) DEFAULT NULL,
  `progress_update` int(11) DEFAULT NULL,
  `task_id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- 傾印資料表的資料 `task_reports`
--

INSERT INTO `task_reports` (`id`, `content`, `created_at`, `issues`, `progress_update`, `task_id`, `user_id`) VALUES
(1, '測試任務報告內容', '2025-05-23 02:20:36.013578', '無重大問題', 50, 2, 1),
(2, '測試任務報告內容', '2025-05-23 02:21:06.270637', '無重大問題', 50, 2, 1);

-- --------------------------------------------------------

--
-- 資料表結構 `users`
--

CREATE TABLE `users` (
  `id` bigint(20) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `enabled` bit(1) NOT NULL,
  `first_name` varchar(255) DEFAULT NULL,
  `last_name` varchar(255) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `updated_at` datetime(6) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `department_id` bigint(20) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- 傾印資料表的資料 `users`
--

INSERT INTO `users` (`id`, `created_at`, `email`, `enabled`, `first_name`, `last_name`, `password`, `updated_at`, `username`, `department_id`) VALUES
(1, '2025-05-19 04:20:02.751506', '<EMAIL>', b'1', 'Super', 'Admin', '$2a$10$g644Hj6YmYAr08i6db9KaOx0K7MM1qyp8YSlWnJgYrF/6CV7v4gza', '2025-05-19 04:20:02.751506', 'superadmin', NULL),
(2, '2025-05-23 02:31:26.236442', '<EMAIL>', b'1', 'Admin', 'User', '$2a$10$UXOBu3Ip6kzCxekZSvr9bOMmQ.B1mA0j/bruu8FP.JDGN4AGuunVG', '2025-05-23 02:31:26.236442', 'admin', NULL),
(3, '2025-05-23 02:34:26.612709', '<EMAIL>', b'1', 'Super', 'Admin', '$2a$10$YUyGhkFeRpqtc2C171M9SeyXNpkOAwGdfFJacHEYPyXIOKJxqP2rC', '2025-05-23 02:34:26.612709', 'superadmin2', NULL),
(4, '2025-05-23 02:36:25.238226', '<EMAIL>', b'1', 'Super', 'Admin', '$2a$10$Z.mN9280Jwu205jtQiikFul7SRM1FQRrdweMg5rcU.5isQxdGwvJO', '2025-05-23 02:36:25.238226', 'superadmin3', NULL);

-- --------------------------------------------------------

--
-- 資料表結構 `user_roles`
--

CREATE TABLE `user_roles` (
  `user_id` bigint(20) NOT NULL,
  `role_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- 傾印資料表的資料 `user_roles`
--

INSERT INTO `user_roles` (`user_id`, `role_id`) VALUES
(1, 1),
(4, 1),
(2, 4),
(3, 4);

--
-- 已傾印資料表的索引
--

--
-- 資料表索引 `activity_logs`
--
ALTER TABLE `activity_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `FK5bm1lt4f4eevt8lv2517soakd` (`user_id`);

--
-- 資料表索引 `contact_records`
--
ALTER TABLE `contact_records`
  ADD PRIMARY KEY (`id`),
  ADD KEY `FKrubfpcq60x91npp8ia29gt1rb` (`customer_id`),
  ADD KEY `FKba0eaxjw039ixb2r9j9h0btdi` (`user_id`);

--
-- 資料表索引 `customers`
--
ALTER TABLE `customers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `FK7bc1ibnx6won796rbh92v19qq` (`customer_type_id`);

--
-- 資料表索引 `customer_tags`
--
ALTER TABLE `customer_tags`
  ADD PRIMARY KEY (`customer_id`,`tag_id`),
  ADD KEY `FKn6n1iennaqbyvvfqq4ixl29nx` (`tag_id`);

--
-- 資料表索引 `customer_types`
--
ALTER TABLE `customer_types`
  ADD PRIMARY KEY (`id`);

--
-- 資料表索引 `departments`
--
ALTER TABLE `departments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `FK63q917a0aq92i7gcw6h7f1jrv` (`parent_id`);

--
-- 資料表索引 `documents`
--
ALTER TABLE `documents`
  ADD PRIMARY KEY (`id`),
  ADD KEY `FKn3yorpjknmi88ikdmdrrkrp0g` (`project_id`),
  ADD KEY `FKresrevkw1mitw6qid52w7lwrk` (`task_id`),
  ADD KEY `FK1ugacya4ssi0ilf8a9tjycgs6` (`uploaded_by`);

--
-- 資料表索引 `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `FK9y21adhxn0ayjhfocscqox7bh` (`user_id`);

--
-- 資料表索引 `projects`
--
ALTER TABLE `projects`
  ADD PRIMARY KEY (`id`),
  ADD KEY `FK4rpwuljjwr5rygq9gwx36q8cj` (`customer_id`),
  ADD KEY `FKurl8wb4qjly2c5xwdcpetuxs` (`manager_id`);

--
-- 資料表索引 `project_members`
--
ALTER TABLE `project_members`
  ADD PRIMARY KEY (`project_id`,`user_id`),
  ADD KEY `FKgul2el0qjk5lsvig3wgajwm77` (`user_id`);

--
-- 資料表索引 `resources`
--
ALTER TABLE `resources`
  ADD PRIMARY KEY (`id`);

--
-- 資料表索引 `resource_allocations`
--
ALTER TABLE `resource_allocations`
  ADD PRIMARY KEY (`id`),
  ADD KEY `FK91tespeeicmqnkx1wn6gkimbo` (`project_id`),
  ADD KEY `FK7bduy4niye6nvgxynch3o8hm0` (`resource_id`),
  ADD KEY `FKargstl5ggxyocf1jifx4w7xys` (`task_id`);

--
-- 資料表索引 `roles`
--
ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`);

--
-- 資料表索引 `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `UK_swd05dvj4ukvw5q135bpbbfae` (`setting_key`);

--
-- 資料表索引 `tags`
--
ALTER TABLE `tags`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `UK_t48xdq560gs3gap9g7jg36kgc` (`name`);

--
-- 資料表索引 `tasks`
--
ALTER TABLE `tasks`
  ADD PRIMARY KEY (`id`),
  ADD KEY `FKekr1dgiqktpyoip3qmp6lxsit` (`assignee_id`),
  ADD KEY `FKmhda52toac2weso6474wmldtn` (`parent_id`),
  ADD KEY `FKsfhn82y57i3k9uxww1s007acc` (`project_id`);

--
-- 資料表索引 `task_dependencies`
--
ALTER TABLE `task_dependencies`
  ADD PRIMARY KEY (`task_id`,`dependency_id`),
  ADD KEY `FKnwfxktx9emcwsylqofad5cxbx` (`dependency_id`);

--
-- 資料表索引 `task_reports`
--
ALTER TABLE `task_reports`
  ADD PRIMARY KEY (`id`),
  ADD KEY `FK201f9jpgyjyssusbnhci13y2b` (`task_id`),
  ADD KEY `FK27fm3isrujj12ldlr009rsqe9` (`user_id`);

--
-- 資料表索引 `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `UKr43af9ap4edm43mmtq01oddj6` (`username`),
  ADD UNIQUE KEY `UK6dotkott2kjsp8vw4d0m25fb7` (`email`),
  ADD KEY `FKsbg59w8q63i0oo53rlgvlcnjq` (`department_id`);

--
-- 資料表索引 `user_roles`
--
ALTER TABLE `user_roles`
  ADD PRIMARY KEY (`user_id`,`role_id`),
  ADD KEY `FKh8ciramu9cc9q3qcqiv4ue8a6` (`role_id`);

--
-- 在傾印的資料表使用自動遞增(AUTO_INCREMENT)
--

--
-- 使用資料表自動遞增(AUTO_INCREMENT) `activity_logs`
--
ALTER TABLE `activity_logs`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- 使用資料表自動遞增(AUTO_INCREMENT) `contact_records`
--
ALTER TABLE `contact_records`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- 使用資料表自動遞增(AUTO_INCREMENT) `customers`
--
ALTER TABLE `customers`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- 使用資料表自動遞增(AUTO_INCREMENT) `customer_types`
--
ALTER TABLE `customer_types`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- 使用資料表自動遞增(AUTO_INCREMENT) `departments`
--
ALTER TABLE `departments`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- 使用資料表自動遞增(AUTO_INCREMENT) `documents`
--
ALTER TABLE `documents`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT;

--
-- 使用資料表自動遞增(AUTO_INCREMENT) `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- 使用資料表自動遞增(AUTO_INCREMENT) `projects`
--
ALTER TABLE `projects`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- 使用資料表自動遞增(AUTO_INCREMENT) `resources`
--
ALTER TABLE `resources`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- 使用資料表自動遞增(AUTO_INCREMENT) `resource_allocations`
--
ALTER TABLE `resource_allocations`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- 使用資料表自動遞增(AUTO_INCREMENT) `roles`
--
ALTER TABLE `roles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- 使用資料表自動遞增(AUTO_INCREMENT) `settings`
--
ALTER TABLE `settings`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- 使用資料表自動遞增(AUTO_INCREMENT) `tags`
--
ALTER TABLE `tags`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- 使用資料表自動遞增(AUTO_INCREMENT) `tasks`
--
ALTER TABLE `tasks`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- 使用資料表自動遞增(AUTO_INCREMENT) `task_reports`
--
ALTER TABLE `task_reports`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- 使用資料表自動遞增(AUTO_INCREMENT) `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- 已傾印資料表的限制式
--

--
-- 資料表的限制式 `activity_logs`
--
ALTER TABLE `activity_logs`
  ADD CONSTRAINT `FK5bm1lt4f4eevt8lv2517soakd` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- 資料表的限制式 `contact_records`
--
ALTER TABLE `contact_records`
  ADD CONSTRAINT `FKba0eaxjw039ixb2r9j9h0btdi` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `FKrubfpcq60x91npp8ia29gt1rb` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`);

--
-- 資料表的限制式 `customers`
--
ALTER TABLE `customers`
  ADD CONSTRAINT `FK7bc1ibnx6won796rbh92v19qq` FOREIGN KEY (`customer_type_id`) REFERENCES `customer_types` (`id`);

--
-- 資料表的限制式 `customer_tags`
--
ALTER TABLE `customer_tags`
  ADD CONSTRAINT `FK5xsoqn8hsr5gp8h8pcvu6iosr` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`),
  ADD CONSTRAINT `FKn6n1iennaqbyvvfqq4ixl29nx` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`id`);

--
-- 資料表的限制式 `departments`
--
ALTER TABLE `departments`
  ADD CONSTRAINT `FK63q917a0aq92i7gcw6h7f1jrv` FOREIGN KEY (`parent_id`) REFERENCES `departments` (`id`);

--
-- 資料表的限制式 `documents`
--
ALTER TABLE `documents`
  ADD CONSTRAINT `FK1ugacya4ssi0ilf8a9tjycgs6` FOREIGN KEY (`uploaded_by`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `FKn3yorpjknmi88ikdmdrrkrp0g` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`),
  ADD CONSTRAINT `FKresrevkw1mitw6qid52w7lwrk` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`);

--
-- 資料表的限制式 `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `FK9y21adhxn0ayjhfocscqox7bh` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- 資料表的限制式 `projects`
--
ALTER TABLE `projects`
  ADD CONSTRAINT `FK4rpwuljjwr5rygq9gwx36q8cj` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`),
  ADD CONSTRAINT `FKurl8wb4qjly2c5xwdcpetuxs` FOREIGN KEY (`manager_id`) REFERENCES `users` (`id`);

--
-- 資料表的限制式 `project_members`
--
ALTER TABLE `project_members`
  ADD CONSTRAINT `FKdki1sp2homqsdcvqm9yrix31g` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`),
  ADD CONSTRAINT `FKgul2el0qjk5lsvig3wgajwm77` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- 資料表的限制式 `resource_allocations`
--
ALTER TABLE `resource_allocations`
  ADD CONSTRAINT `FK7bduy4niye6nvgxynch3o8hm0` FOREIGN KEY (`resource_id`) REFERENCES `resources` (`id`),
  ADD CONSTRAINT `FK91tespeeicmqnkx1wn6gkimbo` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`),
  ADD CONSTRAINT `FKargstl5ggxyocf1jifx4w7xys` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`);

--
-- 資料表的限制式 `tasks`
--
ALTER TABLE `tasks`
  ADD CONSTRAINT `FKekr1dgiqktpyoip3qmp6lxsit` FOREIGN KEY (`assignee_id`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `FKmhda52toac2weso6474wmldtn` FOREIGN KEY (`parent_id`) REFERENCES `tasks` (`id`),
  ADD CONSTRAINT `FKsfhn82y57i3k9uxww1s007acc` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`);

--
-- 資料表的限制式 `task_dependencies`
--
ALTER TABLE `task_dependencies`
  ADD CONSTRAINT `FKerlktvi2bud6uauih348u0loj` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`),
  ADD CONSTRAINT `FKnwfxktx9emcwsylqofad5cxbx` FOREIGN KEY (`dependency_id`) REFERENCES `tasks` (`id`);

--
-- 資料表的限制式 `task_reports`
--
ALTER TABLE `task_reports`
  ADD CONSTRAINT `FK201f9jpgyjyssusbnhci13y2b` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`),
  ADD CONSTRAINT `FK27fm3isrujj12ldlr009rsqe9` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- 資料表的限制式 `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `FKsbg59w8q63i0oo53rlgvlcnjq` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`);

--
-- 資料表的限制式 `user_roles`
--
ALTER TABLE `user_roles`
  ADD CONSTRAINT `FKh8ciramu9cc9q3qcqiv4ue8a6` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`),
  ADD CONSTRAINT `FKhfh9dx7w3ubf1co1vdev94g3f` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
