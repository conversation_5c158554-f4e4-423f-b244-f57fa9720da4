/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/repository/TaskReportRepository.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/model/Project.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/dto/ProjectDTO.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/controller/ResourceAllocationController.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/model/Role.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/security/jwt/AuthTokenFilter.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/service/ScheduledTaskService.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/controller/UserController.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/exception/ErrorDetails.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/repository/RoleRepository.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/dto/ResourceDTO.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/controller/DepartmentController.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/dto/TaskDTO.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/repository/ContactRecordRepository.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/controller/AuthController.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/controller/ActivityLogController.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/model/Task.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/controller/CustomerTypeController.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/model/ActivityLog.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/security/jwt/AuthEntryPointJwt.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/dto/ActivityLogDTO.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/controller/DocumentController.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/controller/ProjectController.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/repository/ResourceAllocationRepository.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/security/ActivityLogSecurity.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/controller/CustomerController.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/security/services/UserDetailsServiceImpl.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/model/Notification.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/repository/DocumentRepository.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/controller/NotificationController.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/repository/CustomerRepository.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/controller/DashboardController.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/controller/TagController.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/model/Customer.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/model/User.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/repository/CustomerTypeRepository.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/exception/ResourceNotFoundException.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/repository/SettingsRepository.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/model/Resource.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/controller/ContactRecordController.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/controller/SettingsController.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/exception/GlobalExceptionHandler.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/model/Settings.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/repository/NotificationRepository.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/security/TaskSecurity.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/controller/TaskReportController.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/model/ContactRecord.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/model/ERole.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/repository/TaskRepository.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/model/Tag.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/security/UserSecurity.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/security/ProjectSecurity.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/payload/response/JwtResponse.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/service/ProjectService.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/model/Document.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/repository/ActivityLogRepository.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/repository/DepartmentRepository.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/payload/request/SignupRequest.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/model/CustomerType.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/payload/request/LoginRequest.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/controller/ResourceController.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/model/ResourceAllocation.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/service/NotificationService.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/service/ValidationService.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/dto/ResourceAllocationDTO.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/repository/ResourceRepository.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/exception/ValidationException.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/security/WebSecurityConfig.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/repository/TagRepository.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/BackendApplication.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/model/Department.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/dto/CustomerDTO.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/payload/response/MessageResponse.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/security/NotificationSecurity.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/security/services/UserDetailsImpl.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/service/DocumentService.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/dto/DocumentDTO.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/security/jwt/JwtUtils.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/controller/TaskController.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/model/TaskReport.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/service/TaskService.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/security/DocumentSecurity.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/config/DatabaseInitializer.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/repository/ProjectRepository.java
/Users/<USER>/jsbvmx/backend/src/main/java/com/projectmgmt/backend/repository/UserRepository.java
