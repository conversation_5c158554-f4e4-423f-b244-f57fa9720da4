package com.projectmgmt.backend.service;

import com.projectmgmt.backend.exception.ResourceNotFoundException;
import com.projectmgmt.backend.model.ActivityLog;
import com.projectmgmt.backend.model.Project;
import com.projectmgmt.backend.model.Task;
import com.projectmgmt.backend.model.User;
import com.projectmgmt.backend.repository.ActivityLogRepository;
import com.projectmgmt.backend.repository.ProjectRepository;
import com.projectmgmt.backend.repository.TaskRepository;
import com.projectmgmt.backend.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Service
public class ProjectService {

    @Autowired
    private ProjectRepository projectRepository;
    
    @Autowired
    private TaskRepository taskRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private ActivityLogRepository activityLogRepository;
    
    @Autowired
    private NotificationService notificationService;

    /**
     * Get all projects
     */
    public List<Project> getAllProjects() {
        return projectRepository.findAll();
    }
    
    /**
     * Get project by ID
     */
    public Project getProjectById(Long id) {
        return projectRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + id));
    }
    
    /**
     * Create a new project
     */
    @Transactional
    public Project createProject(Project project) {
        // Set initial progress to 0 if not provided
        if (project.getProgress() == null) {
            project.setProgress(0);
        }
        
        // Set initial cost to 0 if not provided
        if (project.getCost() == null) {
            project.setCost(BigDecimal.ZERO);
        }
        
        // Save the project
        Project savedProject = projectRepository.save(project);
        
        // Log the activity
        logActivity(ActivityLog.ActivityType.CREATE, "Created project: " + project.getName());
        
        // Send notifications to project members
        notifyProjectMembers(savedProject, "您已被加入新專案: " + savedProject.getName());
        
        return savedProject;
    }
    
    /**
     * Update an existing project
     */
    @Transactional
    public Project updateProject(Long id, Project projectDetails) {
        Project project = getProjectById(id);
        
        // Update project fields
        project.setName(projectDetails.getName());
        project.setDescription(projectDetails.getDescription());
        project.setStartDate(projectDetails.getStartDate());
        project.setEndDate(projectDetails.getEndDate());
        project.setStatus(projectDetails.getStatus());
        project.setProgress(projectDetails.getProgress());
        project.setCustomer(projectDetails.getCustomer());
        project.setManager(projectDetails.getManager());
        project.setBudget(projectDetails.getBudget());
        
        // Handle members update
        if (projectDetails.getMembers() != null) {
            // Find new members to notify them
            projectDetails.getMembers().stream()
                    .filter(member -> !project.getMembers().contains(member))
                    .forEach(newMember -> notificationService.createNotification(
                            newMember,
                            "專案指派通知",
                            "您已被加入專案: " + project.getName(),
                            "PROJECT_UPDATE",
                            "/projects/" + project.getId()
                    ));
            
            project.setMembers(projectDetails.getMembers());
        }
        
        // Save the updated project
        Project updatedProject = projectRepository.save(project);
        
        // Log the activity
        logActivity(ActivityLog.ActivityType.UPDATE, "Updated project: " + project.getName());
        
        return updatedProject;
    }
    
    /**
     * Update project status
     */
    @Transactional
    public Project updateProjectStatus(Long id, Project.ProjectStatus status) {
        Project project = getProjectById(id);
        
        // Update status
        project.setStatus(status);
        
        // If project is completed, set progress to 100%
        if (status == Project.ProjectStatus.COMPLETED) {
            project.setProgress(100);
        }
        
        // Save the updated project
        Project updatedProject = projectRepository.save(project);
        
        // Log the activity
        logActivity(ActivityLog.ActivityType.UPDATE, "Updated project status: " + project.getName() + " to " + status);
        
        // Notify project manager and members
        String message = "專案 " + project.getName() + " 狀態已更新為: " + getStatusLabel(status);
        notificationService.createNotification(
                project.getManager(),
                "專案狀態更新",
                message,
                "PROJECT_UPDATE",
                "/projects/" + project.getId()
        );
        
        project.getMembers().forEach(member -> {
            notificationService.createNotification(
                    member,
                    "專案狀態更新",
                    message,
                    "PROJECT_UPDATE",
                    "/projects/" + project.getId()
            );
        });
        
        return updatedProject;
    }
    
    /**
     * Update project progress
     */
    @Transactional
    public Project updateProjectProgress(Long id, Integer progress) {
        Project project = getProjectById(id);
        
        // Update progress
        project.setProgress(progress);
        
        // If progress is 100%, update status to COMPLETED if not already CLOSED
        if (progress == 100 && project.getStatus() != Project.ProjectStatus.CLOSED) {
            project.setStatus(Project.ProjectStatus.COMPLETED);
        }
        
        // Save the updated project
        Project updatedProject = projectRepository.save(project);
        
        // Log the activity
        logActivity(ActivityLog.ActivityType.UPDATE, "Updated project progress: " + project.getName() + " to " + progress + "%");
        
        return updatedProject;
    }
    
    /**
     * Delete a project
     */
    @Transactional
    public void deleteProject(Long id) {
        Project project = getProjectById(id);
        
        // Check if project has tasks
        List<Task> tasks = taskRepository.findByProjectId(id);
        if (!tasks.isEmpty()) {
            throw new RuntimeException("Cannot delete project with associated tasks");
        }
        
        // Delete the project
        projectRepository.delete(project);
        
        // Log the activity
        logActivity(ActivityLog.ActivityType.DELETE, "Deleted project: " + project.getName());
    }
    
    /**
     * Calculate project progress based on tasks
     */
    @Transactional
    public void calculateProjectProgress(Long projectId) {
        Project project = getProjectById(projectId);
        List<Task> tasks = taskRepository.findByProjectId(projectId);
        
        if (tasks.isEmpty()) {
            return;
        }
        
        // Calculate average progress of all tasks
        int totalProgress = tasks.stream()
                .mapToInt(Task::getProgress)
                .sum();
        
        int averageProgress = totalProgress / tasks.size();
        
        // Update project progress
        project.setProgress(averageProgress);
        projectRepository.save(project);
    }
    
    /**
     * Get projects by date range
     */
    public List<Project> getProjectsByDateRange(LocalDate startDate, LocalDate endDate) {
        return projectRepository.findByStartDateBetween(startDate, endDate);
    }
    
    /**
     * Get projects by manager
     */
    public List<Project> getProjectsByManager(Long managerId) {
        return projectRepository.findByManagerId(managerId);
    }
    
    /**
     * Get projects by member
     */
    public List<Project> getProjectsByMember(Long memberId) {
        return projectRepository.findByMembersId(memberId);
    }
    
    /**
     * Get projects by status
     */
    public List<Project> getProjectsByStatus(Project.ProjectStatus status) {
        return projectRepository.findByStatus(status);
    }
    
    /**
     * Get projects by customer
     */
    public List<Project> getProjectsByCustomer(Long customerId) {
        return projectRepository.findByCustomerId(customerId);
    }
    
    /**
     * Search projects by name
     */
    public List<Project> searchProjects(String name) {
        return projectRepository.findByNameContaining(name);
    }
    
    /**
     * Log activity
     */
    private void logActivity(ActivityLog.ActivityType activityType, String description) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || authentication.getName().equals("anonymousUser")) {
            return;
        }
        
        String username = authentication.getName();
        Optional<User> userOptional = userRepository.findByUsername(username);
        
        if (userOptional.isPresent()) {
            ActivityLog activityLog = new ActivityLog();
            activityLog.setUser(userOptional.get());
            activityLog.setActivityType(activityType);
            activityLog.setDescription(description);
            activityLog.setIpAddress("127.0.0.1"); // In a real app, get the actual IP
            
            activityLogRepository.save(activityLog);
        }
    }
    
    /**
     * Notify project members
     */
    private void notifyProjectMembers(Project project, String message) {
        if (project.getMembers() != null) {
            project.getMembers().forEach(member -> {
                notificationService.createNotification(
                        member,
                        "專案指派通知",
                        message,
                        "PROJECT_UPDATE",
                        "/projects/" + project.getId()
                );
            });
        }
        
        // Also notify the project manager
        if (project.getManager() != null) {
            notificationService.createNotification(
                    project.getManager(),
                    "專案指派通知",
                    "您已被指派為專案負責人: " + project.getName(),
                    "PROJECT_UPDATE",
                    "/projects/" + project.getId()
            );
        }
    }
    
    /**
     * Get status label
     */
    private String getStatusLabel(Project.ProjectStatus status) {
        switch (status) {
            case PLANNING:
                return "規劃中";
            case IN_PROGRESS:
                return "執行中";
            case COMPLETED:
                return "已完成";
            case DELAYED:
                return "延期";
            case CLOSED:
                return "結案";
            default:
                return status.toString();
        }
    }
}
