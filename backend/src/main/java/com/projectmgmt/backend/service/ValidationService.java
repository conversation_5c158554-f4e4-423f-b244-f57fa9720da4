package com.projectmgmt.backend.service;

import com.projectmgmt.backend.exception.ValidationException;
import com.projectmgmt.backend.model.Project;
import com.projectmgmt.backend.model.Task;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
public class ValidationService {

    /**
     * Validate project data
     */
    public void validateProject(Project project) {
        List<String> errors = new ArrayList<>();
        
        // Validate name
        if (project.getName() == null || project.getName().trim().isEmpty()) {
            errors.add("專案名稱不能為空");
        } else if (project.getName().length() > 100) {
            errors.add("專案名稱不能超過 100 個字元");
        }
        
        // Validate description
        if (project.getDescription() != null && project.getDescription().length() > 500) {
            errors.add("專案描述不能超過 500 個字元");
        }
        
        // Validate dates
        if (project.getStartDate() == null) {
            errors.add("開始日期不能為空");
        }
        
        if (project.getEndDate() != null && project.getStartDate() != null) {
            if (project.getEndDate().isBefore(project.getStartDate())) {
                errors.add("結束日期不能早於開始日期");
            }
        }
        
        // Validate status
        if (project.getStatus() == null) {
            errors.add("專案狀態不能為空");
        }
        
        // Validate progress
        if (project.getProgress() != null) {
            if (project.getProgress() < 0 || project.getProgress() > 100) {
                errors.add("進度必須在 0 到 100 之間");
            }
        }
        
        // Validate manager
        if (project.getManager() == null || project.getManager().getId() == null) {
            errors.add("專案負責人不能為空");
        }
        
        // Validate budget
        if (project.getBudget() != null && project.getBudget().doubleValue() < 0) {
            errors.add("預算不能為負數");
        }
        
        // Throw exception if there are errors
        if (!errors.isEmpty()) {
            throw new ValidationException("專案資料驗證失敗", errors);
        }
    }
    
    /**
     * Validate task data
     */
    public void validateTask(Task task) {
        List<String> errors = new ArrayList<>();
        
        // Validate name
        if (task.getName() == null || task.getName().trim().isEmpty()) {
            errors.add("任務名稱不能為空");
        } else if (task.getName().length() > 100) {
            errors.add("任務名稱不能超過 100 個字元");
        }
        
        // Validate description
        if (task.getDescription() != null && task.getDescription().length() > 500) {
            errors.add("任務描述不能超過 500 個字元");
        }
        
        // Validate dates
        if (task.getStartDate() == null) {
            errors.add("開始日期不能為空");
        }
        
        if (task.getDueDate() == null) {
            errors.add("截止日期不能為空");
        }
        
        if (task.getDueDate() != null && task.getStartDate() != null) {
            if (task.getDueDate().isBefore(task.getStartDate())) {
                errors.add("截止日期不能早於開始日期");
            }
        }
        
        // Validate status
        if (task.getStatus() == null) {
            errors.add("任務狀態不能為空");
        }
        
        // Validate priority
        if (task.getPriority() == null) {
            errors.add("任務優先度不能為空");
        }
        
        // Validate progress
        if (task.getProgress() != null) {
            if (task.getProgress() < 0 || task.getProgress() > 100) {
                errors.add("進度必須在 0 到 100 之間");
            }
        }
        
        // Validate project
        if (task.getProject() == null || task.getProject().getId() == null) {
            errors.add("專案不能為空");
        }
        
        // Validate parent task
        if (task.getParent() != null && task.getParent().getId() != null) {
            if (task.getParent().getId().equals(task.getId())) {
                errors.add("任務不能是自己的子任務");
            }
        }
        
        // Throw exception if there are errors
        if (!errors.isEmpty()) {
            throw new ValidationException("任務資料驗證失敗", errors);
        }
    }
    
    /**
     * Validate date range
     */
    public void validateDateRange(LocalDate startDate, LocalDate endDate) {
        List<String> errors = new ArrayList<>();
        
        if (startDate == null) {
            errors.add("開始日期不能為空");
        }
        
        if (endDate == null) {
            errors.add("結束日期不能為空");
        }
        
        if (startDate != null && endDate != null) {
            if (endDate.isBefore(startDate)) {
                errors.add("結束日期不能早於開始日期");
            }
        }
        
        // Throw exception if there are errors
        if (!errors.isEmpty()) {
            throw new ValidationException("日期範圍驗證失敗", errors);
        }
    }
    
    /**
     * Validate resource allocation
     */
    public void validateResourceAllocation(LocalDate startDate, LocalDate endDate, Double quantity) {
        List<String> errors = new ArrayList<>();
        
        if (startDate == null) {
            errors.add("開始日期不能為空");
        }
        
        if (endDate == null) {
            errors.add("結束日期不能為空");
        }
        
        if (startDate != null && endDate != null) {
            if (endDate.isBefore(startDate)) {
                errors.add("結束日期不能早於開始日期");
            }
        }
        
        if (quantity != null && quantity < 0) {
            errors.add("數量不能為負數");
        }
        
        // Throw exception if there are errors
        if (!errors.isEmpty()) {
            throw new ValidationException("資源分配驗證失敗", errors);
        }
    }
}
