package com.projectmgmt.backend.service;

import com.projectmgmt.backend.exception.ResourceNotFoundException;
import com.projectmgmt.backend.model.Notification;
import com.projectmgmt.backend.model.User;
import com.projectmgmt.backend.repository.NotificationRepository;
import com.projectmgmt.backend.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
public class NotificationService {

    @Autowired
    private NotificationRepository notificationRepository;
    
    @Autowired
    private UserRepository userRepository;

    /**
     * Get all notifications
     */
    public List<Notification> getAllNotifications() {
        return notificationRepository.findAll();
    }
    
    /**
     * Get notification by ID
     */
    public Notification getNotificationById(Long id) {
        return notificationRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Notification not found with id: " + id));
    }
    
    /**
     * Get notifications by user ID
     */
    public List<Notification> getNotificationsByUserId(Long userId) {
        return notificationRepository.findByUserId(userId);
    }
    
    /**
     * Get unread notifications by user ID
     */
    public List<Notification> getUnreadNotificationsByUserId(Long userId) {
        return notificationRepository.findByUserIdAndRead(userId, false);
    }
    
    /**
     * Get notifications by type
     */
    public List<Notification> getNotificationsByType(Notification.NotificationType type) {
        return notificationRepository.findByType(type);
    }
    
    /**
     * Create a notification
     */
    @Transactional
    public Notification createNotification(User user, String title, String message, String type, String link) {
        Notification notification = new Notification();
        notification.setUser(user);
        notification.setTitle(title);
        notification.setMessage(message);
        notification.setRead(false);
        
        try {
            notification.setType(Notification.NotificationType.valueOf(type));
        } catch (IllegalArgumentException e) {
            notification.setType(Notification.NotificationType.SYSTEM);
        }
        
        notification.setLink(link);
        
        return notificationRepository.save(notification);
    }
    
    /**
     * Create a notification with enum type
     */
    @Transactional
    public Notification createNotification(User user, String title, String message, Notification.NotificationType type, String link) {
        Notification notification = new Notification();
        notification.setUser(user);
        notification.setTitle(title);
        notification.setMessage(message);
        notification.setRead(false);
        notification.setType(type);
        notification.setLink(link);
        
        return notificationRepository.save(notification);
    }
    
    /**
     * Mark notification as read
     */
    @Transactional
    public Notification markNotificationAsRead(Long id) {
        Notification notification = getNotificationById(id);
        notification.setRead(true);
        return notificationRepository.save(notification);
    }
    
    /**
     * Mark all notifications as read for current user
     */
    @Transactional
    public void markAllNotificationsAsRead() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || authentication.getName().equals("anonymousUser")) {
            return;
        }
        
        String username = authentication.getName();
        Optional<User> userOptional = userRepository.findByUsername(username);
        
        if (userOptional.isPresent()) {
            User user = userOptional.get();
            List<Notification> unreadNotifications = notificationRepository.findByUserIdAndRead(user.getId(), false);
            
            for (Notification notification : unreadNotifications) {
                notification.setRead(true);
                notificationRepository.save(notification);
            }
        }
    }
    
    /**
     * Delete a notification
     */
    @Transactional
    public void deleteNotification(Long id) {
        Notification notification = getNotificationById(id);
        notificationRepository.delete(notification);
    }
    
    /**
     * Create a task assignment notification
     */
    @Transactional
    public void createTaskAssignmentNotification(User assignee, String taskName, Long taskId) {
        createNotification(
                assignee,
                "任務指派通知",
                "您已被指派新任務: " + taskName,
                Notification.NotificationType.TASK_ASSIGNED,
                "/tasks/" + taskId
        );
    }
    
    /**
     * Create a task due soon notification
     */
    @Transactional
    public void createTaskDueSoonNotification(User assignee, String taskName, Long taskId, String dueDate) {
        createNotification(
                assignee,
                "任務即將到期",
                "任務 " + taskName + " 將於 " + dueDate + " 到期",
                Notification.NotificationType.TASK_DUE_SOON,
                "/tasks/" + taskId
        );
    }
    
    /**
     * Create a task overdue notification
     */
    @Transactional
    public void createTaskOverdueNotification(User assignee, String taskName, Long taskId) {
        createNotification(
                assignee,
                "任務已逾期",
                "任務 " + taskName + " 已逾期",
                Notification.NotificationType.TASK_OVERDUE,
                "/tasks/" + taskId
        );
    }
    
    /**
     * Create a project update notification
     */
    @Transactional
    public void createProjectUpdateNotification(User user, String projectName, Long projectId, String updateMessage) {
        createNotification(
                user,
                "專案更新通知",
                "專案 " + projectName + " " + updateMessage,
                Notification.NotificationType.PROJECT_UPDATE,
                "/projects/" + projectId
        );
    }
    
    /**
     * Create a system notification for all users
     */
    @Transactional
    public void createSystemNotificationForAllUsers(String title, String message) {
        List<User> users = userRepository.findAll();
        
        for (User user : users) {
            createNotification(
                    user,
                    title,
                    message,
                    Notification.NotificationType.SYSTEM,
                    null
            );
        }
    }
}
