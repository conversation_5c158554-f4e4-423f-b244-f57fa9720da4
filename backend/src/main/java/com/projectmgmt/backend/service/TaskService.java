package com.projectmgmt.backend.service;

import com.projectmgmt.backend.exception.ResourceNotFoundException;
import com.projectmgmt.backend.model.ActivityLog;
import com.projectmgmt.backend.model.Project;
import com.projectmgmt.backend.model.Task;
import com.projectmgmt.backend.model.User;
import com.projectmgmt.backend.repository.ActivityLogRepository;
import com.projectmgmt.backend.repository.ProjectRepository;
import com.projectmgmt.backend.repository.TaskRepository;
import com.projectmgmt.backend.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class TaskService {

    @Autowired
    private TaskRepository taskRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ActivityLogRepository activityLogRepository;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private ProjectService projectService;

    /**
     * Get all tasks
     */
    public List<Task> getAllTasks() {
        return taskRepository.findAll();
    }

    /**
     * Get task by ID
     */
    public Task getTaskById(Long id) {
        return taskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + id));
    }

    /**
     * Create a new task
     */
    @Transactional
    public Task createTask(Task task) {
        // Set initial progress to 0 if not provided
        if (task.getProgress() == null) {
            task.setProgress(0);
        }

        // Validate project exists
        Project project = projectRepository.findById(task.getProject().getId())
                .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + task.getProject().getId()));

        // Save the task
        Task savedTask = taskRepository.save(task);

        // Log the activity
        logActivity(ActivityLog.ActivityType.CREATE, "Created task: " + task.getName());

        // Send notification to assignee if assigned
        if (task.getAssignee() != null) {
            notificationService.createTaskAssignmentNotification(
                    task.getAssignee(),
                    task.getName(),
                    savedTask.getId()
            );
        }

        // Update project progress
        projectService.calculateProjectProgress(project.getId());

        return savedTask;
    }

    /**
     * Update an existing task
     */
    @Transactional
    public Task updateTask(Long id, Task taskDetails) {
        Task task = getTaskById(id);
        User previousAssignee = task.getAssignee();

        // Update task fields
        task.setName(taskDetails.getName());
        task.setDescription(taskDetails.getDescription());
        task.setStartDate(taskDetails.getStartDate());
        task.setDueDate(taskDetails.getDueDate());
        task.setStatus(taskDetails.getStatus());
        task.setPriority(taskDetails.getPriority());
        task.setProgress(taskDetails.getProgress());
        task.setParent(taskDetails.getParent());

        // Handle assignee change
        if (taskDetails.getAssignee() != null &&
            (previousAssignee == null || !previousAssignee.getId().equals(taskDetails.getAssignee().getId()))) {
            task.setAssignee(taskDetails.getAssignee());

            // Notify new assignee
            notificationService.createTaskAssignmentNotification(
                    taskDetails.getAssignee(),
                    task.getName(),
                    task.getId()
            );
        } else {
            task.setAssignee(taskDetails.getAssignee());
        }

        // Handle dependencies update
        if (taskDetails.getDependencies() != null) {
            task.setDependencies(taskDetails.getDependencies());
        }

        // Save the updated task
        Task updatedTask = taskRepository.save(task);

        // Log the activity
        logActivity(ActivityLog.ActivityType.UPDATE, "Updated task: " + task.getName());

        // Update project progress
        projectService.calculateProjectProgress(task.getProject().getId());

        return updatedTask;
    }

    /**
     * Update task status
     */
    @Transactional
    public Task updateTaskStatus(Long id, Task.TaskStatus status) {
        Task task = getTaskById(id);

        // Update status
        task.setStatus(status);

        // If task is completed, set progress to 100%
        if (status == Task.TaskStatus.COMPLETED) {
            task.setProgress(100);
        }

        // Save the updated task
        Task updatedTask = taskRepository.save(task);

        // Log the activity
        logActivity(ActivityLog.ActivityType.UPDATE, "Updated task status: " + task.getName() + " to " + status);

        // Notify assignee if task is assigned
        if (task.getAssignee() != null) {
            notificationService.createNotification(
                    task.getAssignee(),
                    "任務狀態更新",
                    "任務 " + task.getName() + " 狀態已更新為: " + getStatusLabel(status),
                    "TASK_ASSIGNED",
                    "/tasks/" + task.getId()
            );
        }

        // Update project progress
        projectService.calculateProjectProgress(task.getProject().getId());

        return updatedTask;
    }

    /**
     * Update task progress
     */
    @Transactional
    public Task updateTaskProgress(Long id, Integer progress) {
        Task task = getTaskById(id);

        // Update progress
        task.setProgress(progress);

        // If progress is 100%, update status to COMPLETED
        if (progress == 100) {
            task.setStatus(Task.TaskStatus.COMPLETED);
        } else if (progress > 0 && task.getStatus() == Task.TaskStatus.NOT_STARTED) {
            task.setStatus(Task.TaskStatus.IN_PROGRESS);
        }

        // Save the updated task
        Task updatedTask = taskRepository.save(task);

        // Log the activity
        logActivity(ActivityLog.ActivityType.UPDATE, "Updated task progress: " + task.getName() + " to " + progress + "%");

        // Update project progress
        projectService.calculateProjectProgress(task.getProject().getId());

        return updatedTask;
    }

    /**
     * Calculate time-based progress for a task
     */
    public Integer calculateTimeBasedProgress(Long id) {
        Task task = getTaskById(id);
        return calculateTimeBasedProgress(task);
    }

    /**
     * Calculate time-based progress for a task
     */
    public Integer calculateTimeBasedProgress(Task task) {
        LocalDate now = LocalDate.now();
        LocalDate startDate = task.getStartDate();
        LocalDate dueDate = task.getDueDate();

        // If task hasn't started yet
        if (now.isBefore(startDate)) {
            return 0;
        }

        // If task is overdue
        if (now.isAfter(dueDate)) {
            return 100;
        }

        // Calculate progress based on elapsed time
        long totalDays = ChronoUnit.DAYS.between(startDate, dueDate);
        long elapsedDays = ChronoUnit.DAYS.between(startDate, now);

        if (totalDays <= 0) {
            return 100;
        }

        int timeBasedProgress = (int) Math.round((double) elapsedDays / totalDays * 100);
        return Math.min(100, Math.max(0, timeBasedProgress));
    }

    /**
     * Get task progress analysis including time-based and actual progress
     */
    public Map<String, Object> getTaskProgressAnalysis(Long id) {
        Task task = getTaskById(id);

        Integer timeBasedProgress = calculateTimeBasedProgress(task);
        Integer actualProgress = task.getProgress();

        Map<String, Object> analysis = new HashMap<>();
        analysis.put("taskId", task.getId());
        analysis.put("taskName", task.getName());
        analysis.put("startDate", task.getStartDate());
        analysis.put("dueDate", task.getDueDate());
        analysis.put("actualProgress", actualProgress);
        analysis.put("timeBasedProgress", timeBasedProgress);
        analysis.put("progressDifference", actualProgress - timeBasedProgress);
        analysis.put("status", task.getStatus());

        // Determine progress status
        String progressStatus;
        int difference = actualProgress - timeBasedProgress;
        if (difference > 10) {
            progressStatus = "AHEAD"; // 超前
        } else if (difference < -10) {
            progressStatus = "BEHIND"; // 落後
        } else {
            progressStatus = "ON_TRACK"; // 正常
        }
        analysis.put("progressStatus", progressStatus);

        // Calculate remaining days
        LocalDate now = LocalDate.now();
        long remainingDays = ChronoUnit.DAYS.between(now, task.getDueDate());
        analysis.put("remainingDays", remainingDays);

        // Check if task is overdue
        analysis.put("isOverdue", now.isAfter(task.getDueDate()) && actualProgress < 100);

        return analysis;
    }

    /**
     * Delete a task
     */
    @Transactional
    public void deleteTask(Long id) {
        Task task = getTaskById(id);

        // Check if task has subtasks
        List<Task> subtasks = taskRepository.findByParentId(id);
        if (!subtasks.isEmpty()) {
            throw new RuntimeException("Cannot delete task with subtasks");
        }

        // Get project ID for later progress update
        Long projectId = task.getProject().getId();

        // Delete the task
        taskRepository.delete(task);

        // Log the activity
        logActivity(ActivityLog.ActivityType.DELETE, "Deleted task: " + task.getName());

        // Update project progress
        projectService.calculateProjectProgress(projectId);
    }

    /**
     * Get tasks by project
     */
    public List<Task> getTasksByProject(Long projectId) {
        return taskRepository.findByProjectId(projectId);
    }

    /**
     * Get tasks by assignee
     */
    public List<Task> getTasksByAssignee(Long assigneeId) {
        return taskRepository.findByAssigneeId(assigneeId);
    }

    /**
     * Get tasks by status
     */
    public List<Task> getTasksByStatus(Task.TaskStatus status) {
        return taskRepository.findByStatus(status);
    }

    /**
     * Get tasks by priority
     */
    public List<Task> getTasksByPriority(Task.TaskPriority priority) {
        return taskRepository.findByPriority(priority);
    }

    /**
     * Get tasks due before a date
     */
    public List<Task> getTasksDueBefore(LocalDate date) {
        return taskRepository.findByDueDateBefore(date);
    }

    /**
     * Get tasks due between dates
     */
    public List<Task> getTasksDueBetween(LocalDate startDate, LocalDate endDate) {
        return taskRepository.findByDueDateBetween(startDate, endDate);
    }

    /**
     * Get subtasks
     */
    public List<Task> getSubtasks(Long parentId) {
        return taskRepository.findByParentId(parentId);
    }

    /**
     * Get root tasks
     */
    public List<Task> getRootTasks() {
        return taskRepository.findByParentIsNull();
    }

    /**
     * Check for overdue tasks and send notifications
     * This would typically be called by a scheduled job
     */
    @Transactional
    public void checkOverdueTasks() {
        LocalDate today = LocalDate.now();
        List<Task> overdueTasks = taskRepository.findByDueDateBefore(today);

        for (Task task : overdueTasks) {
            if (task.getStatus() != Task.TaskStatus.COMPLETED && task.getAssignee() != null) {
                // Update task status to DELAYED if not already
                if (task.getStatus() != Task.TaskStatus.DELAYED) {
                    task.setStatus(Task.TaskStatus.DELAYED);
                    taskRepository.save(task);

                    // Send notification to assignee
                    notificationService.createTaskOverdueNotification(
                            task.getAssignee(),
                            task.getName(),
                            task.getId()
                    );

                    // Also notify project manager
                    Project project = task.getProject();
                    if (project != null && project.getManager() != null) {
                        notificationService.createNotification(
                                project.getManager(),
                                "任務逾期通知",
                                "任務 " + task.getName() + " 已逾期，負責人: " +
                                        task.getAssignee().getFirstName() + " " + task.getAssignee().getLastName(),
                                "TASK_OVERDUE",
                                "/tasks/" + task.getId()
                        );
                    }
                }
            }
        }
    }

    /**
     * Check for tasks due soon and send notifications
     * This would typically be called by a scheduled job
     */
    @Transactional
    public void checkTasksDueSoon() {
        LocalDate today = LocalDate.now();
        LocalDate threeDaysLater = today.plusDays(3);
        List<Task> tasksDueSoon = taskRepository.findByDueDateBetween(today, threeDaysLater);

        for (Task task : tasksDueSoon) {
            if (task.getStatus() != Task.TaskStatus.COMPLETED && task.getAssignee() != null) {
                // Send notification to assignee
                notificationService.createTaskDueSoonNotification(
                        task.getAssignee(),
                        task.getName(),
                        task.getId(),
                        task.getDueDate().toString()
                );
            }
        }
    }

    /**
     * Log activity
     */
    private void logActivity(ActivityLog.ActivityType activityType, String description) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || authentication.getName().equals("anonymousUser")) {
            return;
        }

        String username = authentication.getName();
        Optional<User> userOptional = userRepository.findByUsername(username);

        if (userOptional.isPresent()) {
            ActivityLog activityLog = new ActivityLog();
            activityLog.setUser(userOptional.get());
            activityLog.setActivityType(activityType);
            activityLog.setDescription(description);
            activityLog.setIpAddress("127.0.0.1"); // In a real app, get the actual IP

            activityLogRepository.save(activityLog);
        }
    }

    /**
     * Get status label
     */
    private String getStatusLabel(Task.TaskStatus status) {
        switch (status) {
            case NOT_STARTED:
                return "未開始";
            case IN_PROGRESS:
                return "進行中";
            case COMPLETED:
                return "已完成";
            case DELAYED:
                return "延期";
            default:
                return status.toString();
        }
    }
}
