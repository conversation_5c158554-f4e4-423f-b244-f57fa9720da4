package com.projectmgmt.backend.service;

import com.projectmgmt.backend.exception.ResourceNotFoundException;
import com.projectmgmt.backend.model.ActivityLog;
import com.projectmgmt.backend.model.Document;
import com.projectmgmt.backend.model.User;
import com.projectmgmt.backend.repository.ActivityLogRepository;
import com.projectmgmt.backend.repository.DocumentRepository;
import com.projectmgmt.backend.repository.ProjectRepository;
import com.projectmgmt.backend.repository.TaskRepository;
import com.projectmgmt.backend.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
public class DocumentService {

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ActivityLogRepository activityLogRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private TaskRepository taskRepository;

    @Value("${file.upload-dir}")
    private String uploadDir;

    private final Path fileStoragePath;

    @Autowired
    public DocumentService(@Value("${file.upload-dir}") String uploadDir) {
        this.uploadDir = uploadDir;
        this.fileStoragePath = Paths.get(uploadDir).toAbsolutePath().normalize();

        try {
            Files.createDirectories(this.fileStoragePath);
        } catch (IOException ex) {
            throw new RuntimeException("Could not create the directory where the uploaded files will be stored.", ex);
        }
    }

    /**
     * Get all documents
     */
    public List<Document> getAllDocuments() {
        return documentRepository.findAll();
    }

    /**
     * Get document by ID
     */
    public Document getDocumentById(Long id) {
        return documentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Document not found with id: " + id));
    }

    /**
     * Get documents by project
     */
    public List<Document> getDocumentsByProject(Long projectId) {
        return documentRepository.findByProjectId(projectId);
    }

    /**
     * Get documents by task
     */
    public List<Document> getDocumentsByTask(Long taskId) {
        return documentRepository.findByTaskId(taskId);
    }

    /**
     * Get documents by user
     */
    public List<Document> getDocumentsByUser(Long userId) {
        return documentRepository.findByUploadedById(userId);
    }

    /**
     * Get documents by file type
     */
    public List<Document> getDocumentsByType(String fileType) {
        return documentRepository.findByFileType(fileType);
    }

    /**
     * Search documents by name
     */
    public List<Document> searchDocuments(String name) {
        return documentRepository.findByNameContaining(name);
    }

    /**
     * Upload a document
     */
    @Transactional
    public Document uploadDocument(MultipartFile file, String name, String version, Long projectId, Long taskId) {
        // Normalize file name
        String originalFilename = StringUtils.cleanPath(file.getOriginalFilename());

        // Check if the file's name contains invalid characters
        if (originalFilename.contains("..")) {
            throw new RuntimeException("Sorry! Filename contains invalid path sequence " + originalFilename);
        }

        // Generate unique file name
        String fileExtension = getFileExtension(originalFilename);
        String uniqueFilename = UUID.randomUUID().toString() + fileExtension;

        try {
            // Copy file to the target location
            Path targetLocation = this.fileStoragePath.resolve(uniqueFilename);
            Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);

            // Create document record
            Document document = new Document();
            document.setName(name);
            document.setFilePath(uniqueFilename);
            document.setFileType(fileExtension.substring(1)); // Remove the dot
            document.setVersion(version);

            // Set project and task if provided
            if (projectId != null) {
                document.setProject(projectRepository.findById(projectId)
                        .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + projectId)));
            }

            if (taskId != null) {
                document.setTask(taskRepository.findById(taskId)
                        .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + taskId)));
            }

            // Set uploaded by
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && !authentication.getName().equals("anonymousUser")) {
                String username = authentication.getName();
                Optional<User> userOptional = userRepository.findByUsername(username);

                if (userOptional.isPresent()) {
                    document.setUploadedBy(userOptional.get());
                }
            }

            // Save document
            Document savedDocument = documentRepository.save(document);

            // Log activity
            logActivity(ActivityLog.ActivityType.CREATE, "Uploaded document: " + name);

            return savedDocument;
        } catch (IOException ex) {
            throw new RuntimeException("Could not store file " + originalFilename + ". Please try again!", ex);
        }
    }

    /**
     * Download a document
     */
    public Resource downloadDocument(Long id) {
        Document document = getDocumentById(id);

        try {
            Path filePath = this.fileStoragePath.resolve(document.getFilePath()).normalize();
            Resource resource = new UrlResource(filePath.toUri());

            if (resource.exists()) {
                // Log activity
                logActivity(ActivityLog.ActivityType.VIEW, "Downloaded document: " + document.getName());

                return resource;
            } else {
                throw new ResourceNotFoundException("File not found: " + document.getFilePath());
            }
        } catch (MalformedURLException ex) {
            throw new ResourceNotFoundException("File not found: " + document.getFilePath());
        }
    }

    /**
     * Delete a document
     */
    @Transactional
    public void deleteDocument(Long id) {
        Document document = getDocumentById(id);

        try {
            // Delete file from disk
            Path filePath = this.fileStoragePath.resolve(document.getFilePath()).normalize();
            Files.deleteIfExists(filePath);

            // Delete document record
            documentRepository.delete(document);

            // Log activity
            logActivity(ActivityLog.ActivityType.DELETE, "Deleted document: " + document.getName());
        } catch (IOException ex) {
            throw new RuntimeException("Could not delete file: " + document.getFilePath(), ex);
        }
    }

    /**
     * Get file extension
     */
    private String getFileExtension(String filename) {
        int dotIndex = filename.lastIndexOf('.');
        return (dotIndex == -1) ? "" : filename.substring(dotIndex);
    }

    /**
     * Log activity
     */
    private void logActivity(ActivityLog.ActivityType activityType, String description) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || authentication.getName().equals("anonymousUser")) {
            return;
        }

        String username = authentication.getName();
        Optional<User> userOptional = userRepository.findByUsername(username);

        if (userOptional.isPresent()) {
            ActivityLog activityLog = new ActivityLog();
            activityLog.setUser(userOptional.get());
            activityLog.setActivityType(activityType);
            activityLog.setDescription(description);
            activityLog.setIpAddress("127.0.0.1"); // In a real app, get the actual IP

            activityLogRepository.save(activityLog);
        }
    }
}
