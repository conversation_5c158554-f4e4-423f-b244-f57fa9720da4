package com.projectmgmt.backend.service;

import com.projectmgmt.backend.model.*;
import com.projectmgmt.backend.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Transactional
public class ResourceAvailabilityService {

    @Autowired
    private ResourceRepository resourceRepository;

    @Autowired
    private ResourceAllocationRepository resourceAllocationRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private TaskRepository taskRepository;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private UserRepository userRepository;

    /**
     * 檢查資源在指定日期範圍內的可用性
     */
    public ResourceAvailabilityResult checkResourceAvailability(Long resourceId, LocalDate startDate, LocalDate endDate, BigDecimal requiredQuantity) {
        Resource resource = resourceRepository.findById(resourceId)
                .orElseThrow(() -> new RuntimeException("資源不存在"));

        if (!resource.isAvailable()) {
            return new ResourceAvailabilityResult(false, "資源目前不可用", BigDecimal.ZERO);
        }

        // 獲取該時間段內的所有分配
        List<ResourceAllocation> existingAllocations = resourceAllocationRepository
                .findByResourceIdAndDateRangeOverlap(resourceId, startDate, endDate);

        // 計算已分配的總量
        BigDecimal allocatedQuantity = existingAllocations.stream()
                .map(ResourceAllocation::getQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 假設每個資源的最大可用量為100（可以根據實際需求調整）
        BigDecimal maxAvailableQuantity = new BigDecimal("100");
        BigDecimal availableQuantity = maxAvailableQuantity.subtract(allocatedQuantity);

        boolean isAvailable = availableQuantity.compareTo(requiredQuantity) >= 0;
        String message = isAvailable ? "資源可用" : "資源數量不足";

        return new ResourceAvailabilityResult(isAvailable, message, availableQuantity);
    }

    /**
     * 檢查專案所需的所有資源
     */
    public ProjectResourceCheckResult checkProjectResourceRequirements(Long projectId) {
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new RuntimeException("專案不存在"));

        List<ResourceAllocation> projectAllocations = resourceAllocationRepository.findByProjectId(projectId);

        ProjectResourceCheckResult result = new ProjectResourceCheckResult();
        result.setProjectId(projectId);
        result.setProjectName(project.getName());

        for (ResourceAllocation allocation : projectAllocations) {
            ResourceAvailabilityResult availabilityResult = checkResourceAvailability(
                    allocation.getResource().getId(),
                    allocation.getStartDate(),
                    allocation.getEndDate(),
                    allocation.getQuantity()
            );

            if (!availabilityResult.isAvailable()) {
                result.addUnavailableResource(allocation.getResource(), availabilityResult);

                // 發送資源不足通知
                sendResourceShortageNotification(project, allocation.getResource(), availabilityResult);
            }
        }

        return result;
    }

    /**
     * 檢查任務所需的所有資源
     */
    public TaskResourceCheckResult checkTaskResourceRequirements(Long taskId) {
        Task task = taskRepository.findById(taskId)
                .orElseThrow(() -> new RuntimeException("任務不存在"));

        List<ResourceAllocation> taskAllocations = resourceAllocationRepository.findByTaskId(taskId);

        TaskResourceCheckResult result = new TaskResourceCheckResult();
        result.setTaskId(taskId);
        result.setTaskName(task.getName());

        for (ResourceAllocation allocation : taskAllocations) {
            ResourceAvailabilityResult availabilityResult = checkResourceAvailability(
                    allocation.getResource().getId(),
                    allocation.getStartDate(),
                    allocation.getEndDate(),
                    allocation.getQuantity()
            );

            if (!availabilityResult.isAvailable()) {
                result.addUnavailableResource(allocation.getResource(), availabilityResult);

                // 發送資源不足通知
                if (task.getProject() != null) {
                    sendResourceShortageNotification(task.getProject(), allocation.getResource(), availabilityResult);
                }
            }
        }

        return result;
    }

    /**
     * 發送資源不足通知給專案負責人和採購人員
     */
    private void sendResourceShortageNotification(Project project, Resource resource, ResourceAvailabilityResult availabilityResult) {
        String title = "資源不足警告";
        String message = String.format("專案 '%s' 中的資源 '%s' 數量不足。可用數量: %s，需求原因: %s",
                project.getName(),
                resource.getName(),
                availabilityResult.getAvailableQuantity(),
                availabilityResult.getMessage());

        // 通知專案負責人
        if (project.getManager() != null) {
            notificationService.createNotification(
                    project.getManager(),
                    title,
                    message,
                    Notification.NotificationType.RESOURCE_SHORTAGE,
                    "/projects/" + project.getId()
            );
        }

        // 通知採購人員
        if (project.getProcurementOfficer() != null) {
            notificationService.createNotification(
                    project.getProcurementOfficer(),
                    title + " - 需要採購",
                    message + " 請安排採購相關資源。",
                    Notification.NotificationType.PROCUREMENT_REQUEST,
                    "/projects/" + project.getId()
            );
        }
    }

    /**
     * 獲取資源使用統計
     */
    public ResourceUsageStatistics getResourceUsageStatistics(Long resourceId, LocalDate startDate, LocalDate endDate) {
        Resource resource = resourceRepository.findById(resourceId)
                .orElseThrow(() -> new RuntimeException("資源不存在"));

        List<ResourceAllocation> allocations = resourceAllocationRepository
                .findByResourceIdAndDateRangeOverlap(resourceId, startDate, endDate);

        ResourceUsageStatistics stats = new ResourceUsageStatistics();
        stats.setResourceId(resourceId);
        stats.setResourceName(resource.getName());
        stats.setTotalAllocations(allocations.size());

        BigDecimal totalQuantityUsed = allocations.stream()
                .map(ResourceAllocation::getQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        stats.setTotalQuantityUsed(totalQuantityUsed);

        // 計算使用率（假設最大可用量為100）
        BigDecimal maxCapacity = new BigDecimal("100");
        double utilizationRate = totalQuantityUsed.divide(maxCapacity, 4, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal("100")).doubleValue();
        stats.setUtilizationRate(utilizationRate);

        return stats;
    }

    /**
     * 獲取資源在指定日期範圍內的分配情況
     */
    public List<ResourceAllocation> getResourceAllocationsInDateRange(Long resourceId, LocalDate startDate, LocalDate endDate) {
        return resourceAllocationRepository.findByResourceIdAndDateRangeOverlap(resourceId, startDate, endDate);
    }

    // 內部類定義
    public static class ResourceAvailabilityResult {
        private boolean available;
        private String message;
        private BigDecimal availableQuantity;

        public ResourceAvailabilityResult(boolean available, String message, BigDecimal availableQuantity) {
            this.available = available;
            this.message = message;
            this.availableQuantity = availableQuantity;
        }

        // Getters and setters
        public boolean isAvailable() { return available; }
        public void setAvailable(boolean available) { this.available = available; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public BigDecimal getAvailableQuantity() { return availableQuantity; }
        public void setAvailableQuantity(BigDecimal availableQuantity) { this.availableQuantity = availableQuantity; }
    }

    public static class ProjectResourceCheckResult {
        private Long projectId;
        private String projectName;
        private List<ResourceShortage> unavailableResources = new java.util.ArrayList<>();

        public void addUnavailableResource(Resource resource, ResourceAvailabilityResult result) {
            unavailableResources.add(new ResourceShortage(resource, result));
        }

        // Getters and setters
        public Long getProjectId() { return projectId; }
        public void setProjectId(Long projectId) { this.projectId = projectId; }
        public String getProjectName() { return projectName; }
        public void setProjectName(String projectName) { this.projectName = projectName; }
        public List<ResourceShortage> getUnavailableResources() { return unavailableResources; }
        public void setUnavailableResources(List<ResourceShortage> unavailableResources) { this.unavailableResources = unavailableResources; }
        public boolean hasResourceShortages() { return !unavailableResources.isEmpty(); }
    }

    public static class TaskResourceCheckResult {
        private Long taskId;
        private String taskName;
        private List<ResourceShortage> unavailableResources = new java.util.ArrayList<>();

        public void addUnavailableResource(Resource resource, ResourceAvailabilityResult result) {
            unavailableResources.add(new ResourceShortage(resource, result));
        }

        // Getters and setters
        public Long getTaskId() { return taskId; }
        public void setTaskId(Long taskId) { this.taskId = taskId; }
        public String getTaskName() { return taskName; }
        public void setTaskName(String taskName) { this.taskName = taskName; }
        public List<ResourceShortage> getUnavailableResources() { return unavailableResources; }
        public void setUnavailableResources(List<ResourceShortage> unavailableResources) { this.unavailableResources = unavailableResources; }
        public boolean hasResourceShortages() { return !unavailableResources.isEmpty(); }
    }

    public static class ResourceShortage {
        private Resource resource;
        private ResourceAvailabilityResult availabilityResult;

        public ResourceShortage(Resource resource, ResourceAvailabilityResult availabilityResult) {
            this.resource = resource;
            this.availabilityResult = availabilityResult;
        }

        // Getters and setters
        public Resource getResource() { return resource; }
        public void setResource(Resource resource) { this.resource = resource; }
        public ResourceAvailabilityResult getAvailabilityResult() { return availabilityResult; }
        public void setAvailabilityResult(ResourceAvailabilityResult availabilityResult) { this.availabilityResult = availabilityResult; }
    }

    public static class ResourceUsageStatistics {
        private Long resourceId;
        private String resourceName;
        private int totalAllocations;
        private BigDecimal totalQuantityUsed;
        private double utilizationRate;

        // Getters and setters
        public Long getResourceId() { return resourceId; }
        public void setResourceId(Long resourceId) { this.resourceId = resourceId; }
        public String getResourceName() { return resourceName; }
        public void setResourceName(String resourceName) { this.resourceName = resourceName; }
        public int getTotalAllocations() { return totalAllocations; }
        public void setTotalAllocations(int totalAllocations) { this.totalAllocations = totalAllocations; }
        public BigDecimal getTotalQuantityUsed() { return totalQuantityUsed; }
        public void setTotalQuantityUsed(BigDecimal totalQuantityUsed) { this.totalQuantityUsed = totalQuantityUsed; }
        public double getUtilizationRate() { return utilizationRate; }
        public void setUtilizationRate(double utilizationRate) { this.utilizationRate = utilizationRate; }
    }
}
