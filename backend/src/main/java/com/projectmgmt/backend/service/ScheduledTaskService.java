package com.projectmgmt.backend.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
public class ScheduledTaskService {
    
    private static final Logger logger = LoggerFactory.getLogger(ScheduledTaskService.class);
    
    @Autowired
    private TaskService taskService;
    
    /**
     * Check for overdue tasks daily at 1:00 AM
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void checkOverdueTasks() {
        logger.info("Running scheduled job: Check overdue tasks");
        taskService.checkOverdueTasks();
    }
    
    /**
     * Check for tasks due soon daily at 8:00 AM
     */
    @Scheduled(cron = "0 0 8 * * ?")
    public void checkTasksDueSoon() {
        logger.info("Running scheduled job: Check tasks due soon");
        taskService.checkTasksDueSoon();
    }
}
