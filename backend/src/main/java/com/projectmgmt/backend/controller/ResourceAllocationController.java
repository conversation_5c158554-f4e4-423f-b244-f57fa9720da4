package com.projectmgmt.backend.controller;

import com.projectmgmt.backend.dto.ResourceAllocationDTO;
import com.projectmgmt.backend.exception.ResourceNotFoundException;
import com.projectmgmt.backend.model.Project;
import com.projectmgmt.backend.model.Resource;
import com.projectmgmt.backend.model.ResourceAllocation;
import com.projectmgmt.backend.model.Task;
import com.projectmgmt.backend.repository.ProjectRepository;
import com.projectmgmt.backend.repository.ResourceAllocationRepository;
import com.projectmgmt.backend.repository.ResourceRepository;
import com.projectmgmt.backend.repository.TaskRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/resource-allocations")
public class ResourceAllocationController {

    @Autowired
    private ResourceAllocationRepository resourceAllocationRepository;

    @Autowired
    private ResourceRepository resourceRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private TaskRepository taskRepository;

    @GetMapping
    public List<ResourceAllocationDTO> getAllResourceAllocations() {
        return resourceAllocationRepository.findAll().stream()
                .map(ResourceAllocationDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/{id}")
    public ResponseEntity<ResourceAllocationDTO> getResourceAllocationById(@PathVariable Long id) {
        ResourceAllocation resourceAllocation = resourceAllocationRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Resource allocation not found with id: " + id));
        return ResponseEntity.ok(new ResourceAllocationDTO(resourceAllocation));
    }

    @GetMapping("/resource/{resourceId}")
    public List<ResourceAllocationDTO> getResourceAllocationsByResource(@PathVariable Long resourceId) {
        return resourceAllocationRepository.findByResourceId(resourceId).stream()
                .map(ResourceAllocationDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/project/{projectId}")
    public List<ResourceAllocationDTO> getResourceAllocationsByProject(@PathVariable Long projectId) {
        return resourceAllocationRepository.findByProjectId(projectId).stream()
                .map(ResourceAllocationDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/task/{taskId}")
    public List<ResourceAllocationDTO> getResourceAllocationsByTask(@PathVariable Long taskId) {
        return resourceAllocationRepository.findByTaskId(taskId).stream()
                .map(ResourceAllocationDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/date-range")
    public List<ResourceAllocationDTO> getResourceAllocationsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        return resourceAllocationRepository.findByStartDateBetween(startDate, endDate).stream()
                .map(ResourceAllocationDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/available")
    public List<ResourceAllocationDTO> getResourceAllocationsByDate(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        return resourceAllocationRepository.findByStartDateLessThanEqualAndEndDateGreaterThanEqual(date, date).stream()
                .map(ResourceAllocationDTO::new)
                .collect(Collectors.toList());
    }

    @PostMapping
    public ResourceAllocationDTO createResourceAllocation(@RequestBody ResourceAllocation resourceAllocation) {
        // 驗證資源是否存在
        if (resourceAllocation.getResource() != null && resourceAllocation.getResource().getId() != null) {
            Resource resource = resourceRepository.findById(resourceAllocation.getResource().getId())
                    .orElseThrow(() -> new ResourceNotFoundException("Resource not found with id: " + resourceAllocation.getResource().getId()));
            resourceAllocation.setResource(resource);
        }

        // 驗證項目是否存在
        if (resourceAllocation.getProject() != null && resourceAllocation.getProject().getId() != null) {
            Project project = projectRepository.findById(resourceAllocation.getProject().getId())
                    .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + resourceAllocation.getProject().getId()));
            resourceAllocation.setProject(project);
        }

        // 驗證任務是否存在
        if (resourceAllocation.getTask() != null && resourceAllocation.getTask().getId() != null) {
            Task task = taskRepository.findById(resourceAllocation.getTask().getId())
                    .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + resourceAllocation.getTask().getId()));
            resourceAllocation.setTask(task);
        }

        ResourceAllocation savedAllocation = resourceAllocationRepository.save(resourceAllocation);
        return new ResourceAllocationDTO(savedAllocation);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('DEPARTMENT_HEAD') or @projectSecurity.isProjectManager(#resourceAllocation.project.id)")
    public ResponseEntity<ResourceAllocationDTO> updateResourceAllocation(@PathVariable Long id, @RequestBody ResourceAllocation resourceAllocationDetails) {
        ResourceAllocation resourceAllocation = resourceAllocationRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Resource allocation not found with id: " + id));

        resourceAllocation.setStartDate(resourceAllocationDetails.getStartDate());
        resourceAllocation.setEndDate(resourceAllocationDetails.getEndDate());
        resourceAllocation.setQuantity(resourceAllocationDetails.getQuantity());
        resourceAllocation.setCost(resourceAllocationDetails.getCost());

        // 更新資源
        if (resourceAllocationDetails.getResource() != null && resourceAllocationDetails.getResource().getId() != null) {
            Resource resource = resourceRepository.findById(resourceAllocationDetails.getResource().getId())
                    .orElseThrow(() -> new ResourceNotFoundException("Resource not found with id: " + resourceAllocationDetails.getResource().getId()));
            resourceAllocation.setResource(resource);
        }

        // 更新項目
        if (resourceAllocationDetails.getProject() != null && resourceAllocationDetails.getProject().getId() != null) {
            Project project = projectRepository.findById(resourceAllocationDetails.getProject().getId())
                    .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + resourceAllocationDetails.getProject().getId()));
            resourceAllocation.setProject(project);
        }

        // 更新任務
        if (resourceAllocationDetails.getTask() != null && resourceAllocationDetails.getTask().getId() != null) {
            Task task = taskRepository.findById(resourceAllocationDetails.getTask().getId())
                    .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + resourceAllocationDetails.getTask().getId()));
            resourceAllocation.setTask(task);
        }

        ResourceAllocation updatedResourceAllocation = resourceAllocationRepository.save(resourceAllocation);
        return ResponseEntity.ok(new ResourceAllocationDTO(updatedResourceAllocation));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('DEPARTMENT_HEAD') or @projectSecurity.isProjectManager(#resourceAllocation.project.id)")
    public ResponseEntity<Map<String, Boolean>> deleteResourceAllocation(@PathVariable Long id) {
        ResourceAllocation resourceAllocation = resourceAllocationRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Resource allocation not found with id: " + id));

        resourceAllocationRepository.delete(resourceAllocation);
        Map<String, Boolean> response = new HashMap<>();
        response.put("deleted", Boolean.TRUE);
        return ResponseEntity.ok(response);
    }
}
