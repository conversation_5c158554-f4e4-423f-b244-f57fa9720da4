package com.projectmgmt.backend.controller;

import com.projectmgmt.backend.exception.ResourceNotFoundException;
import com.projectmgmt.backend.model.Resource;
import com.projectmgmt.backend.model.ResourceAllocation;
import com.projectmgmt.backend.repository.ResourceAllocationRepository;
import com.projectmgmt.backend.repository.ResourceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/resources")
public class ResourceController {
    @Autowired
    private ResourceRepository resourceRepository;

    @Autowired
    private ResourceAllocationRepository resourceAllocationRepository;

    @GetMapping
    public List<Resource> getAllResources() {
        return resourceRepository.findAll();
    }

    @GetMapping("/type/{type}")
    public List<Resource> getResourcesByType(@PathVariable Resource.ResourceType type) {
        return resourceRepository.findByType(type);
    }

    @GetMapping("/available")
    public List<Resource> getAvailableResources() {
        return resourceRepository.findByAvailable(true);
    }

    @GetMapping("/search")
    public List<Resource> searchResources(@RequestParam String name) {
        return resourceRepository.findByNameContaining(name);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Resource> getResourceById(@PathVariable Long id) {
        Resource resource = resourceRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Resource not found with id: " + id));
        return ResponseEntity.ok(resource);
    }

    @GetMapping("/{id}/allocations")
    public List<ResourceAllocation> getResourceAllocations(@PathVariable Long id) {
        return resourceAllocationRepository.findByResourceId(id);
    }

    @GetMapping("/allocations/date-range")
    public List<ResourceAllocation> getResourceAllocationsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        return resourceAllocationRepository.findByStartDateBetween(startDate, endDate);
    }

    @GetMapping("/allocations/project/{projectId}")
    public List<ResourceAllocation> getResourceAllocationsByProject(@PathVariable Long projectId) {
        return resourceAllocationRepository.findByProjectId(projectId);
    }

    @GetMapping("/allocations/task/{taskId}")
    public List<ResourceAllocation> getResourceAllocationsByTask(@PathVariable Long taskId) {
        return resourceAllocationRepository.findByTaskId(taskId);
    }

    @PostMapping
    public Resource createResource(@RequestBody Resource resource) {
        return resourceRepository.save(resource);
    }

    @PostMapping("/allocations")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('DEPARTMENT_HEAD') or @projectSecurity.isProjectManager(#resourceAllocation.project.id)")
    public ResourceAllocation createResourceAllocation(@RequestBody ResourceAllocation resourceAllocation) {
        return resourceAllocationRepository.save(resourceAllocation);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<Resource> updateResource(@PathVariable Long id, @RequestBody Resource resourceDetails) {
        Resource resource = resourceRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Resource not found with id: " + id));

        resource.setName(resourceDetails.getName());
        resource.setDescription(resourceDetails.getDescription());
        resource.setType(resourceDetails.getType());
        resource.setAvailable(resourceDetails.isAvailable());

        Resource updatedResource = resourceRepository.save(resource);
        return ResponseEntity.ok(updatedResource);
    }

    @PutMapping("/allocations/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('DEPARTMENT_HEAD') or @projectSecurity.isProjectManager(#resourceAllocation.project.id)")
    public ResponseEntity<ResourceAllocation> updateResourceAllocation(@PathVariable Long id, @RequestBody ResourceAllocation resourceAllocationDetails) {
        ResourceAllocation resourceAllocation = resourceAllocationRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Resource allocation not found with id: " + id));

        resourceAllocation.setResource(resourceAllocationDetails.getResource());
        resourceAllocation.setProject(resourceAllocationDetails.getProject());
        resourceAllocation.setTask(resourceAllocationDetails.getTask());
        resourceAllocation.setStartDate(resourceAllocationDetails.getStartDate());
        resourceAllocation.setEndDate(resourceAllocationDetails.getEndDate());
        resourceAllocation.setQuantity(resourceAllocationDetails.getQuantity());
        resourceAllocation.setCost(resourceAllocationDetails.getCost());

        ResourceAllocation updatedResourceAllocation = resourceAllocationRepository.save(resourceAllocation);
        return ResponseEntity.ok(updatedResourceAllocation);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<Map<String, Boolean>> deleteResource(@PathVariable Long id) {
        Resource resource = resourceRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Resource not found with id: " + id));

        // Check if resource has allocations
        List<ResourceAllocation> allocations = resourceAllocationRepository.findByResourceId(id);
        if (!allocations.isEmpty()) {
            throw new RuntimeException("Cannot delete resource with allocations");
        }

        resourceRepository.delete(resource);
        Map<String, Boolean> response = new HashMap<>();
        response.put("deleted", Boolean.TRUE);
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/allocations/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('DEPARTMENT_HEAD') or @projectSecurity.isProjectManager(#resourceAllocation.project.id)")
    public ResponseEntity<Map<String, Boolean>> deleteResourceAllocation(@PathVariable Long id) {
        ResourceAllocation resourceAllocation = resourceAllocationRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Resource allocation not found with id: " + id));

        resourceAllocationRepository.delete(resourceAllocation);
        Map<String, Boolean> response = new HashMap<>();
        response.put("deleted", Boolean.TRUE);
        return ResponseEntity.ok(response);
    }
}
