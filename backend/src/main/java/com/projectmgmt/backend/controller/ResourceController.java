package com.projectmgmt.backend.controller;

import com.projectmgmt.backend.dto.ResourceDTO;
import com.projectmgmt.backend.dto.ResourceAllocationDTO;
import com.projectmgmt.backend.exception.ResourceNotFoundException;
import com.projectmgmt.backend.model.Resource;
import com.projectmgmt.backend.model.ResourceAllocation;
import com.projectmgmt.backend.repository.ResourceAllocationRepository;
import com.projectmgmt.backend.repository.ResourceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/resources")
public class ResourceController {
    @Autowired
    private ResourceRepository resourceRepository;

    @Autowired
    private ResourceAllocationRepository resourceAllocationRepository;

    @GetMapping
    public List<ResourceDTO> getAllResources() {
        return resourceRepository.findAll().stream()
                .map(ResourceDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/type/{type}")
    public List<ResourceDTO> getResourcesByType(@PathVariable Resource.ResourceType type) {
        return resourceRepository.findByType(type).stream()
                .map(ResourceDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/available")
    public List<ResourceDTO> getAvailableResources() {
        return resourceRepository.findByAvailable(true).stream()
                .map(ResourceDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/search")
    public List<ResourceDTO> searchResources(@RequestParam String name) {
        return resourceRepository.findByNameContaining(name).stream()
                .map(ResourceDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/{id}")
    public ResponseEntity<ResourceDTO> getResourceById(@PathVariable Long id) {
        Resource resource = resourceRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Resource not found with id: " + id));
        return ResponseEntity.ok(new ResourceDTO(resource));
    }

    @GetMapping("/{id}/allocations")
    public List<ResourceAllocationDTO> getResourceAllocations(@PathVariable Long id) {
        return resourceAllocationRepository.findByResourceId(id).stream()
                .map(ResourceAllocationDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/allocations/date-range")
    public List<ResourceAllocationDTO> getResourceAllocationsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        return resourceAllocationRepository.findByStartDateBetween(startDate, endDate).stream()
                .map(ResourceAllocationDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/allocations/project/{projectId}")
    public List<ResourceAllocationDTO> getResourceAllocationsByProject(@PathVariable Long projectId) {
        return resourceAllocationRepository.findByProjectId(projectId).stream()
                .map(ResourceAllocationDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/allocations/task/{taskId}")
    public List<ResourceAllocationDTO> getResourceAllocationsByTask(@PathVariable Long taskId) {
        return resourceAllocationRepository.findByTaskId(taskId).stream()
                .map(ResourceAllocationDTO::new)
                .collect(Collectors.toList());
    }

    @PostMapping
    public ResourceDTO createResource(@RequestBody Resource resource) {
        Resource savedResource = resourceRepository.save(resource);
        return new ResourceDTO(savedResource);
    }

    @PostMapping("/allocations")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('DEPARTMENT_HEAD') or @projectSecurity.isProjectManager(#resourceAllocation.project.id)")
    public ResourceAllocationDTO createResourceAllocation(@RequestBody ResourceAllocation resourceAllocation) {
        ResourceAllocation savedAllocation = resourceAllocationRepository.save(resourceAllocation);
        return new ResourceAllocationDTO(savedAllocation);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<ResourceDTO> updateResource(@PathVariable Long id, @RequestBody Resource resourceDetails) {
        Resource resource = resourceRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Resource not found with id: " + id));

        resource.setName(resourceDetails.getName());
        resource.setDescription(resourceDetails.getDescription());
        resource.setType(resourceDetails.getType());
        resource.setAvailable(resourceDetails.isAvailable());

        Resource updatedResource = resourceRepository.save(resource);
        return ResponseEntity.ok(new ResourceDTO(updatedResource));
    }

    @PutMapping("/allocations/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('DEPARTMENT_HEAD') or @projectSecurity.isProjectManager(#resourceAllocation.project.id)")
    public ResponseEntity<ResourceAllocationDTO> updateResourceAllocation(@PathVariable Long id, @RequestBody ResourceAllocation resourceAllocationDetails) {
        ResourceAllocation resourceAllocation = resourceAllocationRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Resource allocation not found with id: " + id));

        resourceAllocation.setResource(resourceAllocationDetails.getResource());
        resourceAllocation.setProject(resourceAllocationDetails.getProject());
        resourceAllocation.setTask(resourceAllocationDetails.getTask());
        resourceAllocation.setStartDate(resourceAllocationDetails.getStartDate());
        resourceAllocation.setEndDate(resourceAllocationDetails.getEndDate());
        resourceAllocation.setQuantity(resourceAllocationDetails.getQuantity());
        resourceAllocation.setCost(resourceAllocationDetails.getCost());

        ResourceAllocation updatedResourceAllocation = resourceAllocationRepository.save(resourceAllocation);
        return ResponseEntity.ok(new ResourceAllocationDTO(updatedResourceAllocation));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<Map<String, Boolean>> deleteResource(@PathVariable Long id) {
        Resource resource = resourceRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Resource not found with id: " + id));

        // Check if resource has allocations
        List<ResourceAllocation> allocations = resourceAllocationRepository.findByResourceId(id);
        if (!allocations.isEmpty()) {
            throw new RuntimeException("Cannot delete resource with allocations");
        }

        resourceRepository.delete(resource);
        Map<String, Boolean> response = new HashMap<>();
        response.put("deleted", Boolean.TRUE);
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/allocations/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('DEPARTMENT_HEAD') or @projectSecurity.isProjectManager(#resourceAllocation.project.id)")
    public ResponseEntity<Map<String, Boolean>> deleteResourceAllocation(@PathVariable Long id) {
        ResourceAllocation resourceAllocation = resourceAllocationRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Resource allocation not found with id: " + id));

        resourceAllocationRepository.delete(resourceAllocation);
        Map<String, Boolean> response = new HashMap<>();
        response.put("deleted", Boolean.TRUE);
        return ResponseEntity.ok(response);
    }
}
