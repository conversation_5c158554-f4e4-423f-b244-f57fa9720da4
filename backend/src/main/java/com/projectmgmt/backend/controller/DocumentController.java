package com.projectmgmt.backend.controller;

import com.projectmgmt.backend.exception.ResourceNotFoundException;
import com.projectmgmt.backend.model.Document;
import com.projectmgmt.backend.model.User;
import com.projectmgmt.backend.repository.DocumentRepository;
import com.projectmgmt.backend.repository.ProjectRepository;
import com.projectmgmt.backend.repository.TaskRepository;
import com.projectmgmt.backend.repository.UserRepository;
import com.projectmgmt.backend.security.DocumentSecurity;
import com.projectmgmt.backend.security.ProjectSecurity;
import com.projectmgmt.backend.security.TaskSecurity;
import com.projectmgmt.backend.security.services.UserDetailsImpl;
import com.projectmgmt.backend.service.DocumentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/documents")
public class DocumentController {
    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ProjectSecurity projectSecurity;

    @Autowired
    private TaskSecurity taskSecurity;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private TaskRepository taskRepository;

    @Value("${file.upload-dir}")
    private String uploadDir;

    @GetMapping
    public List<Document> getAllDocuments() {
        return documentRepository.findAll();
    }

    @GetMapping("/project/{projectId}")
    public List<Document> getDocumentsByProject(@PathVariable Long projectId) {
        return documentRepository.findByProjectId(projectId);
    }

    @GetMapping("/task/{taskId}")
    public List<Document> getDocumentsByTask(@PathVariable Long taskId) {
        return documentRepository.findByTaskId(taskId);
    }

    @GetMapping("/user/{userId}")
    public List<Document> getDocumentsByUser(@PathVariable Long userId) {
        return documentRepository.findByUploadedById(userId);
    }

    @GetMapping("/type/{fileType}")
    public List<Document> getDocumentsByType(@PathVariable String fileType) {
        return documentRepository.findByFileType(fileType);
    }

    @GetMapping("/search")
    public List<Document> searchDocuments(@RequestParam String name) {
        return documentRepository.findByNameContaining(name);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Document> getDocumentById(@PathVariable Long id) {
        Document document = documentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Document not found with id: " + id));
        return ResponseEntity.ok(document);
    }

    @PostMapping("/upload")
    public Document uploadDocument(
            @RequestParam("file") MultipartFile file,
            @RequestParam("name") String name,
            @RequestParam("version") String version,
            @RequestParam(value = "projectId", required = false) Long projectId,
            @RequestParam(value = "taskId", required = false) Long taskId) throws IOException {

        // Create upload directory if it doesn't exist
        Path uploadPath = Paths.get(uploadDir);
        if (!Files.exists(uploadPath)) {
            Files.createDirectories(uploadPath);
        }

        // Generate unique filename
        String originalFilename = file.getOriginalFilename();
        String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
        String uniqueFilename = UUID.randomUUID().toString() + fileExtension;

        // Save file to disk
        Path filePath = uploadPath.resolve(uniqueFilename);
        Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);

        // Create document record
        Document document = new Document();
        document.setName(name);
        document.setFilePath(uniqueFilename);
        document.setFileType(fileExtension.substring(1));
        document.setVersion(version);

        // Set project and task if provided
        if (projectId != null) {
            document.setProject(projectRepository.findById(projectId)
                    .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + projectId)));
        }

        if (taskId != null) {
            document.setTask(taskRepository.findById(taskId)
                    .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + taskId)));
        }

        // Set uploaded by
        UserDetailsImpl userDetails = (UserDetailsImpl) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        User user = userRepository.findById(userDetails.getId())
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userDetails.getId()));
        document.setUploadedBy(user);

        return documentRepository.save(document);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or @documentSecurity.isDocumentOwner(#id)")
    public ResponseEntity<Map<String, Boolean>> deleteDocument(@PathVariable Long id) throws IOException {
        Document document = documentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Document not found with id: " + id));

        // Delete file from disk
        Path filePath = Paths.get(uploadDir).resolve(document.getFilePath());
        Files.deleteIfExists(filePath);

        // Delete document record
        documentRepository.delete(document);

        Map<String, Boolean> response = new HashMap<>();
        response.put("deleted", Boolean.TRUE);
        return ResponseEntity.ok(response);
    }
}
