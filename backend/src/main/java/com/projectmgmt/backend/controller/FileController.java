package com.projectmgmt.backend.controller;

import com.projectmgmt.backend.dto.DocumentDTO;
import com.projectmgmt.backend.exception.ResourceNotFoundException;
import com.projectmgmt.backend.model.Document;
import com.projectmgmt.backend.model.Project;
import com.projectmgmt.backend.model.User;
import com.projectmgmt.backend.repository.DocumentRepository;
import com.projectmgmt.backend.repository.ProjectRepository;
import com.projectmgmt.backend.repository.UserRepository;
import com.projectmgmt.backend.security.services.UserDetailsImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/files")
public class FileController {

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Value("${file.upload-dir:uploads}")
    private String uploadDir;

    @GetMapping
    public ResponseEntity<List<DocumentDTO>> getAllFiles() {
        try {
            List<Document> documents = documentRepository.findAll();
            List<DocumentDTO> documentDTOs = documents.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
            return ResponseEntity.ok(documentDTOs);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @PostMapping("/upload")
    public ResponseEntity<DocumentDTO> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "projectId", required = false) String projectIdStr,
            @RequestParam(value = "category", required = false, defaultValue = "OTHER") String category,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "accessLevel", required = false, defaultValue = "PROJECT") String accessLevel,
            @RequestParam(value = "tags", required = false) String tagsJson) {

        try {
            // Validate file
            if (file.isEmpty()) {
                return ResponseEntity.badRequest().build();
            }

            // Create upload directory if it doesn't exist
            Path uploadPath = Paths.get(uploadDir);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }

            // Generate unique filename
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null) {
                return ResponseEntity.badRequest().build();
            }

            String fileExtension = "";
            int lastDotIndex = originalFilename.lastIndexOf(".");
            if (lastDotIndex > 0) {
                fileExtension = originalFilename.substring(lastDotIndex);
            }
            String uniqueFilename = UUID.randomUUID().toString() + fileExtension;

            // Save file to disk
            Path filePath = uploadPath.resolve(uniqueFilename);
            Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);

            // Get current user
            UserDetailsImpl userDetails = (UserDetailsImpl) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
            User user = userRepository.findById(userDetails.getId())
                    .orElseThrow(() -> new ResourceNotFoundException("User not found"));

            // Create document record
            Document document = new Document();
            document.setName(originalFilename);
            document.setFilePath(uniqueFilename);
            document.setFileType(getFileType(fileExtension));
            document.setVersion("1.0");
            document.setUploadedBy(user);
            document.setSize(file.getSize());

            // Set project if provided
            if (projectIdStr != null && !projectIdStr.isEmpty()) {
                try {
                    Long projectId = Long.parseLong(projectIdStr);
                    Project project = projectRepository.findById(projectId)
                            .orElseThrow(() -> new ResourceNotFoundException("Project not found"));
                    document.setProject(project);
                } catch (NumberFormatException e) {
                    // Invalid project ID, continue without project
                }
            }

            // Set additional fields
            document.setDescription(description);
            // Note: You may need to add these fields to the Document entity
            // document.setCategory(category);
            // document.setAccessLevel(accessLevel);

            Document savedDocument = documentRepository.save(document);
            return ResponseEntity.ok(convertToDTO(savedDocument));

        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/{id}/download")
    public ResponseEntity<Resource> downloadFile(@PathVariable Long id) {
        try {
            Document document = documentRepository.findById(id)
                    .orElseThrow(() -> new ResourceNotFoundException("File not found"));

            Path filePath = Paths.get(uploadDir).resolve(document.getFilePath());
            Resource resource = new UrlResource(filePath.toUri());

            if (resource.exists() && resource.isReadable()) {
                return ResponseEntity.ok()
                        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + document.getName() + "\"")
                        .body(resource);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Boolean>> deleteFile(@PathVariable Long id) {
        try {
            Document document = documentRepository.findById(id)
                    .orElseThrow(() -> new ResourceNotFoundException("File not found"));

            // Delete file from disk
            Path filePath = Paths.get(uploadDir).resolve(document.getFilePath());
            Files.deleteIfExists(filePath);

            // Delete document record
            documentRepository.delete(document);

            Map<String, Boolean> response = new HashMap<>();
            response.put("deleted", Boolean.TRUE);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Boolean> response = new HashMap<>();
            response.put("deleted", Boolean.FALSE);
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @PostMapping("/batch-delete")
    public ResponseEntity<Map<String, Boolean>> batchDeleteFiles(@RequestBody List<Long> fileIds) {
        try {
            for (Long id : fileIds) {
                try {
                    Document document = documentRepository.findById(id).orElse(null);
                    if (document != null) {
                        // Delete file from disk
                        Path filePath = Paths.get(uploadDir).resolve(document.getFilePath());
                        Files.deleteIfExists(filePath);
                        
                        // Delete document record
                        documentRepository.delete(document);
                    }
                } catch (Exception e) {
                    // Continue with other files even if one fails
                    e.printStackTrace();
                }
            }

            Map<String, Boolean> response = new HashMap<>();
            response.put("deleted", Boolean.TRUE);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            Map<String, Boolean> response = new HashMap<>();
            response.put("deleted", Boolean.FALSE);
            return ResponseEntity.internalServerError().body(response);
        }
    }

    @GetMapping("/{id}/content")
    public ResponseEntity<String> getFileContent(@PathVariable Long id) {
        try {
            Document document = documentRepository.findById(id)
                    .orElseThrow(() -> new ResourceNotFoundException("File not found"));

            Path filePath = Paths.get(uploadDir).resolve(document.getFilePath());
            
            // Only allow text files
            String fileType = document.getFileType().toLowerCase();
            if (fileType.equals("txt") || fileType.equals("md") || fileType.equals("json") || 
                fileType.equals("xml") || fileType.equals("csv") || fileType.equals("log")) {
                
                String content = Files.readString(filePath);
                return ResponseEntity.ok(content);
            } else {
                return ResponseEntity.badRequest().body("File type not supported for content preview");
            }

        } catch (Exception e) {
            return ResponseEntity.internalServerError().body("Error reading file content");
        }
    }

    // Helper methods
    private DocumentDTO convertToDTO(Document document) {
        DocumentDTO dto = new DocumentDTO();
        dto.setId(document.getId());
        dto.setName(document.getName());
        dto.setFileType(document.getFileType());
        dto.setVersion(document.getVersion());
        dto.setSize(document.getSize());
        dto.setCreatedAt(document.getCreatedAt());
        dto.setUpdatedAt(document.getUpdatedAt());
        dto.setDescription(document.getDescription());
        
        if (document.getUploadedBy() != null) {
            Map<String, Object> uploadedBy = new HashMap<>();
            uploadedBy.put("id", document.getUploadedBy().getId());
            uploadedBy.put("firstName", document.getUploadedBy().getFirstName());
            uploadedBy.put("lastName", document.getUploadedBy().getLastName());
            uploadedBy.put("username", document.getUploadedBy().getUsername());
            dto.setUploadedBy(uploadedBy);
        }
        
        if (document.getProject() != null) {
            Map<String, Object> project = new HashMap<>();
            project.put("id", document.getProject().getId());
            project.put("name", document.getProject().getName());
            dto.setProject(project);
        }
        
        // Set file URL for preview
        dto.setUrl("/api/files/" + document.getId() + "/download");
        
        return dto;
    }

    private String getFileType(String fileExtension) {
        if (fileExtension == null || fileExtension.isEmpty()) {
            return "OTHER";
        }
        
        String ext = fileExtension.toLowerCase();
        if (ext.matches("\\.(jpg|jpeg|png|gif|bmp|webp)")) {
            return "IMAGE";
        } else if (ext.matches("\\.(mp4|avi|mov|wmv|flv|webm)")) {
            return "VIDEO";
        } else if (ext.matches("\\.(mp3|wav|flac|aac|ogg)")) {
            return "AUDIO";
        } else if (ext.matches("\\.(pdf|doc|docx|txt|md|rtf)")) {
            return "DOCUMENT";
        } else if (ext.matches("\\.(zip|rar|7z|tar|gz)")) {
            return "ARCHIVE";
        } else {
            return "OTHER";
        }
    }
}
