package com.projectmgmt.backend.controller;

import com.projectmgmt.backend.exception.ResourceNotFoundException;
import com.projectmgmt.backend.model.Task;
import com.projectmgmt.backend.model.TaskReport;
import com.projectmgmt.backend.model.User;
import com.projectmgmt.backend.repository.TaskReportRepository;
import com.projectmgmt.backend.repository.TaskRepository;
import com.projectmgmt.backend.repository.UserRepository;
import com.projectmgmt.backend.security.services.UserDetailsImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/task-reports")
public class TaskReportController {

    @Autowired
    private TaskReportRepository taskReportRepository;

    @Autowired
    private TaskRepository taskRepository;

    @Autowired
    private UserRepository userRepository;

    @GetMapping
    public List<TaskReport> getAllTaskReports() {
        return taskReportRepository.findAll();
    }

    @GetMapping("/{id}")
    public ResponseEntity<TaskReport> getTaskReportById(@PathVariable Long id) {
        TaskReport taskReport = taskReportRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Task report not found with id: " + id));
        return ResponseEntity.ok(taskReport);
    }

    @GetMapping("/task/{taskId}")
    public List<TaskReport> getTaskReportsByTask(@PathVariable Long taskId) {
        return taskReportRepository.findByTaskId(taskId);
    }

    @GetMapping("/user/{userId}")
    public List<TaskReport> getTaskReportsByUser(@PathVariable Long userId) {
        return taskReportRepository.findByUserId(userId);
    }

    @GetMapping("/date-range")
    public List<TaskReport> getTaskReportsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        return taskReportRepository.findByCreatedAtBetween(startDate, endDate);
    }

    @GetMapping("/my-reports")
    @PreAuthorize("isAuthenticated()")
    public List<TaskReport> getMyTaskReports() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        UserDetailsImpl userDetails = (UserDetailsImpl) authentication.getPrincipal();
        return taskReportRepository.findByUserId(userDetails.getId());
    }

    @PostMapping
    public TaskReport createTaskReport(@RequestBody TaskReport taskReport) {
        // 驗證任務是否存在
        if (taskReport.getTask() != null && taskReport.getTask().getId() != null) {
            Task task = taskRepository.findById(taskReport.getTask().getId())
                    .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + taskReport.getTask().getId()));
            taskReport.setTask(task);

            // 更新任務進度
            if (taskReport.getProgressUpdate() != null) {
                task.setProgress(taskReport.getProgressUpdate());
                taskRepository.save(task);
            }
        }

        // 設置當前用戶
        User user = userRepository.findById(1L) // 暫時使用固定的用戶ID
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: 1"));
        taskReport.setUser(user);

        return taskReportRepository.save(taskReport);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or @userSecurity.isOwner(#id, 'TaskReport')")
    public ResponseEntity<TaskReport> updateTaskReport(@PathVariable Long id, @RequestBody TaskReport taskReportDetails) {
        TaskReport taskReport = taskReportRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Task report not found with id: " + id));

        taskReport.setContent(taskReportDetails.getContent());
        taskReport.setProgressUpdate(taskReportDetails.getProgressUpdate());
        taskReport.setIssues(taskReportDetails.getIssues());

        // 更新任務進度
        if (taskReportDetails.getProgressUpdate() != null) {
            Task task = taskReport.getTask();
            task.setProgress(taskReportDetails.getProgressUpdate());
            taskRepository.save(task);
        }

        TaskReport updatedTaskReport = taskReportRepository.save(taskReport);
        return ResponseEntity.ok(updatedTaskReport);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or @userSecurity.isOwner(#id, 'TaskReport')")
    public ResponseEntity<Map<String, Boolean>> deleteTaskReport(@PathVariable Long id) {
        TaskReport taskReport = taskReportRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Task report not found with id: " + id));

        taskReportRepository.delete(taskReport);
        Map<String, Boolean> response = new HashMap<>();
        response.put("deleted", Boolean.TRUE);
        return ResponseEntity.ok(response);
    }
}
