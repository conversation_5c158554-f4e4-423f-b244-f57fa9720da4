package com.projectmgmt.backend.controller;

import com.projectmgmt.backend.service.ResourceAvailabilityService;
import com.projectmgmt.backend.service.ResourceAvailabilityService.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;

@RestController
@RequestMapping("/api/resource-availability")
@CrossOrigin(origins = "*")
public class ResourceAvailabilityController {

    @Autowired
    private ResourceAvailabilityService resourceAvailabilityService;

    /**
     * 檢查資源可用性
     */
    @GetMapping("/check")
    public ResponseEntity<ResourceAvailabilityResult> checkResourceAvailability(
            @RequestParam Long resourceId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam BigDecimal requiredQuantity) {
        
        ResourceAvailabilityResult result = resourceAvailabilityService.checkResourceAvailability(
                resourceId, startDate, endDate, requiredQuantity);
        
        return ResponseEntity.ok(result);
    }

    /**
     * 檢查專案資源需求
     */
    @GetMapping("/project/{projectId}/check")
    public ResponseEntity<ProjectResourceCheckResult> checkProjectResourceRequirements(
            @PathVariable Long projectId) {
        
        ProjectResourceCheckResult result = resourceAvailabilityService.checkProjectResourceRequirements(projectId);
        
        return ResponseEntity.ok(result);
    }

    /**
     * 檢查任務資源需求
     */
    @GetMapping("/task/{taskId}/check")
    public ResponseEntity<TaskResourceCheckResult> checkTaskResourceRequirements(
            @PathVariable Long taskId) {
        
        TaskResourceCheckResult result = resourceAvailabilityService.checkTaskResourceRequirements(taskId);
        
        return ResponseEntity.ok(result);
    }

    /**
     * 獲取資源使用統計
     */
    @GetMapping("/statistics/{resourceId}")
    public ResponseEntity<ResourceUsageStatistics> getResourceUsageStatistics(
            @PathVariable Long resourceId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        ResourceUsageStatistics stats = resourceAvailabilityService.getResourceUsageStatistics(
                resourceId, startDate, endDate);
        
        return ResponseEntity.ok(stats);
    }

    /**
     * 批量檢查多個專案的資源需求
     */
    @PostMapping("/projects/batch-check")
    public ResponseEntity<java.util.List<ProjectResourceCheckResult>> batchCheckProjectsResourceRequirements(
            @RequestBody java.util.List<Long> projectIds) {
        
        java.util.List<ProjectResourceCheckResult> results = projectIds.stream()
                .map(resourceAvailabilityService::checkProjectResourceRequirements)
                .collect(java.util.stream.Collectors.toList());
        
        return ResponseEntity.ok(results);
    }

    /**
     * 批量檢查多個任務的資源需求
     */
    @PostMapping("/tasks/batch-check")
    public ResponseEntity<java.util.List<TaskResourceCheckResult>> batchCheckTasksResourceRequirements(
            @RequestBody java.util.List<Long> taskIds) {
        
        java.util.List<TaskResourceCheckResult> results = taskIds.stream()
                .map(resourceAvailabilityService::checkTaskResourceRequirements)
                .collect(java.util.stream.Collectors.toList());
        
        return ResponseEntity.ok(results);
    }

    /**
     * 獲取資源在指定日期範圍內的詳細分配情況
     */
    @GetMapping("/resource/{resourceId}/allocations")
    public ResponseEntity<java.util.List<com.projectmgmt.backend.model.ResourceAllocation>> getResourceAllocations(
            @PathVariable Long resourceId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        java.util.List<com.projectmgmt.backend.model.ResourceAllocation> allocations = 
                resourceAvailabilityService.getResourceAllocationsInDateRange(resourceId, startDate, endDate);
        
        return ResponseEntity.ok(allocations);
    }
}
