package com.projectmgmt.backend.controller;

import com.projectmgmt.backend.exception.ResourceNotFoundException;
import com.projectmgmt.backend.model.ContactRecord;
import com.projectmgmt.backend.model.Customer;
import com.projectmgmt.backend.model.User;
import com.projectmgmt.backend.repository.ContactRecordRepository;
import com.projectmgmt.backend.repository.CustomerRepository;
import com.projectmgmt.backend.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/contact-records")
public class ContactRecordController {

    @Autowired
    private ContactRecordRepository contactRecordRepository;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private UserRepository userRepository;

    @GetMapping
    public List<ContactRecord> getAllContactRecords() {
        return contactRecordRepository.findAll();
    }

    @GetMapping("/{id}")
    public ResponseEntity<ContactRecord> getContactRecordById(@PathVariable Long id) {
        ContactRecord contactRecord = contactRecordRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Contact record not found with id: " + id));
        return ResponseEntity.ok(contactRecord);
    }

    @GetMapping("/customer/{customerId}")
    public List<ContactRecord> getContactRecordsByCustomer(@PathVariable Long customerId) {
        return contactRecordRepository.findByCustomerId(customerId);
    }

    @GetMapping("/user/{userId}")
    public List<ContactRecord> getContactRecordsByUser(@PathVariable Long userId) {
        return contactRecordRepository.findByUserId(userId);
    }

    @GetMapping("/type/{contactType}")
    public List<ContactRecord> getContactRecordsByType(@PathVariable ContactRecord.ContactType contactType) {
        return contactRecordRepository.findByContactType(contactType);
    }

    @GetMapping("/date-range")
    public List<ContactRecord> getContactRecordsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        return contactRecordRepository.findByCreatedAtBetween(startDate, endDate);
    }

    @PostMapping
    public ContactRecord createContactRecord(@RequestBody ContactRecord contactRecord) {
        return contactRecordRepository.save(contactRecord);
    }

    @PutMapping("/{id}")
    public ResponseEntity<ContactRecord> updateContactRecord(@PathVariable Long id, @RequestBody ContactRecord contactRecordDetails) {
        ContactRecord contactRecord = contactRecordRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Contact record not found with id: " + id));

        contactRecord.setContactType(contactRecordDetails.getContactType());
        contactRecord.setContent(contactRecordDetails.getContent());

        if (contactRecordDetails.getCustomer() != null && contactRecordDetails.getCustomer().getId() != null) {
            Customer customer = customerRepository.findById(contactRecordDetails.getCustomer().getId())
                    .orElseThrow(() -> new ResourceNotFoundException("Customer not found with id: " + contactRecordDetails.getCustomer().getId()));
            contactRecord.setCustomer(customer);
        }

        if (contactRecordDetails.getUser() != null && contactRecordDetails.getUser().getId() != null) {
            User user = userRepository.findById(contactRecordDetails.getUser().getId())
                    .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + contactRecordDetails.getUser().getId()));
            contactRecord.setUser(user);
        }

        ContactRecord updatedContactRecord = contactRecordRepository.save(contactRecord);
        return ResponseEntity.ok(updatedContactRecord);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Boolean>> deleteContactRecord(@PathVariable Long id) {
        ContactRecord contactRecord = contactRecordRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Contact record not found with id: " + id));

        contactRecordRepository.delete(contactRecord);
        Map<String, Boolean> response = new HashMap<>();
        response.put("deleted", Boolean.TRUE);
        return ResponseEntity.ok(response);
    }
}
