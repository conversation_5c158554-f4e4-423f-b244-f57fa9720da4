package com.projectmgmt.backend.controller;

import com.projectmgmt.backend.exception.ResourceNotFoundException;
import com.projectmgmt.backend.model.Settings;
import com.projectmgmt.backend.repository.SettingsRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/settings")
public class SettingsController {

    @Autowired
    private SettingsRepository settingsRepository;

    @GetMapping
    public List<Settings> getAllSettings() {
        return settingsRepository.findAll();
    }

    @GetMapping("/{id}")
    public ResponseEntity<Settings> getSettingsById(@PathVariable Long id) {
        Settings settings = settingsRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Settings not found with id: " + id));
        return ResponseEntity.ok(settings);
    }

    @GetMapping("/key/{key}")
    public ResponseEntity<Settings> getSettingsByKey(@PathVariable String key) {
        Settings settings = settingsRepository.findByKey(key)
                .orElseThrow(() -> new ResourceNotFoundException("Settings not found with key: " + key));
        return ResponseEntity.ok(settings);
    }

    @GetMapping("/category/{category}")
    public List<Settings> getSettingsByCategory(@PathVariable String category) {
        return settingsRepository.findByCategory(category);
    }

    @GetMapping("/system")
    public List<Settings> getSystemSettings() {
        return settingsRepository.findByIsSystem(true);
    }

    @GetMapping("/custom")
    public List<Settings> getCustomSettings() {
        return settingsRepository.findByIsSystem(false);
    }

    @PostMapping
    public Settings createSettings(@RequestBody Settings settings) {
        return settingsRepository.save(settings);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<Settings> updateSettings(@PathVariable Long id, @RequestBody Settings settingsDetails) {
        Settings settings = settingsRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Settings not found with id: " + id));

        settings.setKey(settingsDetails.getKey());
        settings.setValue(settingsDetails.getValue());
        settings.setDescription(settingsDetails.getDescription());
        settings.setCategory(settingsDetails.getCategory());
        settings.setSystem(settingsDetails.isSystem());

        Settings updatedSettings = settingsRepository.save(settings);
        return ResponseEntity.ok(updatedSettings);
    }

    @PutMapping("/key/{key}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<Settings> updateSettingsByKey(@PathVariable String key, @RequestBody Map<String, String> valueMap) {
        Settings settings = settingsRepository.findByKey(key)
                .orElseThrow(() -> new ResourceNotFoundException("Settings not found with key: " + key));

        if (valueMap.containsKey("value")) {
            settings.setValue(valueMap.get("value"));
        }

        Settings updatedSettings = settingsRepository.save(settings);
        return ResponseEntity.ok(updatedSettings);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<Map<String, Boolean>> deleteSettings(@PathVariable Long id) {
        Settings settings = settingsRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Settings not found with id: " + id));

        // 系統設置不允許刪除
        if (settings.isSystem()) {
            throw new RuntimeException("System settings cannot be deleted");
        }

        settingsRepository.delete(settings);
        Map<String, Boolean> response = new HashMap<>();
        response.put("deleted", Boolean.TRUE);
        return ResponseEntity.ok(response);
    }
}
