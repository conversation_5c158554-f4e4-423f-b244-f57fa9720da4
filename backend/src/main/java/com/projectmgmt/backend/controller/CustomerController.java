package com.projectmgmt.backend.controller;

import com.projectmgmt.backend.dto.CustomerDTO;
import com.projectmgmt.backend.dto.ProjectDTO;
import com.projectmgmt.backend.exception.ResourceNotFoundException;
import com.projectmgmt.backend.model.ContactRecord;
import com.projectmgmt.backend.model.Customer;
import com.projectmgmt.backend.model.Project;
import com.projectmgmt.backend.repository.ContactRecordRepository;
import com.projectmgmt.backend.repository.CustomerRepository;
import com.projectmgmt.backend.repository.ProjectRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/customers")
public class CustomerController {
    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private ContactRecordRepository contactRecordRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @GetMapping
    public List<CustomerDTO> getAllCustomers() {
        return customerRepository.findAll().stream()
                .map(CustomerDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/search")
    public List<CustomerDTO> searchCustomers(@RequestParam String name) {
        return customerRepository.findByNameContaining(name).stream()
                .map(CustomerDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/type/{typeId}")
    public List<CustomerDTO> getCustomersByType(@PathVariable Long typeId) {
        return customerRepository.findByCustomerTypeId(typeId).stream()
                .map(CustomerDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/tag/{tagId}")
    public List<CustomerDTO> getCustomersByTag(@PathVariable Long tagId) {
        return customerRepository.findByTagsId(tagId).stream()
                .map(CustomerDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/{id}")
    public ResponseEntity<CustomerDTO> getCustomerById(@PathVariable Long id) {
        Customer customer = customerRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Customer not found with id: " + id));
        return ResponseEntity.ok(new CustomerDTO(customer));
    }

    @GetMapping("/{id}/contacts")
    public List<ContactRecord> getCustomerContacts(@PathVariable Long id) {
        return contactRecordRepository.findByCustomerId(id);
    }

    @GetMapping("/{id}/projects")
    public List<ProjectDTO> getCustomerProjects(@PathVariable Long id) {
        return projectRepository.findByCustomerId(id).stream()
                .map(ProjectDTO::new)
                .collect(Collectors.toList());
    }

    @PostMapping
    public CustomerDTO createCustomer(@RequestBody Customer customer) {
        Customer savedCustomer = customerRepository.save(customer);
        return new CustomerDTO(savedCustomer);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('DEPARTMENT_HEAD')")
    public ResponseEntity<CustomerDTO> updateCustomer(@PathVariable Long id, @RequestBody Customer customerDetails) {
        Customer customer = customerRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Customer not found with id: " + id));

        customer.setName(customerDetails.getName());
        customer.setContactPerson(customerDetails.getContactPerson());
        customer.setPhone(customerDetails.getPhone());
        customer.setEmail(customerDetails.getEmail());
        customer.setAddress(customerDetails.getAddress());
        customer.setCustomerType(customerDetails.getCustomerType());

        if (customerDetails.getTags() != null) {
            customer.setTags(customerDetails.getTags());
        }

        Customer updatedCustomer = customerRepository.save(customer);
        return ResponseEntity.ok(new CustomerDTO(updatedCustomer));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<Map<String, Boolean>> deleteCustomer(@PathVariable Long id) {
        Customer customer = customerRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Customer not found with id: " + id));

        // Check if customer has projects
        List<Project> projects = projectRepository.findByCustomerId(id);
        if (!projects.isEmpty()) {
            throw new RuntimeException("Cannot delete customer with associated projects");
        }

        customerRepository.delete(customer);
        Map<String, Boolean> response = new HashMap<>();
        response.put("deleted", Boolean.TRUE);
        return ResponseEntity.ok(response);
    }
}
