package com.projectmgmt.backend.controller;

import com.projectmgmt.backend.exception.ResourceNotFoundException;
import com.projectmgmt.backend.model.CustomerType;
import com.projectmgmt.backend.repository.CustomerTypeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/customer-types")
@CrossOrigin(origins = "*", maxAge = 3600)
public class CustomerTypeController {
    
    @Autowired
    private CustomerTypeRepository customerTypeRepository;
    
    @GetMapping
    public List<CustomerType> getAllCustomerTypes() {
        return customerTypeRepository.findAll();
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<CustomerType> getCustomerTypeById(@PathVariable Long id) {
        CustomerType customerType = customerTypeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("CustomerType not found with id: " + id));
        return ResponseEntity.ok(customerType);
    }
    
    @PostMapping
    public CustomerType createCustomerType(@RequestBody CustomerType customerType) {
        return customerTypeRepository.save(customerType);
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<CustomerType> updateCustomerType(@PathVariable Long id, @RequestBody CustomerType customerTypeDetails) {
        CustomerType customerType = customerTypeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("CustomerType not found with id: " + id));
        
        customerType.setName(customerTypeDetails.getName());
        customerType.setDescription(customerTypeDetails.getDescription());
        
        CustomerType updatedCustomerType = customerTypeRepository.save(customerType);
        return ResponseEntity.ok(updatedCustomerType);
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Boolean>> deleteCustomerType(@PathVariable Long id) {
        CustomerType customerType = customerTypeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("CustomerType not found with id: " + id));
        
        customerTypeRepository.delete(customerType);
        Map<String, Boolean> response = new HashMap<>();
        response.put("deleted", Boolean.TRUE);
        return ResponseEntity.ok(response);
    }
}
