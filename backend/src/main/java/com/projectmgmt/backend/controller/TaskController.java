package com.projectmgmt.backend.controller;

import com.projectmgmt.backend.exception.ResourceNotFoundException;
import com.projectmgmt.backend.model.Document;
import com.projectmgmt.backend.model.Task;
import com.projectmgmt.backend.model.TaskReport;
import com.projectmgmt.backend.repository.DocumentRepository;
import com.projectmgmt.backend.repository.TaskReportRepository;
import com.projectmgmt.backend.repository.TaskRepository;
import com.projectmgmt.backend.security.ProjectSecurity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/tasks")
public class TaskController {
    @Autowired
    private TaskRepository taskRepository;

    @Autowired
    private TaskReportRepository taskReportRepository;

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private ProjectSecurity projectSecurity;

    @GetMapping
    public List<Task> getAllTasks() {
        return taskRepository.findAll();
    }

    @GetMapping("/project/{projectId}")
    public List<Task> getTasksByProject(@PathVariable Long projectId) {
        return taskRepository.findByProjectId(projectId);
    }

    @GetMapping("/assignee/{assigneeId}")
    public List<Task> getTasksByAssignee(@PathVariable Long assigneeId) {
        return taskRepository.findByAssigneeId(assigneeId);
    }

    @GetMapping("/status/{status}")
    public List<Task> getTasksByStatus(@PathVariable Task.TaskStatus status) {
        return taskRepository.findByStatus(status);
    }

    @GetMapping("/priority/{priority}")
    public List<Task> getTasksByPriority(@PathVariable Task.TaskPriority priority) {
        return taskRepository.findByPriority(priority);
    }

    @GetMapping("/due-before")
    public List<Task> getTasksDueBefore(@RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        return taskRepository.findByDueDateBefore(date);
    }

    @GetMapping("/due-between")
    public List<Task> getTasksDueBetween(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        return taskRepository.findByDueDateBetween(startDate, endDate);
    }

    @GetMapping("/parent/{parentId}")
    public List<Task> getSubtasks(@PathVariable Long parentId) {
        return taskRepository.findByParentId(parentId);
    }

    @GetMapping("/root")
    public List<Task> getRootTasks() {
        return taskRepository.findByParentIsNull();
    }

    @GetMapping("/{id}")
    public ResponseEntity<Task> getTaskById(@PathVariable Long id) {
        Task task = taskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + id));
        return ResponseEntity.ok(task);
    }

    @GetMapping("/{id}/reports")
    public List<TaskReport> getTaskReports(@PathVariable Long id) {
        return taskReportRepository.findByTaskId(id);
    }

    @GetMapping("/{id}/documents")
    public List<Document> getTaskDocuments(@PathVariable Long id) {
        return documentRepository.findByTaskId(id);
    }

    @PostMapping
    public Task createTask(@RequestBody Task task) {
        return taskRepository.save(task);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('DEPARTMENT_HEAD') or @projectSecurity.isProjectManager(#task.project.id) or @taskSecurity.isTaskAssignee(#id)")
    public ResponseEntity<Task> updateTask(@PathVariable Long id, @RequestBody Task taskDetails) {
        Task task = taskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + id));

        task.setName(taskDetails.getName());
        task.setDescription(taskDetails.getDescription());
        task.setStartDate(taskDetails.getStartDate());
        task.setDueDate(taskDetails.getDueDate());
        task.setStatus(taskDetails.getStatus());
        task.setPriority(taskDetails.getPriority());
        task.setProgress(taskDetails.getProgress());
        task.setAssignee(taskDetails.getAssignee());
        task.setParent(taskDetails.getParent());

        if (taskDetails.getDependencies() != null) {
            task.setDependencies(taskDetails.getDependencies());
        }

        Task updatedTask = taskRepository.save(task);
        return ResponseEntity.ok(updatedTask);
    }

    @PutMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('DEPARTMENT_HEAD') or @projectSecurity.isProjectManager(#task.project.id) or @taskSecurity.isTaskAssignee(#id)")
    public ResponseEntity<Task> updateTaskStatus(@PathVariable Long id, @RequestBody Map<String, String> statusUpdate) {
        Task task = taskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + id));

        task.setStatus(Task.TaskStatus.valueOf(statusUpdate.get("status")));
        Task updatedTask = taskRepository.save(task);
        return ResponseEntity.ok(updatedTask);
    }

    @PutMapping("/{id}/progress")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('DEPARTMENT_HEAD') or @projectSecurity.isProjectManager(#task.project.id) or @taskSecurity.isTaskAssignee(#id)")
    public ResponseEntity<Task> updateTaskProgress(@PathVariable Long id, @RequestBody Map<String, Integer> progressUpdate) {
        Task task = taskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + id));

        task.setProgress(progressUpdate.get("progress"));
        Task updatedTask = taskRepository.save(task);
        return ResponseEntity.ok(updatedTask);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or @projectSecurity.isProjectManager(#task.project.id)")
    public ResponseEntity<Map<String, Boolean>> deleteTask(@PathVariable Long id) {
        Task task = taskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + id));

        // Check if task has subtasks
        List<Task> subtasks = taskRepository.findByParentId(id);
        if (!subtasks.isEmpty()) {
            throw new RuntimeException("Cannot delete task with subtasks");
        }

        taskRepository.delete(task);
        Map<String, Boolean> response = new HashMap<>();
        response.put("deleted", Boolean.TRUE);
        return ResponseEntity.ok(response);
    }
}
