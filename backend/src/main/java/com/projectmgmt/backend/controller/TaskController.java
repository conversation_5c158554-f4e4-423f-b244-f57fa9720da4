package com.projectmgmt.backend.controller;

import com.projectmgmt.backend.dto.DocumentDTO;
import com.projectmgmt.backend.dto.TaskDTO;
import com.projectmgmt.backend.exception.ResourceNotFoundException;
import com.projectmgmt.backend.model.Task;
import com.projectmgmt.backend.model.TaskReport;
import com.projectmgmt.backend.repository.DocumentRepository;
import com.projectmgmt.backend.repository.TaskReportRepository;
import com.projectmgmt.backend.repository.TaskRepository;
import com.projectmgmt.backend.security.ProjectSecurity;
import com.projectmgmt.backend.service.TaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/tasks")
public class TaskController {
    @Autowired
    private TaskRepository taskRepository;

    @Autowired
    private TaskReportRepository taskReportRepository;

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private ProjectSecurity projectSecurity;

    @Autowired
    private TaskService taskService;

    @GetMapping
    public List<TaskDTO> getAllTasks() {
        return taskRepository.findAll().stream()
                .map(TaskDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/project/{projectId}")
    public List<TaskDTO> getTasksByProject(@PathVariable Long projectId) {
        return taskRepository.findByProjectId(projectId).stream()
                .map(TaskDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/assignee/{assigneeId}")
    public List<TaskDTO> getTasksByAssignee(@PathVariable Long assigneeId) {
        return taskRepository.findByAssigneeId(assigneeId).stream()
                .map(TaskDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/status/{status}")
    public List<TaskDTO> getTasksByStatus(@PathVariable Task.TaskStatus status) {
        return taskRepository.findByStatus(status).stream()
                .map(TaskDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/priority/{priority}")
    public List<TaskDTO> getTasksByPriority(@PathVariable Task.TaskPriority priority) {
        return taskRepository.findByPriority(priority).stream()
                .map(TaskDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/due-before")
    public List<TaskDTO> getTasksDueBefore(@RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {
        return taskRepository.findByDueDateBefore(date).stream()
                .map(TaskDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/due-between")
    public List<TaskDTO> getTasksDueBetween(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        return taskRepository.findByDueDateBetween(startDate, endDate).stream()
                .map(TaskDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/parent/{parentId}")
    public List<TaskDTO> getSubtasks(@PathVariable Long parentId) {
        return taskRepository.findByParentId(parentId).stream()
                .map(TaskDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/root")
    public List<TaskDTO> getRootTasks() {
        return taskRepository.findByParentIsNull().stream()
                .map(TaskDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/{id}")
    public ResponseEntity<TaskDTO> getTaskById(@PathVariable Long id) {
        Task task = taskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + id));
        return ResponseEntity.ok(new TaskDTO(task));
    }

    @GetMapping("/{id}/reports")
    public List<TaskReport> getTaskReports(@PathVariable Long id) {
        return taskReportRepository.findByTaskId(id);
    }

    @GetMapping("/{id}/documents")
    public List<DocumentDTO> getTaskDocuments(@PathVariable Long id) {
        return documentRepository.findByTaskId(id).stream()
                .map(DocumentDTO::new)
                .collect(Collectors.toList());
    }

    @PostMapping
    public TaskDTO createTask(@RequestBody Task task) {
        Task savedTask = taskRepository.save(task);
        return new TaskDTO(savedTask);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('DEPARTMENT_HEAD') or @projectSecurity.isProjectManager(#task.project.id) or @taskSecurity.isTaskAssignee(#id)")
    public ResponseEntity<TaskDTO> updateTask(@PathVariable Long id, @RequestBody Task taskDetails) {
        Task task = taskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + id));

        task.setName(taskDetails.getName());
        task.setDescription(taskDetails.getDescription());
        task.setStartDate(taskDetails.getStartDate());
        task.setDueDate(taskDetails.getDueDate());
        task.setStatus(taskDetails.getStatus());
        task.setPriority(taskDetails.getPriority());
        task.setProgress(taskDetails.getProgress());
        task.setAssignee(taskDetails.getAssignee());
        task.setParent(taskDetails.getParent());

        if (taskDetails.getDependencies() != null) {
            task.setDependencies(taskDetails.getDependencies());
        }

        Task updatedTask = taskRepository.save(task);
        return ResponseEntity.ok(new TaskDTO(updatedTask));
    }

    @PutMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('DEPARTMENT_HEAD') or @projectSecurity.isProjectManager(#task.project.id) or @taskSecurity.isTaskAssignee(#id)")
    public ResponseEntity<TaskDTO> updateTaskStatus(@PathVariable Long id, @RequestBody Map<String, String> statusUpdate) {
        Task task = taskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + id));

        task.setStatus(Task.TaskStatus.valueOf(statusUpdate.get("status")));
        Task updatedTask = taskRepository.save(task);
        return ResponseEntity.ok(new TaskDTO(updatedTask));
    }

    @PutMapping("/{id}/progress")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('DEPARTMENT_HEAD') or @projectSecurity.isProjectManager(#task.project.id) or @taskSecurity.isTaskAssignee(#id)")
    @Deprecated
    public ResponseEntity<TaskDTO> updateTaskProgress(@PathVariable Long id, @RequestBody Map<String, Integer> progressUpdate) {
        Task task = taskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + id));

        task.setProgress(progressUpdate.get("progress"));
        Task updatedTask = taskRepository.save(task);
        return ResponseEntity.ok(new TaskDTO(updatedTask));
    }

    @PostMapping("/{id}/calculate-progress")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('DEPARTMENT_HEAD') or @projectSecurity.isProjectManager(#task.project.id) or @taskSecurity.isTaskAssignee(#id)")
    public ResponseEntity<TaskDTO> calculateAutomaticProgress(@PathVariable Long id) {
        Task updatedTask = taskService.calculateAutomaticProgress(id);
        return ResponseEntity.ok(new TaskDTO(updatedTask));
    }

    @GetMapping("/{id}/progress-analysis")
    public ResponseEntity<Map<String, Object>> getTaskProgressAnalysis(@PathVariable Long id) {
        Map<String, Object> analysis = taskService.getTaskProgressAnalysis(id);
        return ResponseEntity.ok(analysis);
    }

    @GetMapping("/{id}/time-based-progress")
    public ResponseEntity<Map<String, Integer>> getTimeBasedProgress(@PathVariable Long id) {
        Integer timeBasedProgress = taskService.calculateTimeBasedProgress(id);
        Map<String, Integer> response = new HashMap<>();
        response.put("timeBasedProgress", timeBasedProgress);
        return ResponseEntity.ok(response);
    }



    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or @projectSecurity.isProjectManager(#task.project.id)")
    public ResponseEntity<Map<String, Boolean>> deleteTask(@PathVariable Long id) {
        Task task = taskRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Task not found with id: " + id));

        // Check if task has subtasks
        List<Task> subtasks = taskRepository.findByParentId(id);
        if (!subtasks.isEmpty()) {
            throw new RuntimeException("Cannot delete task with subtasks");
        }

        taskRepository.delete(task);
        Map<String, Boolean> response = new HashMap<>();
        response.put("deleted", Boolean.TRUE);
        return ResponseEntity.ok(response);
    }
}
