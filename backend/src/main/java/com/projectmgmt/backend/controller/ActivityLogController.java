package com.projectmgmt.backend.controller;

import com.projectmgmt.backend.exception.ResourceNotFoundException;
import com.projectmgmt.backend.model.ActivityLog;
import com.projectmgmt.backend.model.User;
import com.projectmgmt.backend.repository.ActivityLogRepository;
import com.projectmgmt.backend.repository.UserRepository;
import com.projectmgmt.backend.security.UserSecurity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/activity-logs")
public class ActivityLogController {
    @Autowired
    private ActivityLogRepository activityLogRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private UserSecurity userSecurity;

    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public List<ActivityLog> getAllActivityLogs() {
        return activityLogRepository.findAll();
    }
    
    @GetMapping("/user/{userId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('DEPARTMENT_HEAD') or @userSecurity.isCurrentUser(#userId)")
    public List<ActivityLog> getUserActivityLogs(@PathVariable Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));
        
        return activityLogRepository.findByUserId(userId);
    }
    
    @GetMapping("/type/{activityType}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public List<ActivityLog> getActivityLogsByType(@PathVariable ActivityLog.ActivityType activityType) {
        return activityLogRepository.findByActivityType(activityType);
    }
    
    @GetMapping("/date-range")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public List<ActivityLog> getActivityLogsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        return activityLogRepository.findByCreatedAtBetween(startDate, endDate);
    }
    
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or @activityLogSecurity.isActivityLogOwner(#id)")
    public ResponseEntity<ActivityLog> getActivityLogById(@PathVariable Long id) {
        ActivityLog activityLog = activityLogRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Activity log not found with id: " + id));
        
        return ResponseEntity.ok(activityLog);
    }
}
