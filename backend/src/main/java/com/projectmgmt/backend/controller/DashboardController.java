package com.projectmgmt.backend.controller;

import com.projectmgmt.backend.model.Project;
import com.projectmgmt.backend.model.Task;
import com.projectmgmt.backend.repository.DepartmentRepository;
import com.projectmgmt.backend.repository.ProjectRepository;
import com.projectmgmt.backend.repository.TaskRepository;
import com.projectmgmt.backend.repository.UserRepository;
import com.projectmgmt.backend.security.services.UserDetailsImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/dashboard")
public class DashboardController {
    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private TaskRepository taskRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private DepartmentRepository departmentRepository;

    @GetMapping("/summary")
    @PreAuthorize("isAuthenticated()")
    public Map<String, Object> getDashboardSummary() {
        Map<String, Object> summary = new HashMap<>();

        // Get current user
        UserDetailsImpl userDetails = (UserDetailsImpl) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Long userId = userDetails.getId();

        // Projects summary
        List<Project> allProjects = projectRepository.findAll();
        List<Project> userProjects = projectRepository.findByMembersId(userId);
        List<Project> managedProjects = projectRepository.findByManagerId(userId);

        summary.put("totalProjects", allProjects.size());
        summary.put("userProjects", userProjects.size());
        summary.put("managedProjects", managedProjects.size());

        // Project status counts
        Map<Project.ProjectStatus, Long> projectStatusCounts = allProjects.stream()
                .collect(Collectors.groupingBy(Project::getStatus, Collectors.counting()));
        summary.put("projectStatusCounts", projectStatusCounts);

        // Tasks summary
        List<Task> allTasks = taskRepository.findAll();
        List<Task> userTasks = taskRepository.findByAssigneeId(userId);
        List<Task> overdueTasks = taskRepository.findByDueDateBefore(LocalDate.now()).stream()
                .filter(task -> task.getStatus() != Task.TaskStatus.COMPLETED)
                .collect(Collectors.toList());
        List<Task> userOverdueTasks = overdueTasks.stream()
                .filter(task -> task.getAssignee() != null && task.getAssignee().getId().equals(userId))
                .collect(Collectors.toList());

        summary.put("totalTasks", allTasks.size());
        summary.put("userTasks", userTasks.size());
        summary.put("overdueTasks", overdueTasks.size());
        summary.put("userOverdueTasks", userOverdueTasks.size());

        // Task status counts
        Map<Task.TaskStatus, Long> taskStatusCounts = allTasks.stream()
                .collect(Collectors.groupingBy(Task::getStatus, Collectors.counting()));
        summary.put("taskStatusCounts", taskStatusCounts);

        // User task status counts
        Map<Task.TaskStatus, Long> userTaskStatusCounts = userTasks.stream()
                .collect(Collectors.groupingBy(Task::getStatus, Collectors.counting()));
        summary.put("userTaskStatusCounts", userTaskStatusCounts);

        // Organization summary
        summary.put("totalUsers", userRepository.count());
        summary.put("totalDepartments", departmentRepository.count());

        return summary;
    }

    @GetMapping("/projects")
    @PreAuthorize("isAuthenticated()")
    public Map<String, Object> getProjectsDashboard() {
        Map<String, Object> dashboard = new HashMap<>();

        // Get current user
        UserDetailsImpl userDetails = (UserDetailsImpl) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Long userId = userDetails.getId();

        // Recent projects
        List<Project> recentProjects = projectRepository.findAll().stream()
                .sorted((p1, p2) -> p2.getCreatedAt().compareTo(p1.getCreatedAt()))
                .limit(5)
                .collect(Collectors.toList());

        // User's projects
        List<Project> userProjects = projectRepository.findByMembersId(userId);

        // Projects by status
        Map<Project.ProjectStatus, List<Project>> projectsByStatus = projectRepository.findAll().stream()
                .collect(Collectors.groupingBy(Project::getStatus));

        dashboard.put("recentProjects", recentProjects);
        dashboard.put("userProjects", userProjects);
        dashboard.put("projectsByStatus", projectsByStatus);

        return dashboard;
    }

    @GetMapping("/tasks")
    @PreAuthorize("isAuthenticated()")
    public Map<String, Object> getTasksDashboard() {
        Map<String, Object> dashboard = new HashMap<>();

        // Get current user
        UserDetailsImpl userDetails = (UserDetailsImpl) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        Long userId = userDetails.getId();

        // User's tasks
        List<Task> userTasks = taskRepository.findByAssigneeId(userId);

        // Due soon tasks (due in the next 7 days)
        LocalDate today = LocalDate.now();
        LocalDate nextWeek = today.plusDays(7);
        List<Task> dueSoonTasks = userTasks.stream()
                .filter(task -> task.getStatus() != Task.TaskStatus.COMPLETED)
                .filter(task -> task.getDueDate().isAfter(today) && task.getDueDate().isBefore(nextWeek))
                .collect(Collectors.toList());

        // Overdue tasks
        List<Task> overdueTasks = userTasks.stream()
                .filter(task -> task.getStatus() != Task.TaskStatus.COMPLETED)
                .filter(task -> task.getDueDate().isBefore(today))
                .collect(Collectors.toList());

        // Tasks by priority
        Map<Task.TaskPriority, List<Task>> tasksByPriority = userTasks.stream()
                .collect(Collectors.groupingBy(Task::getPriority));

        dashboard.put("userTasks", userTasks);
        dashboard.put("dueSoonTasks", dueSoonTasks);
        dashboard.put("overdueTasks", overdueTasks);
        dashboard.put("tasksByPriority", tasksByPriority);

        return dashboard;
    }
}
