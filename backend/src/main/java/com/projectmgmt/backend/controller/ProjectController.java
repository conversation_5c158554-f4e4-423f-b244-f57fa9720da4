package com.projectmgmt.backend.controller;

import com.projectmgmt.backend.dto.DocumentDTO;
import com.projectmgmt.backend.dto.ProjectDTO;
import com.projectmgmt.backend.dto.ResourceAllocationDTO;
import com.projectmgmt.backend.dto.TaskDTO;
import com.projectmgmt.backend.exception.ResourceNotFoundException;
import com.projectmgmt.backend.model.Document;
import com.projectmgmt.backend.model.Project;
import com.projectmgmt.backend.model.ResourceAllocation;
import com.projectmgmt.backend.model.Task;
import com.projectmgmt.backend.repository.DocumentRepository;
import com.projectmgmt.backend.repository.ProjectRepository;
import com.projectmgmt.backend.repository.ResourceAllocationRepository;
import com.projectmgmt.backend.repository.TaskRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/projects")
public class ProjectController {
    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private TaskRepository taskRepository;

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private ResourceAllocationRepository resourceAllocationRepository;

    @GetMapping
    public List<ProjectDTO> getAllProjects() {
        return projectRepository.findAll().stream()
                .map(ProjectDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/status/{status}")
    public List<ProjectDTO> getProjectsByStatus(@PathVariable Project.ProjectStatus status) {
        return projectRepository.findByStatus(status).stream()
                .map(ProjectDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/manager/{managerId}")
    public List<ProjectDTO> getProjectsByManager(@PathVariable Long managerId) {
        return projectRepository.findByManagerId(managerId).stream()
                .map(ProjectDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/member/{memberId}")
    public List<ProjectDTO> getProjectsByMember(@PathVariable Long memberId) {
        return projectRepository.findByMembersId(memberId).stream()
                .map(ProjectDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/customer/{customerId}")
    public List<ProjectDTO> getProjectsByCustomer(@PathVariable Long customerId) {
        return projectRepository.findByCustomerId(customerId).stream()
                .map(ProjectDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/date-range")
    public List<ProjectDTO> getProjectsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        return projectRepository.findByStartDateBetween(startDate, endDate).stream()
                .map(ProjectDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/search")
    public List<ProjectDTO> searchProjects(@RequestParam String name) {
        return projectRepository.findByNameContaining(name).stream()
                .map(ProjectDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/{id}")
    public ResponseEntity<ProjectDTO> getProjectById(@PathVariable Long id) {
        Project project = projectRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + id));
        return ResponseEntity.ok(new ProjectDTO(project));
    }

    @GetMapping("/{id}/tasks")
    public List<TaskDTO> getProjectTasks(@PathVariable Long id) {
        return taskRepository.findByProjectId(id).stream()
                .map(TaskDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/{id}/documents")
    public List<DocumentDTO> getProjectDocuments(@PathVariable Long id) {
        return documentRepository.findByProjectId(id).stream()
                .map(DocumentDTO::new)
                .collect(Collectors.toList());
    }

    @GetMapping("/{id}/resources")
    public List<ResourceAllocationDTO> getProjectResources(@PathVariable Long id) {
        return resourceAllocationRepository.findByProjectId(id).stream()
                .map(ResourceAllocationDTO::new)
                .collect(Collectors.toList());
    }

    @PostMapping
    public ProjectDTO createProject(@RequestBody Project project) {
        Project savedProject = projectRepository.save(project);
        return new ProjectDTO(savedProject);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('DEPARTMENT_HEAD') or @projectSecurity.isProjectManager(#id)")
    public ResponseEntity<ProjectDTO> updateProject(@PathVariable Long id, @RequestBody Project projectDetails) {
        Project project = projectRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + id));

        project.setName(projectDetails.getName());
        project.setDescription(projectDetails.getDescription());
        project.setStartDate(projectDetails.getStartDate());
        project.setEndDate(projectDetails.getEndDate());
        project.setStatus(projectDetails.getStatus());
        project.setProgress(projectDetails.getProgress());
        project.setCustomer(projectDetails.getCustomer());
        project.setManager(projectDetails.getManager());
        project.setMembers(projectDetails.getMembers());
        project.setBudget(projectDetails.getBudget());
        project.setCost(projectDetails.getCost());

        Project updatedProject = projectRepository.save(project);
        return ResponseEntity.ok(new ProjectDTO(updatedProject));
    }

    @PutMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('DEPARTMENT_HEAD') or @projectSecurity.isProjectManager(#id)")
    public ResponseEntity<ProjectDTO> updateProjectStatus(@PathVariable Long id, @RequestBody Map<String, String> statusUpdate) {
        Project project = projectRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + id));

        project.setStatus(Project.ProjectStatus.valueOf(statusUpdate.get("status")));
        Project updatedProject = projectRepository.save(project);
        return ResponseEntity.ok(new ProjectDTO(updatedProject));
    }

    @PostMapping("/{id}/recalculate-progress")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('DEPARTMENT_HEAD') or @projectSecurity.isProjectManager(#id)")
    public ResponseEntity<Map<String, Object>> recalculateProjectProgress(@PathVariable Long id) {
        Project project = projectRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + id));

        int oldProgress = project.getProgress();

        // Trigger progress recalculation
        List<Task> tasks = taskRepository.findByProjectId(id);

        // Calculate progress details
        Map<String, Object> progressDetails = new HashMap<>();
        progressDetails.put("oldProgress", oldProgress);
        progressDetails.put("taskCount", tasks.size());

        if (!tasks.isEmpty()) {
            double totalWeightedProgress = 0.0;
            double totalWeight = 0.0;

            for (Task task : tasks) {
                double weight = getTaskWeight(task);
                totalWeightedProgress += (task.getProgress() * weight);
                totalWeight += weight;
            }

            int newProgress = totalWeight > 0 ?
                (int) Math.round(totalWeightedProgress / totalWeight) : 0;

            project.setProgress(newProgress);
            projectRepository.save(project);

            progressDetails.put("newProgress", newProgress);
            progressDetails.put("totalWeight", totalWeight);
            progressDetails.put("weightedAverage", totalWeightedProgress / totalWeight);
        } else {
            progressDetails.put("newProgress", oldProgress);
            progressDetails.put("totalWeight", 0);
            progressDetails.put("weightedAverage", 0);
        }

        return ResponseEntity.ok(progressDetails);
    }

    @GetMapping("/{id}/progress-details")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('DEPARTMENT_HEAD') or @projectSecurity.isProjectManager(#id) or @projectSecurity.isProjectMember(#id)")
    public ResponseEntity<Map<String, Object>> getProjectProgressDetails(@PathVariable Long id) {
        Project project = projectRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + id));

        List<Task> tasks = taskRepository.findByProjectId(id);

        Map<String, Object> details = new HashMap<>();
        details.put("projectId", id);
        details.put("projectName", project.getName());
        details.put("currentProgress", project.getProgress());
        details.put("totalTasks", tasks.size());
        details.put("completedTasks", tasks.stream().mapToInt(t -> t.getProgress() == 100 ? 1 : 0).sum());
        details.put("averageTaskProgress", tasks.isEmpty() ? 0 :
            tasks.stream().mapToInt(Task::getProgress).average().orElse(0));

        // Task breakdown by priority
        Map<String, Integer> tasksByPriority = new HashMap<>();
        Map<String, Double> progressByPriority = new HashMap<>();

        for (Task task : tasks) {
            String priority = task.getPriority() != null ? task.getPriority().toString() : "NONE";
            tasksByPriority.put(priority, tasksByPriority.getOrDefault(priority, 0) + 1);
            progressByPriority.put(priority,
                progressByPriority.getOrDefault(priority, 0.0) + task.getProgress());
        }

        // Calculate average progress by priority
        progressByPriority.replaceAll((k, v) ->
            tasksByPriority.get(k) > 0 ? v / tasksByPriority.get(k) : 0);

        details.put("tasksByPriority", tasksByPriority);
        details.put("progressByPriority", progressByPriority);
        details.put("isAutoCalculated", true);
        details.put("lastCalculated", project.getUpdatedAt());

        return ResponseEntity.ok(details);
    }

    private double getTaskWeight(Task task) {
        double baseWeight = 1.0;

        if (task.getPriority() != null) {
            switch (task.getPriority()) {
                case URGENT:
                    baseWeight = 2.0;
                    break;
                case HIGH:
                    baseWeight = 1.5;
                    break;
                case MEDIUM:
                    baseWeight = 1.0;
                    break;
                case LOW:
                    baseWeight = 0.7;
                    break;
            }
        }

        return baseWeight;
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<Map<String, Boolean>> deleteProject(@PathVariable Long id) {
        Project project = projectRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + id));

        // Check if project has tasks
        List<Task> tasks = taskRepository.findByProjectId(id);
        if (!tasks.isEmpty()) {
            throw new RuntimeException("Cannot delete project with associated tasks");
        }

        projectRepository.delete(project);
        Map<String, Boolean> response = new HashMap<>();
        response.put("deleted", Boolean.TRUE);
        return ResponseEntity.ok(response);
    }
}
