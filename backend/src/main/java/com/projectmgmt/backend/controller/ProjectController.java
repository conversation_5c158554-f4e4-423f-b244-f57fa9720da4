package com.projectmgmt.backend.controller;

import com.projectmgmt.backend.exception.ResourceNotFoundException;
import com.projectmgmt.backend.model.Document;
import com.projectmgmt.backend.model.Project;
import com.projectmgmt.backend.model.ResourceAllocation;
import com.projectmgmt.backend.model.Task;
import com.projectmgmt.backend.repository.DocumentRepository;
import com.projectmgmt.backend.repository.ProjectRepository;
import com.projectmgmt.backend.repository.ResourceAllocationRepository;
import com.projectmgmt.backend.repository.TaskRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/projects")
public class ProjectController {
    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private TaskRepository taskRepository;

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private ResourceAllocationRepository resourceAllocationRepository;

    @GetMapping
    public List<Project> getAllProjects() {
        return projectRepository.findAll();
    }

    @GetMapping("/status/{status}")
    public List<Project> getProjectsByStatus(@PathVariable Project.ProjectStatus status) {
        return projectRepository.findByStatus(status);
    }

    @GetMapping("/manager/{managerId}")
    public List<Project> getProjectsByManager(@PathVariable Long managerId) {
        return projectRepository.findByManagerId(managerId);
    }

    @GetMapping("/member/{memberId}")
    public List<Project> getProjectsByMember(@PathVariable Long memberId) {
        return projectRepository.findByMembersId(memberId);
    }

    @GetMapping("/customer/{customerId}")
    public List<Project> getProjectsByCustomer(@PathVariable Long customerId) {
        return projectRepository.findByCustomerId(customerId);
    }

    @GetMapping("/date-range")
    public List<Project> getProjectsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        return projectRepository.findByStartDateBetween(startDate, endDate);
    }

    @GetMapping("/search")
    public List<Project> searchProjects(@RequestParam String name) {
        return projectRepository.findByNameContaining(name);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Project> getProjectById(@PathVariable Long id) {
        Project project = projectRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + id));
        return ResponseEntity.ok(project);
    }

    @GetMapping("/{id}/tasks")
    public List<Task> getProjectTasks(@PathVariable Long id) {
        return taskRepository.findByProjectId(id);
    }

    @GetMapping("/{id}/documents")
    public List<Document> getProjectDocuments(@PathVariable Long id) {
        return documentRepository.findByProjectId(id);
    }

    @GetMapping("/{id}/resources")
    public List<ResourceAllocation> getProjectResources(@PathVariable Long id) {
        return resourceAllocationRepository.findByProjectId(id);
    }

    @PostMapping
    public Project createProject(@RequestBody Project project) {
        return projectRepository.save(project);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('DEPARTMENT_HEAD') or @projectSecurity.isProjectManager(#id)")
    public ResponseEntity<Project> updateProject(@PathVariable Long id, @RequestBody Project projectDetails) {
        Project project = projectRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + id));

        project.setName(projectDetails.getName());
        project.setDescription(projectDetails.getDescription());
        project.setStartDate(projectDetails.getStartDate());
        project.setEndDate(projectDetails.getEndDate());
        project.setStatus(projectDetails.getStatus());
        project.setProgress(projectDetails.getProgress());
        project.setCustomer(projectDetails.getCustomer());
        project.setManager(projectDetails.getManager());
        project.setMembers(projectDetails.getMembers());
        project.setBudget(projectDetails.getBudget());
        project.setCost(projectDetails.getCost());

        Project updatedProject = projectRepository.save(project);
        return ResponseEntity.ok(updatedProject);
    }

    @PutMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('DEPARTMENT_HEAD') or @projectSecurity.isProjectManager(#id)")
    public ResponseEntity<Project> updateProjectStatus(@PathVariable Long id, @RequestBody Map<String, String> statusUpdate) {
        Project project = projectRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + id));

        project.setStatus(Project.ProjectStatus.valueOf(statusUpdate.get("status")));
        Project updatedProject = projectRepository.save(project);
        return ResponseEntity.ok(updatedProject);
    }

    @PutMapping("/{id}/progress")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN') or hasRole('DEPARTMENT_HEAD') or @projectSecurity.isProjectManager(#id)")
    public ResponseEntity<Project> updateProjectProgress(@PathVariable Long id, @RequestBody Map<String, Integer> progressUpdate) {
        Project project = projectRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + id));

        project.setProgress(progressUpdate.get("progress"));
        Project updatedProject = projectRepository.save(project);
        return ResponseEntity.ok(updatedProject);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<Map<String, Boolean>> deleteProject(@PathVariable Long id) {
        Project project = projectRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Project not found with id: " + id));

        // Check if project has tasks
        List<Task> tasks = taskRepository.findByProjectId(id);
        if (!tasks.isEmpty()) {
            throw new RuntimeException("Cannot delete project with associated tasks");
        }

        projectRepository.delete(project);
        Map<String, Boolean> response = new HashMap<>();
        response.put("deleted", Boolean.TRUE);
        return ResponseEntity.ok(response);
    }
}
