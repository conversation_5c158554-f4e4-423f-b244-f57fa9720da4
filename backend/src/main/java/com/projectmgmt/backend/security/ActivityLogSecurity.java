package com.projectmgmt.backend.security;

import com.projectmgmt.backend.model.ActivityLog;
import com.projectmgmt.backend.repository.ActivityLogRepository;
import com.projectmgmt.backend.security.services.UserDetailsImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component("activityLogSecurity")
public class ActivityLogSecurity {
    
    @Autowired
    private ActivityLogRepository activityLogRepository;
    
    public boolean isActivityLogOwner(Long activityLogId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            return false;
        }
        
        Object principal = authentication.getPrincipal();
        if (!(principal instanceof UserDetailsImpl)) {
            return false;
        }
        
        UserDetailsImpl userDetails = (UserDetailsImpl) principal;
        Optional<ActivityLog> activityLog = activityLogRepository.findById(activityLogId);
        
        if (!activityLog.isPresent() || activityLog.get().getUser() == null) {
            return false;
        }
        
        return activityLog.get().getUser().getId().equals(userDetails.getId());
    }
}
