package com.projectmgmt.backend.security;

import com.projectmgmt.backend.model.Notification;
import com.projectmgmt.backend.repository.NotificationRepository;
import com.projectmgmt.backend.security.services.UserDetailsImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component("notificationSecurity")
public class NotificationSecurity {
    
    @Autowired
    private NotificationRepository notificationRepository;
    
    public boolean isNotificationRecipient(Long notificationId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            return false;
        }
        
        Object principal = authentication.getPrincipal();
        if (!(principal instanceof UserDetailsImpl)) {
            return false;
        }
        
        UserDetailsImpl userDetails = (UserDetailsImpl) principal;
        Optional<Notification> notification = notificationRepository.findById(notificationId);
        
        if (!notification.isPresent()) {
            return false;
        }
        
        return notification.get().getUser().getId().equals(userDetails.getId());
    }
}
