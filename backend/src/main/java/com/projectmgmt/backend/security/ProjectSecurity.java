package com.projectmgmt.backend.security;

import com.projectmgmt.backend.model.Project;
import com.projectmgmt.backend.repository.ProjectRepository;
import com.projectmgmt.backend.security.services.UserDetailsImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component("projectSecurity")
public class ProjectSecurity {
    
    @Autowired
    private ProjectRepository projectRepository;
    
    public boolean isProjectManager(Long projectId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            return false;
        }
        
        Object principal = authentication.getPrincipal();
        if (!(principal instanceof UserDetailsImpl)) {
            return false;
        }
        
        UserDetailsImpl userDetails = (UserDetailsImpl) principal;
        Optional<Project> project = projectRepository.findById(projectId);
        
        if (!project.isPresent()) {
            return false;
        }
        
        return project.get().getManager().getId().equals(userDetails.getId());
    }
    
    public boolean isProjectMember(Long projectId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            return false;
        }
        
        Object principal = authentication.getPrincipal();
        if (!(principal instanceof UserDetailsImpl)) {
            return false;
        }
        
        UserDetailsImpl userDetails = (UserDetailsImpl) principal;
        Optional<Project> project = projectRepository.findById(projectId);
        
        if (!project.isPresent()) {
            return false;
        }
        
        return project.get().getMembers().stream()
                .anyMatch(member -> member.getId().equals(userDetails.getId()));
    }
}
