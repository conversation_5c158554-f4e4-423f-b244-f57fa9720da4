package com.projectmgmt.backend.security;

import com.projectmgmt.backend.model.Task;
import com.projectmgmt.backend.repository.TaskRepository;
import com.projectmgmt.backend.security.services.UserDetailsImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component("taskSecurity")
public class TaskSecurity {
    
    @Autowired
    private TaskRepository taskRepository;
    
    public boolean isTaskAssignee(Long taskId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            return false;
        }
        
        Object principal = authentication.getPrincipal();
        if (!(principal instanceof UserDetailsImpl)) {
            return false;
        }
        
        UserDetailsImpl userDetails = (UserDetailsImpl) principal;
        Optional<Task> task = taskRepository.findById(taskId);
        
        if (!task.isPresent() || task.get().getAssignee() == null) {
            return false;
        }
        
        return task.get().getAssignee().getId().equals(userDetails.getId());
    }
}
