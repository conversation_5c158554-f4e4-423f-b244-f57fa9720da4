package com.projectmgmt.backend.security;

import com.projectmgmt.backend.model.Document;
import com.projectmgmt.backend.repository.DocumentRepository;
import com.projectmgmt.backend.security.services.UserDetailsImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component("documentSecurity")
public class DocumentSecurity {
    
    @Autowired
    private DocumentRepository documentRepository;
    
    public boolean isDocumentOwner(Long documentId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            return false;
        }
        
        Object principal = authentication.getPrincipal();
        if (!(principal instanceof UserDetailsImpl)) {
            return false;
        }
        
        UserDetailsImpl userDetails = (UserDetailsImpl) principal;
        Optional<Document> document = documentRepository.findById(documentId);
        
        if (!document.isPresent() || document.get().getUploadedBy() == null) {
            return false;
        }
        
        return document.get().getUploadedBy().getId().equals(userDetails.getId());
    }
}
