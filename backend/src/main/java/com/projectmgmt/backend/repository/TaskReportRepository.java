package com.projectmgmt.backend.repository;

import com.projectmgmt.backend.model.TaskReport;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface TaskReportRepository extends JpaRepository<TaskReport, Long> {
    List<TaskReport> findByTaskId(Long taskId);

    List<TaskReport> findByTaskIdOrderByCreatedAtDesc(Long taskId);

    List<TaskReport> findByUserId(Long userId);

    List<TaskReport> findByCreatedAtBetween(LocalDateTime start, LocalDateTime end);
}
