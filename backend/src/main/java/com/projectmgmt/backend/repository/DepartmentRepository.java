package com.projectmgmt.backend.repository;

import com.projectmgmt.backend.model.Department;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DepartmentRepository extends JpaRepository<Department, Long> {
    Optional<Department> findByName(String name);
    
    List<Department> findByParentId(Long parentId);
    
    List<Department> findByParentIsNull();
    
    List<Department> findByActive(boolean active);
}
