package com.projectmgmt.backend.repository;

import com.projectmgmt.backend.model.Settings;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SettingsRepository extends JpaRepository<Settings, Long> {
    Optional<Settings> findByKey(String key);
    
    List<Settings> findByCategory(String category);
    
    List<Settings> findByIsSystem(boolean isSystem);
}
