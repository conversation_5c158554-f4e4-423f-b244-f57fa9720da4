package com.projectmgmt.backend.repository;

import com.projectmgmt.backend.model.Document;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DocumentRepository extends JpaRepository<Document, Long> {
    List<Document> findByProjectId(Long projectId);
    
    List<Document> findByTaskId(Long taskId);
    
    List<Document> findByUploadedById(Long uploadedById);
    
    List<Document> findByFileType(String fileType);
    
    List<Document> findByNameContaining(String name);
}
