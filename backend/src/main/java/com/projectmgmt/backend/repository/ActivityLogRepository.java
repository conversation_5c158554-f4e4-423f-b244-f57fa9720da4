package com.projectmgmt.backend.repository;

import com.projectmgmt.backend.model.ActivityLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ActivityLogRepository extends JpaRepository<ActivityLog, Long> {
    List<ActivityLog> findByUserId(Long userId);
    
    List<ActivityLog> findByActivityType(ActivityLog.ActivityType activityType);
    
    List<ActivityLog> findByCreatedAtBetween(LocalDateTime start, LocalDateTime end);
    
    List<ActivityLog> findByUserIdAndActivityType(Long userId, ActivityLog.ActivityType activityType);
}
