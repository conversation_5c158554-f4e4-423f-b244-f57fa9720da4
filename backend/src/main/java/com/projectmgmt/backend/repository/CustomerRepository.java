package com.projectmgmt.backend.repository;

import com.projectmgmt.backend.model.Customer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CustomerRepository extends JpaRepository<Customer, Long> {
    List<Customer> findByCustomerTypeId(Long customerTypeId);
    
    List<Customer> findByNameContaining(String name);
    
    List<Customer> findByTagsId(Long tagId);
}
