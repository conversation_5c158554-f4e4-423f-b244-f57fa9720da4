package com.projectmgmt.backend.repository;

import com.projectmgmt.backend.model.Task;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TaskRepository extends JpaRepository<Task, Long> {
    List<Task> findByProjectId(Long projectId);
    
    List<Task> findByAssigneeId(Long assigneeId);
    
    List<Task> findByStatus(Task.TaskStatus status);
    
    List<Task> findByPriority(Task.TaskPriority priority);
    
    List<Task> findByDueDateBefore(LocalDate date);
    
    List<Task> findByDueDateBetween(LocalDate start, LocalDate end);
    
    List<Task> findByParentId(Long parentId);
    
    List<Task> findByParentIsNull();
}
