package com.projectmgmt.backend.repository;

import com.projectmgmt.backend.model.ContactRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface ContactRecordRepository extends JpaRepository<ContactRecord, Long> {
    List<ContactRecord> findByCustomerId(Long customerId);
    
    List<ContactRecord> findByUserId(Long userId);
    
    List<ContactRecord> findByContactType(ContactRecord.ContactType contactType);
    
    List<ContactRecord> findByCreatedAtBetween(LocalDateTime start, LocalDateTime end);
}
