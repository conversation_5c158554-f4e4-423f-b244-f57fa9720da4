package com.projectmgmt.backend.repository;

import com.projectmgmt.backend.model.Project;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface ProjectRepository extends JpaRepository<Project, Long> {
    List<Project> findByStatus(Project.ProjectStatus status);
    
    List<Project> findByManagerId(Long managerId);
    
    List<Project> findByMembersId(Long memberId);
    
    List<Project> findByCustomerId(Long customerId);
    
    List<Project> findByStartDateBetween(LocalDate start, LocalDate end);
    
    List<Project> findByEndDateBetween(LocalDate start, LocalDate end);
    
    List<Project> findByNameContaining(String name);
}
