package com.projectmgmt.backend.repository;

import com.projectmgmt.backend.model.Resource;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ResourceRepository extends JpaRepository<Resource, Long> {
    List<Resource> findByType(Resource.ResourceType type);
    
    List<Resource> findByAvailable(boolean available);
    
    List<Resource> findByNameContaining(String name);
}
