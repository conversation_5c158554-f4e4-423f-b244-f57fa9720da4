package com.projectmgmt.backend.repository;

import com.projectmgmt.backend.model.Notification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface NotificationRepository extends JpaRepository<Notification, Long> {
    List<Notification> findByUserId(Long userId);
    
    List<Notification> findByUserIdAndRead(Long userId, boolean read);
    
    List<Notification> findByType(Notification.NotificationType type);
    
    List<Notification> findByCreatedAtBetween(LocalDateTime start, LocalDateTime end);
}
