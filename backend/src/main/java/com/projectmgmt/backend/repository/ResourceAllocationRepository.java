package com.projectmgmt.backend.repository;

import com.projectmgmt.backend.model.ResourceAllocation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface ResourceAllocationRepository extends JpaRepository<ResourceAllocation, Long> {
    List<ResourceAllocation> findByResourceId(Long resourceId);

    List<ResourceAllocation> findByProjectId(Long projectId);

    List<ResourceAllocation> findByTaskId(Long taskId);

    List<ResourceAllocation> findByStartDateBetween(LocalDate start, LocalDate end);

    List<ResourceAllocation> findByEndDateBetween(LocalDate start, LocalDate end);

    List<ResourceAllocation> findByStartDateLessThanEqualAndEndDateGreaterThanEqual(LocalDate date1, LocalDate date2);

    @Query("SELECT ra FROM ResourceAllocation ra WHERE ra.resource.id = :resourceId " +
           "AND ((ra.startDate <= :endDate) AND (ra.endDate >= :startDate))")
    List<ResourceAllocation> findByResourceIdAndDateRangeOverlap(
            @Param("resourceId") Long resourceId,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);

    @Query("SELECT ra FROM ResourceAllocation ra WHERE ra.resource.id = :resourceId " +
           "AND ra.startDate >= :startDate AND ra.endDate <= :endDate")
    List<ResourceAllocation> findByResourceIdAndDateRange(
            @Param("resourceId") Long resourceId,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);
}
