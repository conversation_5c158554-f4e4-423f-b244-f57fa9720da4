package com.projectmgmt.backend.repository;

import com.projectmgmt.backend.model.ResourceAllocation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface ResourceAllocationRepository extends JpaRepository<ResourceAllocation, Long> {
    List<ResourceAllocation> findByResourceId(Long resourceId);
    
    List<ResourceAllocation> findByProjectId(Long projectId);
    
    List<ResourceAllocation> findByTaskId(Long taskId);
    
    List<ResourceAllocation> findByStartDateBetween(LocalDate start, LocalDate end);
    
    List<ResourceAllocation> findByEndDateBetween(LocalDate start, LocalDate end);
    
    List<ResourceAllocation> findByStartDateLessThanEqualAndEndDateGreaterThanEqual(LocalDate date1, LocalDate date2);
}
