package com.projectmgmt.backend.dto;

import com.projectmgmt.backend.model.ResourceAllocation;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class ResourceAllocationDTO {
    private Long id;
    private ResourceSummaryDTO resource;
    private ProjectSummaryDTO project;
    private TaskSummaryDTO task;
    private LocalDate startDate;
    private LocalDate endDate;
    private BigDecimal quantity;
    private BigDecimal cost;
    private LocalDateTime createdAt;

    @Data
    public static class ResourceSummaryDTO {
        private Long id;
        private String name;
        private String type;
        private String description;

        public ResourceSummaryDTO(Long id, String name, String type, String description) {
            this.id = id;
            this.name = name;
            this.type = type;
            this.description = description;
        }
    }

    @Data
    public static class ProjectSummaryDTO {
        private Long id;
        private String name;

        public ProjectSummaryDTO(Long id, String name) {
            this.id = id;
            this.name = name;
        }
    }

    @Data
    public static class TaskSummaryDTO {
        private Long id;
        private String name;

        public TaskSummaryDTO(Long id, String name) {
            this.id = id;
            this.name = name;
        }
    }

    public ResourceAllocationDTO(ResourceAllocation allocation) {
        this.id = allocation.getId();
        this.startDate = allocation.getStartDate();
        this.endDate = allocation.getEndDate();
        this.quantity = allocation.getQuantity();
        this.cost = allocation.getCost();
        this.createdAt = allocation.getCreatedAt();

        if (allocation.getResource() != null) {
            this.resource = new ResourceSummaryDTO(
                allocation.getResource().getId(),
                allocation.getResource().getName(),
                allocation.getResource().getType() != null ? allocation.getResource().getType().toString() : null,
                allocation.getResource().getDescription()
            );
        }

        if (allocation.getProject() != null) {
            this.project = new ProjectSummaryDTO(
                allocation.getProject().getId(),
                allocation.getProject().getName()
            );
        }

        if (allocation.getTask() != null) {
            this.task = new TaskSummaryDTO(
                allocation.getTask().getId(),
                allocation.getTask().getName()
            );
        }
    }
}
