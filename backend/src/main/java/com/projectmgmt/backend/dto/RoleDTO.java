package com.projectmgmt.backend.dto;

import com.projectmgmt.backend.model.ERole;
import com.projectmgmt.backend.model.Role;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class RoleDTO {
    private Long id;
    private ERole name;
    private String displayName;

    public RoleDTO(Role role) {
        this.id = role.getId().longValue();
        this.name = role.getName();
        this.displayName = getRoleDisplayName(role.getName());
    }

    private String getRoleDisplayName(ERole role) {
        switch (role) {
            case ROLE_SUPER_ADMIN:
                return "超級管理員";
            case ROLE_ADMIN:
                return "管理員";
            case ROLE_DEPARTMENT_HEAD:
                return "部門主管";
            case ROLE_EMPLOYEE:
                return "員工";
            case ROLE_GUEST:
                return "訪客";
            default:
                return role.name();
        }
    }
}
