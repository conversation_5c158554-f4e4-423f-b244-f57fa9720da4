package com.projectmgmt.backend.dto;

import com.projectmgmt.backend.model.Resource;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class ResourceDTO {
    private Long id;
    private String name;
    private String description;
    private Resource.ResourceType type;
    private boolean available;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Statistics
    private int allocationCount;
    private int activeAllocationCount;
    private double utilizationRate;

    public ResourceDTO(Resource resource) {
        this.id = resource.getId();
        this.name = resource.getName();
        this.description = resource.getDescription();
        this.type = resource.getType();
        this.available = resource.isAvailable();
        this.createdAt = resource.getCreatedAt();
        this.updatedAt = resource.getUpdatedAt();
        
        // Calculate statistics
        if (resource.getAllocations() != null) {
            this.allocationCount = resource.getAllocations().size();
            this.activeAllocationCount = (int) resource.getAllocations().stream()
                    .filter(allocation -> {
                        LocalDateTime now = LocalDateTime.now();
                        return allocation.getStartDate() != null && allocation.getEndDate() != null &&
                               allocation.getStartDate().atStartOfDay().isBefore(now) &&
                               allocation.getEndDate().atStartOfDay().isAfter(now);
                    })
                    .count();
            
            // Calculate utilization rate (simplified calculation)
            this.utilizationRate = this.allocationCount > 0 ? 
                    (double) this.activeAllocationCount / this.allocationCount * 100 : 0.0;
        } else {
            this.allocationCount = 0;
            this.activeAllocationCount = 0;
            this.utilizationRate = 0.0;
        }
    }
}
