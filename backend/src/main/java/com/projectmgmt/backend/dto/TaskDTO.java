package com.projectmgmt.backend.dto;

import com.projectmgmt.backend.model.Task;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class TaskDTO {
    private Long id;
    private String name;
    private String description;
    private LocalDate startDate;
    private LocalDate dueDate;
    private Task.TaskStatus status;
    private Task.TaskPriority priority;
    private Integer progress;
    private ProjectSummaryDTO project;
    private UserSummaryDTO assignee;
    private TaskSummaryDTO parent;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    @Data
    public static class ProjectSummaryDTO {
        private Long id;
        private String name;
        private String description;

        public ProjectSummaryDTO(Long id, String name, String description) {
            this.id = id;
            this.name = name;
            this.description = description;
        }
    }

    @Data
    public static class UserSummaryDTO {
        private Long id;
        private String username;
        private String firstName;
        private String lastName;
        private String email;

        public UserSummaryDTO(Long id, String username, String firstName, String lastName, String email) {
            this.id = id;
            this.username = username;
            this.firstName = firstName;
            this.lastName = lastName;
            this.email = email;
        }
    }

    @Data
    public static class TaskSummaryDTO {
        private Long id;
        private String name;
        private Task.TaskStatus status;

        public TaskSummaryDTO(Long id, String name, Task.TaskStatus status) {
            this.id = id;
            this.name = name;
            this.status = status;
        }
    }

    public TaskDTO(Task task) {
        this.id = task.getId();
        this.name = task.getName();
        this.description = task.getDescription();
        this.startDate = task.getStartDate();
        this.dueDate = task.getDueDate();
        this.status = task.getStatus();
        this.priority = task.getPriority();
        this.progress = task.getProgress();
        this.createdAt = task.getCreatedAt();
        this.updatedAt = task.getUpdatedAt();
        
        if (task.getProject() != null) {
            this.project = new ProjectSummaryDTO(
                task.getProject().getId(),
                task.getProject().getName(),
                task.getProject().getDescription()
            );
        }
        
        if (task.getAssignee() != null) {
            this.assignee = new UserSummaryDTO(
                task.getAssignee().getId(),
                task.getAssignee().getUsername(),
                task.getAssignee().getFirstName(),
                task.getAssignee().getLastName(),
                task.getAssignee().getEmail()
            );
        }
        
        if (task.getParent() != null) {
            this.parent = new TaskSummaryDTO(
                task.getParent().getId(),
                task.getParent().getName(),
                task.getParent().getStatus()
            );
        }
    }
}
