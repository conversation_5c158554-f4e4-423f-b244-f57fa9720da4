package com.projectmgmt.backend.dto;

import com.projectmgmt.backend.model.User;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
public class UserDTO {
    private Long id;
    private String username;
    private String firstName;
    private String lastName;
    private String email;
    private boolean enabled;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Department info
    private DepartmentDTO department;

    // Role info
    private List<RoleDTO> roles;

    // Statistics
    private int managedProjectsCount;
    private int memberProjectsCount;
    private int assignedTasksCount;
    private int activityLogsCount;
    private LocalDateTime lastLoginAt;

    public UserDTO(User user) {
        this.id = user.getId();
        this.username = user.getUsername();
        this.firstName = user.getFirstName();
        this.lastName = user.getLastName();
        this.email = user.getEmail();
        this.enabled = user.isEnabled();
        this.createdAt = user.getCreatedAt();
        this.updatedAt = user.getUpdatedAt();

        // Department
        if (user.getDepartment() != null) {
            this.department = new DepartmentDTO(user.getDepartment());
        }

        // Roles
        if (user.getRoles() != null) {
            this.roles = user.getRoles().stream()
                    .map(RoleDTO::new)
                    .collect(Collectors.toList());
        }

        // Initialize statistics to 0 (will be calculated separately if needed)
        this.managedProjectsCount = 0;
        this.memberProjectsCount = 0;
        this.assignedTasksCount = 0;

        if (user.getActivityLogs() != null) {
            this.activityLogsCount = user.getActivityLogs().size();
            // Get last activity log time as last login approximation
            this.lastLoginAt = user.getActivityLogs().stream()
                    .map(log -> log.getCreatedAt())
                    .max(LocalDateTime::compareTo)
                    .orElse(null);
        } else {
            this.activityLogsCount = 0;
            this.lastLoginAt = null;
        }
    }

    public String getFullName() {
        return firstName + " " + lastName;
    }
}
