package com.projectmgmt.backend.dto;

import com.projectmgmt.backend.model.Department;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class DepartmentDTO {
    private Long id;
    private String name;
    private String description;
    private boolean active;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Parent department info (without children to avoid recursion)
    private Long parentId;
    private String parentName;
    
    // Statistics
    private int userCount;
    private int childDepartmentCount;

    public DepartmentDTO(Department department) {
        this.id = department.getId();
        this.name = department.getName();
        this.description = department.getDescription();
        this.active = department.isActive();
        this.createdAt = department.getCreatedAt();
        this.updatedAt = department.getUpdatedAt();
        
        // Parent department
        if (department.getParent() != null) {
            this.parentId = department.getParent().getId();
            this.parentName = department.getParent().getName();
        }
        
        // Calculate statistics
        if (department.getUsers() != null) {
            this.userCount = department.getUsers().size();
        } else {
            this.userCount = 0;
        }
        
        if (department.getChildren() != null) {
            this.childDepartmentCount = department.getChildren().size();
        } else {
            this.childDepartmentCount = 0;
        }
    }
}
