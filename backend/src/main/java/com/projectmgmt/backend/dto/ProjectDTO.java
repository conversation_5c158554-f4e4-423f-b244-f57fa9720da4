package com.projectmgmt.backend.dto;

import com.projectmgmt.backend.model.Project;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class ProjectDTO {
    private Long id;
    private String name;
    private String description;
    private LocalDate startDate;
    private LocalDate endDate;
    private Project.ProjectStatus status;
    private Integer progress;
    private CustomerSummaryDTO customer;
    private UserSummaryDTO manager;
    private BigDecimal budget;
    private BigDecimal cost;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    @Data
    public static class CustomerSummaryDTO {
        private Long id;
        private String name;
        private String contactPerson;
        private String email;
        private String phone;

        public CustomerSummaryDTO(Long id, String name, String contactPerson, String email, String phone) {
            this.id = id;
            this.name = name;
            this.contactPerson = contactPerson;
            this.email = email;
            this.phone = phone;
        }
    }

    @Data
    public static class UserSummaryDTO {
        private Long id;
        private String username;
        private String firstName;
        private String lastName;
        private String email;

        public UserSummaryDTO(Long id, String username, String firstName, String lastName, String email) {
            this.id = id;
            this.username = username;
            this.firstName = firstName;
            this.lastName = lastName;
            this.email = email;
        }
    }

    public ProjectDTO(Project project) {
        this.id = project.getId();
        this.name = project.getName();
        this.description = project.getDescription();
        this.startDate = project.getStartDate();
        this.endDate = project.getEndDate();
        this.status = project.getStatus();
        this.progress = project.getProgress();
        this.budget = project.getBudget();
        this.cost = project.getCost();
        this.createdAt = project.getCreatedAt();
        this.updatedAt = project.getUpdatedAt();
        
        if (project.getCustomer() != null) {
            this.customer = new CustomerSummaryDTO(
                project.getCustomer().getId(),
                project.getCustomer().getName(),
                project.getCustomer().getContactPerson(),
                project.getCustomer().getEmail(),
                project.getCustomer().getPhone()
            );
        }
        
        if (project.getManager() != null) {
            this.manager = new UserSummaryDTO(
                project.getManager().getId(),
                project.getManager().getUsername(),
                project.getManager().getFirstName(),
                project.getManager().getLastName(),
                project.getManager().getEmail()
            );
        }
    }
}
