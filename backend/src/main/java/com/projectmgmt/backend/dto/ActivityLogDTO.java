package com.projectmgmt.backend.dto;

import com.projectmgmt.backend.model.ActivityLog;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ActivityLogDTO {
    private Long id;
    private UserSummaryDTO user;
    private ActivityLog.ActivityType activityType;
    private String description;
    private String ipAddress;
    private LocalDateTime createdAt;

    @Data
    public static class UserSummaryDTO {
        private Long id;
        private String username;
        private String firstName;
        private String lastName;
        private String email;

        public UserSummaryDTO(Long id, String username, String firstName, String lastName, String email) {
            this.id = id;
            this.username = username;
            this.firstName = firstName;
            this.lastName = lastName;
            this.email = email;
        }
    }

    public ActivityLogDTO(ActivityLog activityLog) {
        this.id = activityLog.getId();
        this.activityType = activityLog.getActivityType();
        this.description = activityLog.getDescription();
        this.ipAddress = activityLog.getIpAddress();
        this.createdAt = activityLog.getCreatedAt();
        
        if (activityLog.getUser() != null) {
            this.user = new UserSummaryDTO(
                activityLog.getUser().getId(),
                activityLog.getUser().getUsername(),
                activityLog.getUser().getFirstName(),
                activityLog.getUser().getLastName(),
                activityLog.getUser().getEmail()
            );
        }
    }
}
