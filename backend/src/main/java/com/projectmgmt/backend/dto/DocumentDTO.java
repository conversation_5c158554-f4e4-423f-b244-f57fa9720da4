package com.projectmgmt.backend.dto;

import com.projectmgmt.backend.model.Document;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class DocumentDTO {
    private Long id;
    private String name;
    private String filePath;
    private String fileType;
    private String version;
    private ProjectSummaryDTO project;
    private TaskSummaryDTO task;
    private UserSummaryDTO uploadedBy;
    private LocalDateTime createdAt;

    @Data
    public static class ProjectSummaryDTO {
        private Long id;
        private String name;

        public ProjectSummaryDTO(Long id, String name) {
            this.id = id;
            this.name = name;
        }
    }

    @Data
    public static class TaskSummaryDTO {
        private Long id;
        private String name;

        public TaskSummaryDTO(Long id, String name) {
            this.id = id;
            this.name = name;
        }
    }

    @Data
    public static class UserSummaryDTO {
        private Long id;
        private String username;
        private String firstName;
        private String lastName;

        public UserSummaryDTO(Long id, String username, String firstName, String lastName) {
            this.id = id;
            this.username = username;
            this.firstName = firstName;
            this.lastName = lastName;
        }
    }

    public DocumentDTO(Document document) {
        this.id = document.getId();
        this.name = document.getName();
        this.filePath = document.getFilePath();
        this.fileType = document.getFileType();
        this.version = document.getVersion();
        this.createdAt = document.getCreatedAt();
        
        if (document.getProject() != null) {
            this.project = new ProjectSummaryDTO(
                document.getProject().getId(),
                document.getProject().getName()
            );
        }
        
        if (document.getTask() != null) {
            this.task = new TaskSummaryDTO(
                document.getTask().getId(),
                document.getTask().getName()
            );
        }
        
        if (document.getUploadedBy() != null) {
            this.uploadedBy = new UserSummaryDTO(
                document.getUploadedBy().getId(),
                document.getUploadedBy().getUsername(),
                document.getUploadedBy().getFirstName(),
                document.getUploadedBy().getLastName()
            );
        }
    }
}
