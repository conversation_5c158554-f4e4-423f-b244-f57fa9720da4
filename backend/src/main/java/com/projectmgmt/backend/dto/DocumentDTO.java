package com.projectmgmt.backend.dto;

import com.projectmgmt.backend.model.Document;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

@Data
public class DocumentDTO {
    private Long id;
    private String name;
    private String filePath;
    private String fileType;
    private String version;
    private Long size;
    private String description;
    private String url;
    private Boolean isShared;
    private Map<String, Object> project;
    private Map<String, Object> task;
    private Map<String, Object> uploadedBy;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    @Data
    public static class ProjectSummaryDTO {
        private Long id;
        private String name;

        public ProjectSummaryDTO(Long id, String name) {
            this.id = id;
            this.name = name;
        }
    }

    @Data
    public static class TaskSummaryDTO {
        private Long id;
        private String name;

        public TaskSummaryDTO(Long id, String name) {
            this.id = id;
            this.name = name;
        }
    }

    @Data
    public static class UserSummaryDTO {
        private Long id;
        private String username;
        private String firstName;
        private String lastName;

        public UserSummaryDTO(Long id, String username, String firstName, String lastName) {
            this.id = id;
            this.username = username;
            this.firstName = firstName;
            this.lastName = lastName;
        }
    }

    public DocumentDTO() {
        // Default constructor
    }

    public DocumentDTO(Document document) {
        this.id = document.getId();
        this.name = document.getName();
        this.filePath = document.getFilePath();
        this.fileType = document.getFileType();
        this.version = document.getVersion();
        this.size = document.getSize();
        this.description = document.getDescription();
        this.createdAt = document.getCreatedAt();
        this.updatedAt = document.getUpdatedAt();
        this.isShared = false; // Default value

        if (document.getProject() != null) {
            this.project = Map.of(
                "id", document.getProject().getId(),
                "name", document.getProject().getName()
            );
        }

        if (document.getTask() != null) {
            this.task = Map.of(
                "id", document.getTask().getId(),
                "name", document.getTask().getName()
            );
        }

        if (document.getUploadedBy() != null) {
            this.uploadedBy = Map.of(
                "id", document.getUploadedBy().getId(),
                "username", document.getUploadedBy().getUsername(),
                "firstName", document.getUploadedBy().getFirstName(),
                "lastName", document.getUploadedBy().getLastName()
            );
        }
    }
}
