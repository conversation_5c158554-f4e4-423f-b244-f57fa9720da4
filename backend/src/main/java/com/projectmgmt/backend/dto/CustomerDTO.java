package com.projectmgmt.backend.dto;

import com.projectmgmt.backend.model.Customer;
import com.projectmgmt.backend.model.CustomerType;
import com.projectmgmt.backend.model.Tag;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
public class CustomerDTO {
    private Long id;
    private String name;
    private String contactPerson;
    private String phone;
    private String email;
    private String address;
    private CustomerTypeDTO customerType;
    private Set<TagDTO> tags;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Statistics
    private int projectCount;
    private int contactRecordCount;

    public CustomerDTO(Customer customer) {
        this.id = customer.getId();
        this.name = customer.getName();
        this.contactPerson = customer.getContactPerson();
        this.phone = customer.getPhone();
        this.email = customer.getEmail();
        this.address = customer.getAddress();
        this.createdAt = customer.getCreatedAt();
        this.updatedAt = customer.getUpdatedAt();
        
        if (customer.getCustomerType() != null) {
            this.customerType = new CustomerTypeDTO(customer.getCustomerType());
        }
        
        if (customer.getTags() != null) {
            this.tags = customer.getTags().stream()
                    .map(TagDTO::new)
                    .collect(Collectors.toSet());
        }
        
        // Calculate statistics
        this.projectCount = customer.getProjects() != null ? customer.getProjects().size() : 0;
        this.contactRecordCount = customer.getContactRecords() != null ? customer.getContactRecords().size() : 0;
    }

    @Data
    @NoArgsConstructor
    public static class CustomerTypeDTO {
        private Long id;
        private String name;
        private String description;

        public CustomerTypeDTO(CustomerType customerType) {
            this.id = customerType.getId();
            this.name = customerType.getName();
            this.description = customerType.getDescription();
        }
    }

    @Data
    @NoArgsConstructor
    public static class TagDTO {
        private Long id;
        private String name;
        private String color;

        public TagDTO(Tag tag) {
            this.id = tag.getId();
            this.name = tag.getName();
            this.color = tag.getColor();
        }
    }
}
