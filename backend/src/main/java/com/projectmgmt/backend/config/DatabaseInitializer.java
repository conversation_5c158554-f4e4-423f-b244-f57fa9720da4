package com.projectmgmt.backend.config;

import com.projectmgmt.backend.model.ERole;
import com.projectmgmt.backend.model.Role;
import com.projectmgmt.backend.model.User;
import com.projectmgmt.backend.model.Resource;
import com.projectmgmt.backend.repository.RoleRepository;
import com.projectmgmt.backend.repository.UserRepository;
import com.projectmgmt.backend.repository.ResourceRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

@Component
public class DatabaseInitializer implements CommandLineRunner {

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ResourceRepository resourceRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        // Initialize roles if they don't exist
        initRoles();

        // Create super admin user if it doesn't exist
        createSuperAdminIfNotExists();

        // Initialize sample resources if they don't exist
        initSampleResources();
    }

    private void initRoles() {
        if (roleRepository.count() == 0) {
            Role superAdminRole = new Role(ERole.ROLE_SUPER_ADMIN);
            Role adminRole = new Role(ERole.ROLE_ADMIN);
            Role deptHeadRole = new Role(ERole.ROLE_DEPARTMENT_HEAD);
            Role employeeRole = new Role(ERole.ROLE_EMPLOYEE);
            Role guestRole = new Role(ERole.ROLE_GUEST);

            roleRepository.save(superAdminRole);
            roleRepository.save(adminRole);
            roleRepository.save(deptHeadRole);
            roleRepository.save(employeeRole);
            roleRepository.save(guestRole);
        }
    }

    private void createSuperAdminIfNotExists() {
        if (!userRepository.existsByUsername("superadmin")) {
            User superAdmin = new User(
                    "superadmin",
                    "Super",
                    "Admin",
                    "<EMAIL>",
                    passwordEncoder.encode("Admin@123")
            );

            Set<Role> roles = new HashSet<>();
            Role superAdminRole = roleRepository.findByName(ERole.ROLE_SUPER_ADMIN)
                    .orElseThrow(() -> new RuntimeException("Error: Role is not found."));
            roles.add(superAdminRole);
            superAdmin.setRoles(roles);

            userRepository.save(superAdmin);
        }
    }

    private void initSampleResources() {
        if (resourceRepository.count() == 0) {
            // 人力資源
            Resource developer1 = new Resource();
            developer1.setName("張工程師");
            developer1.setDescription("資深軟體工程師，專精Java和Spring Boot開發");
            developer1.setType(Resource.ResourceType.HUMAN);
            developer1.setAvailable(true);
            resourceRepository.save(developer1);

            Resource developer2 = new Resource();
            developer2.setName("李設計師");
            developer2.setDescription("UI/UX設計師，擅長前端設計和用戶體驗");
            developer2.setType(Resource.ResourceType.HUMAN);
            developer2.setAvailable(true);
            resourceRepository.save(developer2);

            Resource projectManager = new Resource();
            projectManager.setName("王專案經理");
            projectManager.setDescription("資深專案經理，具有10年專案管理經驗");
            projectManager.setType(Resource.ResourceType.HUMAN);
            projectManager.setAvailable(true);
            resourceRepository.save(projectManager);

            // 設備資源
            Resource laptop1 = new Resource();
            laptop1.setName("MacBook Pro 16吋");
            laptop1.setDescription("高效能筆記型電腦，適合開發工作");
            laptop1.setType(Resource.ResourceType.EQUIPMENT);
            laptop1.setAvailable(true);
            resourceRepository.save(laptop1);

            Resource laptop2 = new Resource();
            laptop2.setName("Dell XPS 15");
            laptop2.setDescription("高效能筆記型電腦，適合設計工作");
            laptop2.setType(Resource.ResourceType.EQUIPMENT);
            laptop2.setAvailable(true);
            resourceRepository.save(laptop2);

            Resource server = new Resource();
            server.setName("開發伺服器");
            server.setDescription("用於開發環境的伺服器");
            server.setType(Resource.ResourceType.EQUIPMENT);
            server.setAvailable(true);
            resourceRepository.save(server);

            // 材料資源
            Resource license1 = new Resource();
            license1.setName("IntelliJ IDEA授權");
            license1.setDescription("專業版IDE授權");
            license1.setType(Resource.ResourceType.MATERIAL);
            license1.setAvailable(true);
            resourceRepository.save(license1);

            Resource license2 = new Resource();
            license2.setName("Adobe Creative Suite授權");
            license2.setDescription("設計軟體套件授權");
            license2.setType(Resource.ResourceType.MATERIAL);
            license2.setAvailable(true);
            resourceRepository.save(license2);

            Resource cloudService = new Resource();
            cloudService.setName("AWS雲端服務");
            cloudService.setDescription("雲端運算和儲存服務");
            cloudService.setType(Resource.ResourceType.MATERIAL);
            cloudService.setAvailable(true);
            resourceRepository.save(cloudService);
        }
    }
}
