package com.projectmgmt.backend.config;

import com.projectmgmt.backend.model.ERole;
import com.projectmgmt.backend.model.Role;
import com.projectmgmt.backend.model.User;
import com.projectmgmt.backend.repository.RoleRepository;
import com.projectmgmt.backend.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

@Component
public class DatabaseInitializer implements CommandLineRunner {

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        // Initialize roles if they don't exist
        initRoles();
        
        // Create super admin user if it doesn't exist
        createSuperAdminIfNotExists();
    }

    private void initRoles() {
        if (roleRepository.count() == 0) {
            Role superAdminRole = new Role(ERole.ROLE_SUPER_ADMIN);
            Role adminRole = new Role(ERole.ROLE_ADMIN);
            Role deptHeadRole = new Role(ERole.ROLE_DEPARTMENT_HEAD);
            Role employeeRole = new Role(ERole.ROLE_EMPLOYEE);
            Role guestRole = new Role(ERole.ROLE_GUEST);

            roleRepository.save(superAdminRole);
            roleRepository.save(adminRole);
            roleRepository.save(deptHeadRole);
            roleRepository.save(employeeRole);
            roleRepository.save(guestRole);
        }
    }

    private void createSuperAdminIfNotExists() {
        if (!userRepository.existsByUsername("superadmin")) {
            User superAdmin = new User(
                    "superadmin",
                    "Super",
                    "Admin",
                    "<EMAIL>",
                    passwordEncoder.encode("Admin@123")
            );

            Set<Role> roles = new HashSet<>();
            Role superAdminRole = roleRepository.findByName(ERole.ROLE_SUPER_ADMIN)
                    .orElseThrow(() -> new RuntimeException("Error: Role is not found."));
            roles.add(superAdminRole);
            superAdmin.setRoles(roles);

            userRepository.save(superAdmin);
        }
    }
}
