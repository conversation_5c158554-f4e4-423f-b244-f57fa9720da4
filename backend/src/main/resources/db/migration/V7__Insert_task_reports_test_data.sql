-- 插入任務報告測試資料
INSERT INTO task_reports (task_id, user_id, content, progress_update, issues, created_at) VALUES
(1, 1, '今日完成了系統架構設計的初步方案，包括數據庫設計和API接口規劃。主要工作內容：1. 完成用戶管理模塊的數據表設計 2. 設計了RESTful API接口規範 3. 搭建了基礎的Spring Boot項目框架。下一步計劃：明天開始實現用戶註冊和登錄功能。', 25, '在設計過程中發現原有的權限管理方案可能過於複雜，需要簡化設計以提高開發效率。', NOW()),

(1, 1, '用戶管理模塊開發進展順利，已完成用戶註冊、登錄、密碼重置等核心功能。具體完成內容：1. 實現用戶註冊API和前端頁面 2. 完成JWT認證機制 3. 添加密碼加密和驗證功能 4. 完成用戶信息修改功能。測試結果：所有功能測試通過，性能表現良好。', 45, NULL, NOW() - INTERVAL 1 DAY),

(2, 2, '專案管理模塊的基礎功能已經完成，包括專案創建、編輯、刪除和查詢功能。主要成果：1. 完成專案CRUD操作的後端API 2. 實現專案列表和詳情頁面 3. 添加專案狀態管理功能 4. 完成專案成員管理功能。目前進度符合預期，質量良好。', 60, '專案成員權限管理邏輯較為複雜，需要更多時間進行測試和優化。', NOW() - INTERVAL 2 DAY),

(3, 1, '任務管理功能開發完成度較高，核心功能已實現。完成內容：1. 任務創建、分配、更新功能 2. 任務狀態流轉管理 3. 任務優先級和截止日期管理 4. 子任務功能實現。當前階段主要專注於功能完善和用戶體驗優化。', 70, '任務依賴關係的處理邏輯需要進一步優化，目前的實現可能會導致循環依賴問題。', NOW() - INTERVAL 3 DAY),

(4, 2, '客戶管理模塊開發進展良好，基本功能已經實現。主要工作：1. 完成客戶信息管理功能 2. 實現客戶分類和標籤系統 3. 添加客戶聯繫記錄功能 4. 完成客戶統計報表功能。系統穩定性和性能都達到了預期要求。', 80, NULL, NOW() - INTERVAL 4 DAY),

(1, 1, '系統集成測試階段，各模塊功能運行穩定。測試內容：1. 用戶權限系統集成測試 2. 數據一致性驗證 3. API接口壓力測試 4. 前端用戶體驗測試。測試結果顯示系統整體性能良好，用戶反饋積極。準備進入部署階段。', 90, '在高並發情況下，數據庫查詢性能有待優化，需要添加適當的索引和查詢優化。', NOW() - INTERVAL 5 DAY),

(2, 2, '專案管理模塊優化完成，添加了多項新功能。新增功能：1. 專案進度可視化圖表 2. 專案風險評估功能 3. 專案資源分配管理 4. 專案報告自動生成。用戶測試反饋良好，功能實用性強。', 85, '圖表渲染在大數據量時存在性能問題，需要實現數據分頁或懶加載機制。', NOW() - INTERVAL 6 DAY),

(3, 1, '任務報告功能開發完成，支持豐富的報告內容和統計分析。實現功能：1. 任務執行報告創建和編輯 2. 報告模板系統 3. 報告數據統計和分析 4. 報告導出功能。該功能大大提升了項目管理的透明度和效率。', 95, NULL, NOW() - INTERVAL 7 DAY),

(4, 2, '客戶關係管理功能全面完成，系統功能豐富且易用。最終成果：1. 完整的客戶生命週期管理 2. 客戶溝通記錄追蹤 3. 客戶滿意度調查系統 4. 客戶價值分析報告。項目達到了預期目標，準備交付使用。', 100, NULL, NOW() - INTERVAL 8 DAY),

(5, 1, '資源管理模塊開發啟動，完成了基礎架構設計。初期工作：1. 資源分類體系設計 2. 資源分配算法研究 3. 數據模型設計 4. 基礎API接口規劃。為後續開發奠定了良好基礎。', 15, '資源分配算法的複雜度較高，需要更多時間進行算法優化和測試。', NOW() - INTERVAL 9 DAY);
