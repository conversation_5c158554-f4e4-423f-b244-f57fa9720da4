-- 添加專案成員數據
-- 為現有專案添加成員

-- 專案1的成員 (假設專案ID為1)
INSERT INTO project_members (project_id, user_id) VALUES
(1, 1), -- superadmin 作為成員
(1, 2), -- admin 作為成員
(1, 3); -- superadmin2 作為成員

-- 專案2的成員 (假設專案ID為2)
INSERT INTO project_members (project_id, user_id) VALUES
(2, 1), -- superadmin 作為成員
(2, 4); -- superadmin3 作為成員

-- 專案3的成員 (假設專案ID為3)
INSERT INTO project_members (project_id, user_id) VALUES
(3, 2), -- admin 作為成員
(3, 3), -- superadmin2 作為成員
(3, 4); -- superadmin3 作為成員

-- 專案4的成員 (假設專案ID為4)
INSERT INTO project_members (project_id, user_id) VALUES
(4, 1), -- superadmin 作為成員
(4, 2), -- admin 作為成員
(4, 3), -- superadmin2 作為成員
(4, 4); -- superadmin3 作為成員
