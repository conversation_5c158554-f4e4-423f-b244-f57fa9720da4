# 系統通用
system.name=專案管理系統
system.welcome=歡迎使用專案管理系統
system.error=系統錯誤
system.success=操作成功
system.failed=操作失敗

# 使用者與權限
user.login=登入
user.logout=登出
user.register=註冊
user.password=密碼
user.username=使用者名稱
user.email=電子郵件
user.role=角色
user.status=狀態
user.created=使用者已建立
user.updated=使用者已更新
user.deleted=使用者已刪除
user.enabled=已啟用
user.disabled=已停用
user.password.reset=密碼重設
user.not.found=找不到使用者
user.exists=使用者已存在
user.invalid.credentials=無效的憑證

# 角色
role.admin=最高管理員
role.manager=管理員
role.department.head=部門主管
role.employee=員工
role.guest=訪客

# 部門
department.name=部門名稱
department.created=部門已建立
department.updated=部門已更新
department.deleted=部門已刪除
department.not.found=找不到部門
department.exists=部門已存在

# 客戶
customer.name=客戶名稱
customer.contact=聯絡人
customer.phone=電話
customer.email=電子郵件
customer.address=地址
customer.type=客戶類型
customer.created=客戶已建立
customer.updated=客戶已更新
customer.deleted=客戶已刪除
customer.not.found=找不到客戶

# 專案
project.name=專案名稱
project.description=專案描述
project.start.date=開始日期
project.end.date=結束日期
project.status=專案狀態
project.progress=進度
project.manager=專案負責人
project.members=專案成員
project.budget=預算
project.cost=成本
project.created=專案已建立
project.updated=專案已更新
project.deleted=專案已刪除
project.not.found=找不到專案

# 專案狀態
project.status.planning=規劃中
project.status.in.progress=執行中
project.status.completed=已完成
project.status.delayed=延期
project.status.closed=結案

# 任務
task.name=任務名稱
task.description=任務描述
task.start.date=開始日期
task.end.date=截止日期
task.status=任務狀態
task.priority=優先度
task.assignee=負責人
task.progress=進度
task.created=任務已建立
task.updated=任務已更新
task.deleted=任務已刪除
task.not.found=找不到任務

# 任務狀態
task.status.not.started=未開始
task.status.in.progress=進行中
task.status.completed=已完成
task.status.delayed=延期

# 任務優先度
task.priority.low=低
task.priority.medium=中
task.priority.high=高
task.priority.urgent=緊急

# 資源
resource.name=資源名稱
resource.type=資源類型
resource.availability=可用性
resource.created=資源已建立
resource.updated=資源已更新
resource.deleted=資源已刪除

# 文件
document.name=文件名稱
document.type=文件類型
document.version=版本
document.uploaded=文件已上傳
document.deleted=文件已刪除

# 通知
notification.title=通知
notification.message=訊息
notification.read=已讀
notification.unread=未讀
