#!/bin/bash

# 修復 API 端點，為所有缺少 api/ 前綴的端點添加前綴

API_FILE="/Users/<USER>/jsbvmx/frontend/frontend/src/services/api.ts"

# 備份原文件
cp "$API_FILE" "$API_FILE.backup2"

# 修復所有缺少 api/ 前綴的端點
# 注意：只修復那些不是以 api/ 開頭的端點

# Customer endpoints
sed -i '' "s|return apiClient\.get('customers|return apiClient.get('api/customers|g" "$API_FILE"
sed -i '' "s|return apiClient\.get(\`customers|return apiClient.get(\`api/customers|g" "$API_FILE"
sed -i '' "s|return apiClient\.put(\`customers|return apiClient.put(\`api/customers|g" "$API_FILE"
sed -i '' "s|return apiClient\.delete(\`customers|return apiClient.delete(\`api/customers|g" "$API_FILE"

# Customer Type endpoints
sed -i '' "s|return apiClient\.get('customer-types|return apiClient.get('api/customer-types|g" "$API_FILE"
sed -i '' "s|return apiClient\.get(\`customer-types|return apiClient.get(\`api/customer-types|g" "$API_FILE"
sed -i '' "s|return apiClient\.post('customer-types|return apiClient.post('api/customer-types|g" "$API_FILE"
sed -i '' "s|return apiClient\.put(\`customer-types|return apiClient.put(\`api/customer-types|g" "$API_FILE"
sed -i '' "s|return apiClient\.delete(\`customer-types|return apiClient.delete(\`api/customer-types|g" "$API_FILE"

# Project endpoints (除了已經有 api/ 前綴的)
sed -i '' "s|return apiClient\.get(\`projects|return apiClient.get(\`api/projects|g" "$API_FILE"
sed -i '' "s|return apiClient\.put(\`projects|return apiClient.put(\`api/projects|g" "$API_FILE"
sed -i '' "s|return apiClient\.post(\`projects|return apiClient.post(\`api/projects|g" "$API_FILE"
sed -i '' "s|return apiClient\.delete(\`projects|return apiClient.delete(\`api/projects|g" "$API_FILE"

# Resource endpoints (除了已經有 api/ 前綴的)
sed -i '' "s|return apiClient\.get(\`resources|return apiClient.get(\`api/resources|g" "$API_FILE"
sed -i '' "s|return apiClient\.put(\`resources|return apiClient.put(\`api/resources|g" "$API_FILE"
sed -i '' "s|return apiClient\.delete(\`resources|return apiClient.delete(\`api/resources|g" "$API_FILE"

# Task endpoints (除了已經有 api/ 前綴的)
sed -i '' "s|return apiClient\.get(\`tasks|return apiClient.get(\`api/tasks|g" "$API_FILE"
sed -i '' "s|return apiClient\.put(\`tasks|return apiClient.put(\`api/tasks|g" "$API_FILE"
sed -i '' "s|return apiClient\.post(\`tasks|return apiClient.post(\`api/tasks|g" "$API_FILE"
sed -i '' "s|return apiClient\.delete(\`tasks|return apiClient.delete(\`api/tasks|g" "$API_FILE"

# Dashboard endpoints
sed -i '' "s|return apiClient\.get('dashboard|return apiClient.get('api/dashboard|g" "$API_FILE"

# Notification endpoints (除了已經有 api/ 前綴的)
sed -i '' "s|return apiClient\.put(\`notifications|return apiClient.put(\`api/notifications|g" "$API_FILE"
sed -i '' "s|return apiClient\.delete(\`notifications|return apiClient.delete(\`api/notifications|g" "$API_FILE"

# Task Report endpoints
sed -i '' "s|return apiClient\.get('task-reports|return apiClient.get('api/task-reports|g" "$API_FILE"
sed -i '' "s|return apiClient\.get(\`task-reports|return apiClient.get(\`api/task-reports|g" "$API_FILE"
sed -i '' "s|return apiClient\.post('task-reports|return apiClient.post('api/task-reports|g" "$API_FILE"
sed -i '' "s|return apiClient\.put(\`task-reports|return apiClient.put(\`api/task-reports|g" "$API_FILE"
sed -i '' "s|return apiClient\.delete(\`task-reports|return apiClient.delete(\`api/task-reports|g" "$API_FILE"

# Resource Allocation endpoints
sed -i '' "s|return apiClient\.get('resource-allocations|return apiClient.get('api/resource-allocations|g" "$API_FILE"
sed -i '' "s|return apiClient\.get(\`resource-allocations|return apiClient.get(\`api/resource-allocations|g" "$API_FILE"
sed -i '' "s|return apiClient\.post('resource-allocations|return apiClient.post('api/resource-allocations|g" "$API_FILE"
sed -i '' "s|return apiClient\.put(\`resource-allocations|return apiClient.put(\`api/resource-allocations|g" "$API_FILE"
sed -i '' "s|return apiClient\.delete(\`resource-allocations|return apiClient.delete(\`api/resource-allocations|g" "$API_FILE"

# Resource Availability endpoints
sed -i '' "s|return apiClient\.get('resource-availability|return apiClient.get('api/resource-availability|g" "$API_FILE"
sed -i '' "s|return apiClient\.get(\`resource-availability|return apiClient.get(\`api/resource-availability|g" "$API_FILE"
sed -i '' "s|return apiClient\.post('resource-availability|return apiClient.post('api/resource-availability|g" "$API_FILE"

# Contact Record endpoints
sed -i '' "s|return apiClient\.get('contact-records|return apiClient.get('api/contact-records|g" "$API_FILE"
sed -i '' "s|return apiClient\.get(\`contact-records|return apiClient.get(\`api/contact-records|g" "$API_FILE"
sed -i '' "s|return apiClient\.post('contact-records|return apiClient.post('api/contact-records|g" "$API_FILE"
sed -i '' "s|return apiClient\.put(\`contact-records|return apiClient.put(\`api/contact-records|g" "$API_FILE"
sed -i '' "s|return apiClient\.delete(\`contact-records|return apiClient.delete(\`api/contact-records|g" "$API_FILE"

# Tag endpoints
sed -i '' "s|return apiClient\.get('tags|return apiClient.get('api/tags|g" "$API_FILE"
sed -i '' "s|return apiClient\.get(\`tags|return apiClient.get(\`api/tags|g" "$API_FILE"
sed -i '' "s|return apiClient\.post('tags|return apiClient.post('api/tags|g" "$API_FILE"
sed -i '' "s|return apiClient\.put(\`tags|return apiClient.put(\`api/tags|g" "$API_FILE"
sed -i '' "s|return apiClient\.delete(\`tags|return apiClient.delete(\`api/tags|g" "$API_FILE"

# Activity Log endpoints
sed -i '' "s|return apiClient\.get('activity-logs|return apiClient.get('api/activity-logs|g" "$API_FILE"
sed -i '' "s|return apiClient\.get(\`activity-logs|return apiClient.get(\`api/activity-logs|g" "$API_FILE"

# Settings endpoints (除了已經有 api/ 前綴的)
sed -i '' "s|return apiClient\.get(\`settings|return apiClient.get(\`api/settings|g" "$API_FILE"
sed -i '' "s|return apiClient\.put(\`settings|return apiClient.put(\`api/settings|g" "$API_FILE"

echo "API 端點修復完成！"
